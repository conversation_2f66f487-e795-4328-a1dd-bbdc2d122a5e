#!/usr/bin/env janet

(import ./util :as u)

(defn conventional-commit? [msg]
  (peg/match
   '(choice
     "Merge"
     "RELEASE"
     (sequence (choice "I" "i") "nitial commit")
     (sequence
      (choice "feat" "feature" "fix" "perf" "revert" "docs" "changelog" "style" "chore" "refactor" "test" "build" "ci")
      (opt (sequence "(" (some (choice (range "az" "AZ" "09") "-")) ")"))
      (opt "!")
      ": "))
    msg))

(when (u/master?)
  (let [commit-msg (-> (dyn :args) (get 1) (u/read-line) (string/trimr))]
    (unless (conventional-commit? commit-msg)
      (print (string "\e[31m'" commit-msg "' is not a conventional commit message.\e[0m"))
      (os/exit 1))))
