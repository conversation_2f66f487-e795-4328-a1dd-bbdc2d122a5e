# ci-submodule

To add this to a new repository:
```bash
# To configure the repository to use the submodule
git submodule add -b master ../ci-submodule .ci

# To pull the submodule locally in a repository that's already using it
git submodule update --init --recursive 

# To clone the repository that's already using the submodule
git clone --recurse-submodules
```

To use provided git hooks:
```bash
brew install janet #once

# To point your local git config to the submodule's hooks
git config --local core.hooksPath .ci/.githooks/ #once per repository
```

