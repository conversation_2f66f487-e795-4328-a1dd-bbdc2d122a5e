name: changelog-progress
description: "Update the unreleased changelog based on historic commit messages"
runs:
  using: "composite"
  steps:
    - uses: ./.ci/janet-setup
    - run: janet ./.ci/src/changelogs.janet > CHANGELOG.md
      shell: bash
    - run: |
        git config user.name github-actions[bot]
        git config user.email 41898282+github-actions[bot]@users.noreply.github.com
        git add CHANGELOG.md
        git commit -m "changelog: update changelog" && git push || true
      shell: bash
