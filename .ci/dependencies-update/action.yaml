name: dependencies-update
description: "Read deps-output.txt and create PR with updated dependencies"
runs:
  using: "composite"
  steps:
    - name: create PR
      id: cpr
      uses: peter-evans/create-pull-request@v6
      with:
        token: ${{ env.GITHUB_TOKEN }}
        title: "chore(dependencies): Update dependencies"
        body-path: deps-output.txt
        commit-message: "chore(dependencies): Update dependencies"
        branch: update-dependencies
        author: github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
        labels: dependencies
    - run: |
        echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
        echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
      shell: bash
