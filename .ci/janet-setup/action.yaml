name: janet-setup
description: "Download janet and add to path"
runs:
  using: "composite"
  steps:
    - run: echo "tag=$(gh api repos/{owner}/{repo}/releases | jq 'map(select(.assets | .[] | .name | test("-linux-x64.tar.gz$")))[0].tag_name')" >> "$GITHUB_OUTPUT"
      shell: bash
      env:
        GH_REPO: janet-lang/janet
      id: janet-ver
    - run: curl -LO https://github.com/janet-lang/janet/releases/download/${{ steps.janet-ver.outputs.tag }}/janet-${{ steps.janet-ver.outputs.tag }}-linux-x64.tar.gz
      shell: bash
    - run: tar -xvzf janet-${{ steps.janet-ver.outputs.tag }}-linux-x64.tar.gz
      shell: bash
    - run: echo "janet-${{ steps.janet-ver.outputs.tag }}-linux/bin" >> $GITHUB_PATH
      shell: bash
