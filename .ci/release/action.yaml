name: release
description: "Update the changelog and cut a new release based on historic commit messages"
runs:
  using: "composite"
  steps:
    - uses: ./.ci/janet-setup
    - name: get old tag
      run: echo "value=$(git describe --abbrev=0 --tags)" >> $GITHUB_OUTPUT
      shell: bash
      id: old-tag
    - run: echo "value=$(janet ./.ci/src/tags.janet)" >> $GITHUB_OUTPUT
      shell: bash
      id: new-tag
    - shell: bash
      run: git tag ${{ steps.new-tag.outputs.value }} $(git log --grep '\[skip actions\]' --invert-grep -1 --pretty=format:%H)
    - run: |
        janet ./.ci/src/changelogs.janet > CHANGELOG.md
        janet ./.ci/src/changelogs.janet ${{ steps.old-tag.outputs.value }} ${{ steps.new-tag.outputs.value }} > .ci/RELEASE.md
      shell: bash
    - run: |
        git config user.name github-actions
        git config user.email <EMAIL>
        git add CHANGELOG.md
        git commit -m "changelog: update changelog for ${{ steps.new-tag.outputs.value }} [skip actions]"
      shell: bash
    - run: git push --tags && git push && gh release create ${{ steps.new-tag.outputs.value }} -F .ci/RELEASE.md
      shell: bash
