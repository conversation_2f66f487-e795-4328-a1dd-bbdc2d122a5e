(import ./util :as u)

(def repo-url (string "https://github.com/" (os/getenv "GITHUB_REPOSITORY")))

(def section-titles [[:breaking "⚠ BREAKING CHANGES"]
                     ["feat" "Features"]
                     ["fix" "Bug Fixes"]
                     ["perf" "Performance Improvements"]
                     ["refactor" "Other Changes"]
                     ["revert" "Reverts"]
                     ["docs" "Documentation"]])

(defn ->changelog-line [scope message hash]
  (let [b (buffer)]
    (buffer/push-string
      b "* "
      (if (or (nil? scope) (empty? scope))
        ""
        (string "**" (string/slice scope 1 (dec (length scope))) ":** "))
      message
      " ([" (string/slice hash 0 7) "](" repo-url "/commit/" hash "))\n")))

(defn ->sections [commits]
  (reduce
    (fn [acc [hash type scope breaking message]]
      (let [line (->changelog-line scope message hash)
            concat-line (fn [b] (buffer/push (or b (buffer)) line))
            acc (update acc type concat-line)]
        (if (= "!" breaking)
          (update acc :breaking concat-line)
          acc)))
    @{}
    commits))

(defn add-markdown-line [b line]
  (buffer/push-string b " - " line "\n"))

(defn markdown-section [sections b [section title]]
  (let [lines (get sections section)]
    (if (or (nil? lines) (empty? lines))
      b
      (-> (buffer/push-string b "\n### " title "\n")
          (buffer/push lines)))))

(defn ->markdown-body [b sections]
  (reduce (fn [b st] (markdown-section sections b st)) b section-titles))

(defn capture-changelog-commit [s]
  (when-let [c (u/conventional-commit-capture s)]
    (unless (string/has-suffix? "[no docs]" (last c))
       c)))

(defn generate-tag-changelog [from to from-date]
  (let [to (or to "HEAD")
        commits (u/fetch-commits from to)
        sections (if commits
                   (->> commits
                        (keep capture-changelog-commit)
                        (->sections))
                   @{})
        head? (= to "HEAD")
        tag-title (if head? "Unreleased" to)]
    (-> (buffer)
        (buffer/push-string
          "## " (if from "[" "") tag-title
          (if from (string "](" repo-url "/compare/" from "..." (if head? (u/read-exec "git" "log" "-1" "--format=%H") to) ")") "")
          (if from-date (string " - " from-date) "") "\n")
        (->markdown-body sections)
        (buffer/push-string "\n"))))

(defn capture-release-tag [s]
  (peg/match
    '(sequence
       (capture (sequence (some (range "09")) "." (some (range "09")) "." (some (range "09"))
                          (any (sequence "-" (some (range "-z"))))) :tag)
       " "
       (capture (some (range "09" "--")) :date))
    s))

(defn generate-changelog []
  (let [release-tags (-?>> (u/read-exec "git" "tag" "--list" "--sort=-v:refname" "--format=%(refname:short) %(creatordate:short)")
                           (string/split "\n")
                           (keep capture-release-tag))
        b (buffer)]
    (if (or (nil? release-tags) (empty? release-tags))
      (let [{:year year :month month :month-day day} (os/date)]
        (buffer/push b (generate-tag-changelog nil nil (string/format "%d-%02d-%02d" year month day))))
      (->> (u/read-exec "git" "log" "--max-parents=0" "--format=%H %cs")
           (string/split " ")
           (array/push release-tags)
           (reduce (fn [[to to-date] [from from-date]]
                     (buffer/push b (generate-tag-changelog from to to-date))
                     [from from-date]) ["HEAD" nil])))
    b))

(defn main [& args]
  (let [[_ previous current] args
        b (if (and previous current) (generate-tag-changelog previous current nil) (generate-changelog))]
    (print b)))
