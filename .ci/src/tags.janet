(import ./util :as u)

(def commit-type-precedence
  (reduce (fn [acc [precedence types]]
            (reduce (fn [acc type]
                      (put acc type precedence))
                    acc types))
          (table)
          (pairs @{1 ["feat" "feature"]
                   2 ["fix" "docs" "style" "chore" "build" "refactor" "perf"]})))

(defn derive-commit-precedence [[_ type _ breaking]]
  (if (= "!" breaking)
    0
    (get commit-type-precedence type)))

(defn derive-release-tag [current-tag]
  (if current-tag
    (let [precedence (->> (u/fetch-commits current-tag "HEAD")
                          (keep u/conventional-commit-capture)
                          (keep derive-commit-precedence)
                          (reduce min 2))
          tag-sections (->> (string/split "." current-tag)
                            (map int/s64))]
      (as-> (range 3) $
            (map (fn [i]
                   (cond
                     (= i precedence) (inc (get tag-sections i))
                     (> i precedence) 0
                     (< i precedence) (get tag-sections i))) $)
            (map string $)
            (string/join $ ".")))
    "1.0.0"))

(defn new-tag []
  (let [current-tag (u/read-exec "git" "describe" "--tags" "--abbrev=0")]
    (derive-release-tag current-tag)))

(defn main [& _]
  (print (new-tag)))
