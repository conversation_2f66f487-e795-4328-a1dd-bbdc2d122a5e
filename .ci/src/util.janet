(defn read-exec [& args]
  (let [task (os/spawn args :p {:in :pipe :out :pipe})]
    (-?> (:read (task :out) :all) (string/trimr))))

(defn fetch-commits [from to]
  (or (-?>> (if (and from to) (string from "..." to) "HEAD")
            (read-exec "git" "log" "--pretty=format:%H %s")
            (string/split "\n"))
      []))

(def conventional-commit-matcher
  (peg/compile
    '(sequence
       (capture (repeat 40 :h) :hash)
       " "
       (choice
         "Merge"
         "RELEASE"
         (sequence (choice "I" "i") "nitial commit")
         (sequence
           (capture (choice "feat" "feature" "fix" "perf" "revert" "docs" "changelog" "style" "chore" "refactor" "test" "build" "ci") :type)
           (capture (opt (sequence "(" (some (choice (range "az" "AZ" "09") "-")) ")")) :scope)
           (capture (opt "!") :breaking?)
           ": "
           (capture (some (range " ~")) :message))))))

(defn conventional-commit-capture [msg]
  (let [match (peg/match conventional-commit-matcher msg)]
    (when (get match 1) match)))
