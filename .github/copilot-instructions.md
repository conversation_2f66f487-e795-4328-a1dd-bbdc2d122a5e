# Pythia - ML-Powered Anomaly Detection & Forecasting Service

## Project Architecture

**Pythia** is a FastAPI-based microservice for log anomaly detection and time series forecasting using Prophet models. The system features a distributed job scheduler, multiple ML pipelines, and support for multi-tenant environments.

### Core Components

1. **FastAPI Service** (`src/main.py`) - REST API with three main routers:
   - `/tenants/{tenant_id}/anomaly-detectors/{ad_id}` - Anomaly detection management
   - `/tenants/{tenant_id}/prophecies/{prophecy_id}` - Time series forecasting
   - `/sks` - SKS-specific endpoints (environment-conditional)

2. **Job Scheduler** (`src/job/scheduler/`) - Background task execution:
   - `JobScheduler` orchestrates async job processing with capacity management
   - `JobStore` manages PostgreSQL job persistence and state transitions
   - Supports both `ThreadPoolJobExecutor` and `ProcessPoolJobExecutor`
   - Jobs have types: `log-anomaly`, `prophecy`, `sks-mcs-nss`

3. **ML Pipelines**:
   - **Anomaly Detection** (`src/anomaly_detector/`) - LogBERT-based log analysis with drain3 parsing
   - **Prophecy** (`src/prophecy/`) - Prophet time series forecasting
   - **SKS** (`src/sks/`) - Site-specific monitoring and QoE analysis

### Key Architectural Patterns

- **Service Discovery**: Uses Consul for dynamic service resolution (`src/services.py`)
- **Dependency Injection**: FastAPI dependencies for database connections, clients (`src/dependencies.py`)
- **Multi-tenant**: All APIs are tenant-scoped with tenant-specific data isolation
- **S3 Integration**: Dual S3 support (AWS + hosted) for model artifacts and data storage
- **Microservice Communication**: Integrates with Loki, Mimir, Greptime, Qdrant, LLM services

## Development Workflows

### Local Development Setup
```bash
poetry install
poetry shell
```

### CLI Training Commands
The service provides CLI interfaces via `python3 src <command>`:

**Prophet Forecasting:**
```bash
# Train model
python3 src prophecy train --tenant-id <id> --prophecy-id <id> --mimir-query <query> --start <timestamp> --end <timestamp>

# Predict
python3 src prophecy predict --prophecy-id <id> --start <timestamp> --end <timestamp>
```

**Anomaly Detection:**
```bash
# Train
python3 src log-anomaly train --tenant-id <id> --flow-id <id> --anomaly-detector-id <id> --metric-name <name> --start <timestamp> --end <timestamp>

# Inference
python3 src log-anomaly predict --tenant-id <id> --flow-id <id> --anomaly-detector-id <id> --trained-date <timestamp> --start <timestamp> --end <timestamp>
```

### Running the Service
```bash
# Start FastAPI server
python -m uvicorn src.main:app --reload

# Environment controls
USE_THREAD_EXECUTOR=1  # Use thread pool instead of process pool
```

## Code Patterns & Conventions

### Job Implementation Pattern
All background jobs inherit from `BaseJob` and follow this structure:
```python
class MyJob(BaseJob):
    def maximum_duration(self) -> Duration: ...
    async def prepare_args(self) -> tuple | dict: ...
    def run(self) -> Callable: ...  # Returns function to execute
    async def on_success/on_failed/on_done(self): ...
```

### Configuration Management
- **Settings**: Pydantic settings with environment variable binding (`src/config.py`)
- **Service URLs**: Dynamic resolution via `ServicesRegistry` and Consul
- **Multi-environment**: `settings.is_sks_env` controls SKS-specific features

### Database Patterns
- **AsyncPG**: All database operations use async connection pools
- **PyPika**: SQL query building with `Q.from_(table).select(...).where(...)`
- **Transactions**: Use `db_txn` dependency for transactional operations
- **Job State**: Jobs stored in `jobs` table with JSON config column

### ML Pipeline Patterns
- **S3PathConfig**: Centralized path management for model artifacts
- **Training/Inference Split**: Separate job types with shared base classes
- **Preprocessing**: Arrow/Parquet data pipeline with chunked processing
- **Feature Engineering**: Drain3 for log parsing, HDBSCAN for clustering

### Error Handling
- **RecoverableError**: Retryable failures (network, temp issues)
- **ServiceError**: Service-level failures requiring job retry with cache invalidation
- **Accounting**: Track resource usage and timing via `AccountingCollector`

## Integration Points

### External Services (via ServiceRegistry)
- **Loki**: Log ingestion and querying
- **Mimir**: Prometheus-compatible metrics storage
- **Greptime**: Time series database for both metrics and logs
- **Qdrant**: Vector database for semantic search
- **S3/Garage**: Object storage for model artifacts
- **LLM Services**: OpenAI, Gemini, Groq for contextual analysis

### Data Flow
1. **Logs** → Loki/Greptime → **Preprocessing** → Parquet → **Training**
2. **Metrics** → Mimir/Greptime → **Prophet Training** → **Forecasting**
3. **Models** → S3 → **Inference Jobs** → **Anomaly Scores** → Greptime

### Key Files for Understanding
- `src/main.py` - Service bootstrap and router registration
- `src/job/scheduler/scheduler.py` - Core job execution logic
- `src/anomaly_detector/job/train.py` - ML training pipeline
- `src/prophecy/job/prophecy.py` - Time series forecasting
- `src/dependencies.py` - Dependency injection patterns
- `src/config.py` - Configuration and service definitions

### Development Tips
- Job execution can be debugged by setting `USE_THREAD_EXECUTOR=1`
- Check `accounting_collector.save()` for resource usage tracking
- Use `path_config` for consistent S3 path management
- Service URLs are resolved dynamically - check `ServicesRegistry` for debugging
- SKS environment features are controlled by `settings.is_sks_env`
