name: Build and publish image

on:
  workflow_dispatch:
    inputs:
      with_cuda:
        description: "Enable CUDA build"
        type: boolean
        default: false
        required: false

  pull_request:
    branches:
      - master
  push:
    branches:
      - master
    tags:
      - "*"

env:
  REPOSITORY: core0
  PROJECT_NAME: ${{ github.event.repository.name }}
  GRAAL_OPTIMISATION: b

jobs:
  build-and-push-image:
    if: github.ref_type == 'tag' || !startsWith(github.event.head_commit.message, 'changelog')
    runs-on: self-hosted
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Add CUDA variable
        if: ${{ github.event.inputs.with_cuda == 'true' }}
        run: echo "WITH_CUDA=true" >> $GITHUB_ENV

      - run: echo ${{ env.GITHUB_REF_NAME || github.sha }} > version.txt

      - run: echo "${HOME}/.local/bin" >> $GITHUB_PATH

      - name: Fetch GeoLite City DB
        run: wget https://github.com/P3TERX/GeoLite.mmdb/raw/download/GeoLite2-City.mmdb --output-document=./resources/GeoLite2-City.mmdb

      - name: Prepare tiktoken cache
        run: |
          mkdir -p ./tiktoken_cache
          CL100K_TIKTOKEN_URL="https://openaipublic.blob.core.windows.net/encodings/cl100k_base.tiktoken"
          O200K_TIKTOKEN_URL="https://openaipublic.blob.core.windows.net/encodings/o200k_base.tiktoken"

          wget -O ./tiktoken_cache/$(echo -n $CL100K_TIKTOKEN_URL | sha1sum | head -c 40) $CL100K_TIKTOKEN_URL
          wget -O ./tiktoken_cache/$(echo -n $O200K_TIKTOKEN_URL | sha1sum | head -c 40) $O200K_TIKTOKEN_URL

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64

      - name: Login to Harbor Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.HARBOR_REGISTRY }}
          username: ${{ secrets.HARBOR_REGISTRY_USERNAME }}
          password: ${{ secrets.HARBOR_REGISTRY_PASSWORD }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ vars.HARBOR_REGISTRY }}/${{ env.REPOSITORY }}/${{ env.PROJECT_NAME }}
          tags: |
            type=ref,event=tag
            type=ref,event=pr
            type=raw,value=latest,enable=${{ github.ref_type != 'tag' && github.ref == 'refs/heads/master' && !inputs.with_cuda }}
          flavor: |
            latest=false
            suffix=${{ inputs.with_cuda && '-gpu' || '' }}

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ inputs.with_cuda && 'Dockerfile_cuda' || 'Dockerfile' }}
          platforms: linux/amd64
          push: true
          pull: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            PROJECT_NAME=${{ env.PROJECT_NAME }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
