name: update-changelog
on:
  schedule:
    - cron: '0 0 * * 0'
  workflow_dispatch:

jobs:
  changelog-progress:
    name: changelog-progress
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.SUBMODULE_PAT }}
          fetch-depth: 0
          submodules: true
      - id: latest-commit
        run: echo "message=$(git log -1 --pretty=format:%s -E --grep '^((:?fix|perf|revert|docs|changelog|feat|refactor)(:?\(.+\))?\!?:)|(:?\!:)')" >> $GITHUB_OUTPUT
      - if: "!startsWith(steps.latest-commit.outputs.message, 'changelog:')"
        uses: ./.ci/changelog-progress
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
