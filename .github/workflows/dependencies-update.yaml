name: create-dependencies-pr
on:
  workflow_dispatch:

jobs:
  open-pr:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.SUBMODULE_PAT }}
          submodules: true
      - uses: DeLaGuardo/setup-clojure@13.1
        with:
          cli: latest
          github-token: ${{ secrets.GITHUB_TOKEN }}
      - run: echo '```json' > deps-output.txt
      - run: clojure -M:outdated  --upgrade --force --skip=pom --skip=github-action --reporter=json | head -n 1 | jq >> deps-output.txt
      - run: echo '```' >> deps-output.txt
      - uses: ./.ci/dependencies-update
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
