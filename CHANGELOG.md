## [Unreleased](https://github.com/core0-io/pythia/compare/1.18.2...f3a68fa36644ac4ee51715bbd0e15914fb7932f3)

### Features
* add health check ([e77bf3d](https://github.com/core0-io/pythia/commit/e77bf3d3cc908718fa0fdd86b4c96067f70ed4f2))
* support openai embedding ([787fceb](https://github.com/core0-io/pythia/commit/787fceb94432b0bce822ea60b5dc3d0679fbc6ef))
* add qwen-turbo model ([4131ddc](https://github.com/core0-io/pythia/commit/4131ddc99a0ed080383315fe69a3013de9557b0e))
* **sks:** add name lang for metrics ([5190c21](https://github.com/core0-io/pythia/commit/5190c215ac26062cd40f46169f04f14faca1371a))
* sks mcs nss qoe ([18393ad](https://github.com/core0-io/pythia/commit/18393ad7e95f1012c35f342092a0ce66b1683543))
* **sks:** mcs and nss qoe ([f9073e6](https://github.com/core0-io/pythia/commit/f9073e672a5247c1ae9cb6bafeb9519922d88c31))
* support query multiple markers and prophecies ([f5a7b38](https://github.com/core0-io/pythia/commit/f5a7b387ba942b809f93b1a0bc495188760d594f))
* add lang to marker description, add raw markers to sub marker ([d7c0a77](https://github.com/core0-io/pythia/commit/d7c0a773fe9c26e845662abe7ec7052672a5a098))
* add metrics advisories ([a9f673a](https://github.com/core0-io/pythia/commit/a9f673a52fc0a4b659fb50d1c70d76ee1784e0ed))
* add sks sites api ([95a4bd1](https://github.com/core0-io/pythia/commit/95a4bd1d4855750bf65fb29187aa929fcc42d399))
* add api endpoint ([d1af082](https://github.com/core0-io/pythia/commit/d1af0823f7c5898e9cda83be490bb6d500bbbd7a))
* ingest correlation rising edges to greptime via influxdb protocol ([bc85dae](https://github.com/core0-io/pythia/commit/bc85dae611a993b0c6d6c6de2637f70d4f127cc8))
* correlation ([af75743](https://github.com/core0-io/pythia/commit/af757431382787689a0d43a77c2e55de6a89bb7a))
* logbert compatible with skks ([9d2d715](https://github.com/core0-io/pythia/commit/9d2d71582d58d7fd5f91dbcd129179dadf293eed))
* support excluded fields ([c48aef0](https://github.com/core0-io/pythia/commit/c48aef043602d914821b0b0b6d7bb2723f83a345))
* add sks multi series prophecy endpoint ([95cc3aa](https://github.com/core0-io/pythia/commit/95cc3aaefa45280b55dee80dd69ab1a9383b2928))
* **prophecy:** support multi series sql prophecy ([2716d8a](https://github.com/core0-io/pythia/commit/2716d8a5c59d72bdf7cbed0b7971aac9683dc280))
* qoe_scores add datetime ([3361ba4](https://github.com/core0-io/pythia/commit/3361ba484aa843664cccfdf38b17ae373a52e620))
* **sks:** add description and raw markers list ([aa57bc1](https://github.com/core0-io/pythia/commit/aa57bc1641834dfba373a825715d17b08f906c67))
* support tags and markers filter ([162adfe](https://github.com/core0-io/pythia/commit/162adfe0071dee7d771cb5c35664efeffce40ee7))

### Bug Fixes
* send logs to stderr in subprocess, fix past log limit ([f3a68fa](https://github.com/core0-io/pythia/commit/f3a68fa36644ac4ee51715bbd0e15914fb7932f3))
* prophecy table name ([6b63aa7](https://github.com/core0-io/pythia/commit/6b63aa7bf6f6b61e3ff669508ba9ae5e66050b0c))
* add deleted to config ([dd12679](https://github.com/core0-io/pythia/commit/dd12679670f025487d43b2028bc049218212da13))
* anomaly detector job factory support delete ([d98889d](https://github.com/core0-io/pythia/commit/d98889d503409a2bbd4639b55e455ba3c980704b))
* delete anomaly-detector ([787abaa](https://github.com/core0-io/pythia/commit/787abaae07fa9e8a7da3105b271674daf20209f3))
* don't print out Failed to resolve service ([a8cc07d](https://github.com/core0-io/pythia/commit/a8cc07d7d53a095b0184edefcb1c253aee018287))
* loki url is optional ([a358436](https://github.com/core0-io/pythia/commit/a358436d00674a1815b64384d930dfeed42d64a3))
* loki is optional ([d891180](https://github.com/core0-io/pythia/commit/d89118071b3914591603bfe05ff045285bd047bf))
* **greptime:** return value in string ([7902f84](https://github.com/core0-io/pythia/commit/7902f84772343c1d3491324d78659f50920a482c))
* **anomaly-detector:** return timestapm in second instead ms ([b2fdbab](https://github.com/core0-io/pythia/commit/b2fdbab19e69508c0360f1b46b1463b82b2d5f2a))
* bind ip to 0.0.0.0 ([bd8421f](https://github.com/core0-io/pythia/commit/bd8421f89dd4219b5d0b1625c4231966b670e081))
* uvicorn logging ([25b9556](https://github.com/core0-io/pythia/commit/25b9556d2a02cac3a5b7078c1adc6c4816fcfa1d))
* **ad:** overall score table name ([50de13b](https://github.com/core0-io/pythia/commit/50de13b3096b11f4162382abc3708521ebc9ea62))
* update postgres_password to postgres_pass ([e16405b](https://github.com/core0-io/pythia/commit/e16405b4ab79247f9bf4cdd6e2cb89187e80c62d))
* revert logger change ([0f534ee](https://github.com/core0-io/pythia/commit/0f534eeefcb7065b966399640fa4a410eea23792))
* handle no logs but have metrics ([a5bb75e](https://github.com/core0-io/pythia/commit/a5bb75e78f7f224f4d61238b5582b8a95738b1d2))
* **api:** add overall scores ([333e251](https://github.com/core0-io/pythia/commit/333e2512dc8cbe36b8021680bbd4b041253d39e6))
* **service:** add greptime service ([0eb2d15](https://github.com/core0-io/pythia/commit/0eb2d1557b4de2c04af69bcea02bd1000aa3df9a))
* **scheduler:** use nomad alloc id ([936379d](https://github.com/core0-io/pythia/commit/936379dc7bb9f13a3e1ffdb0eedae507ba2aa670))
* greptime table ([0073add](https://github.com/core0-io/pythia/commit/0073adda80d7510346cc0e7955581a6cea816c09))
* log rate ([c8fa150](https://github.com/core0-io/pythia/commit/c8fa1509654cc44262987bd2445452bfa2cbeae2))
* preprocess_reader path config ([571f4f0](https://github.com/core0-io/pythia/commit/571f4f0a26df14778fce347c69ecea4e0546aa23))
* mimir enabled and greptime sql ([263682c](https://github.com/core0-io/pythia/commit/263682c5711efaaabe3cdf73b88deff0ccd80993))
* environment variables ([2d3235d](https://github.com/core0-io/pythia/commit/2d3235d49dabcb181b9bb66f57f385d7d7d37379))
* compatible with sks ([f3a6895](https://github.com/core0-io/pythia/commit/f3a6895f1d22b3d7dcb1d9bdbd5e2fd8c8e72af0))
* merge past suppression into refactor ([808f1d8](https://github.com/core0-io/pythia/commit/808f1d88de54fcdc82ec27b658a126e0d72d3081))
* update rssi threshold ([5ebfda7](https://github.com/core0-io/pythia/commit/5ebfda733be12acf4b295d4c3ad13ea8f07fbd09))
* **sks:** add INTERVAL '10 minute'  to match promql ([77a6ee1](https://github.com/core0-io/pythia/commit/77a6ee1af61142c9621df6c314f1e5c86cad2df2))
* seperate openai embedding url ([0827bd2](https://github.com/core0-io/pythia/commit/0827bd2ee182f54ccfaba6e1b0594b0b7b4d155b))
* update sks coverage rssi calculation ([3e29658](https://github.com/core0-io/pythia/commit/3e2965821c5742afd2d26ab90938a22379e80776))
* merge ([f988070](https://github.com/core0-io/pythia/commit/f988070f4969fa17f0512b4ef1f0652b463d86e9))
* job store sql ([9a75237](https://github.com/core0-io/pythia/commit/9a7523771c930be67501b4fed0fa0aee6c4aa3cc))
* correlation circular import ([00535ae](https://github.com/core0-io/pythia/commit/00535ae1e3c97e724654fc40354bd6586361bd7a))
* update ruff rules ([0a1d67b](https://github.com/core0-io/pythia/commit/0a1d67ba7f7c137c54b918f7583dbfc85075d5ba))
* **sks:** update throughput_symmetry_score desc ([3c986e6](https://github.com/core0-io/pythia/commit/3c986e6c67e20d50771e1d9a04995f441b11b7f1))
* **sks:** update qoe sqls ([c468323](https://github.com/core0-io/pythia/commit/c4683230cb4b2950c81ee0fda2a725c198ee15d6))
* lint ([73b69c1](https://github.com/core0-io/pythia/commit/73b69c18b1163c757916a7c1730cf4998c36ed90))
* rx and tx ratio col can be empty ([5ac0a6d](https://github.com/core0-io/pythia/commit/5ac0a6d80b47776b6a828b7840ffe1aeb7e4d43e))
* **sks:** throughput qoe chan util ([48dadd5](https://github.com/core0-io/pythia/commit/48dadd57cafb605a3e5a2426570c070a76744aac))
* add ap_radio_obss_chan_util to throughput_efficiency ([d17fa39](https://github.com/core0-io/pythia/commit/d17fa393a68afb5a4ddbde622c70f4dcb5036bb5))
* **sks:** mcs nss columns can be empty ([cabdc6e](https://github.com/core0-io/pythia/commit/cabdc6edaeb09f9a6f806074ce092802fdd13753))
* **sks:** add bandwidth 160 mcs table ([fdf1b22](https://github.com/core0-io/pythia/commit/fdf1b222dac27580088e93fdb46675974bcbc35a))
* orjson import ([4311a90](https://github.com/core0-io/pythia/commit/4311a90052191403496e9957cc905827bb2fd952))
* **sks:** update sks model to qwen-turbo ([17c0969](https://github.com/core0-io/pythia/commit/17c0969eabd10ef3fc247553887f9931eeabc1f0))
* ruff lint ([cbbab25](https://github.com/core0-io/pythia/commit/cbbab2558cc4eb653353e8574cf0f9c7af74c125))
* lint ([14dd89a](https://github.com/core0-io/pythia/commit/14dd89a96f5c9a31d99dcbfb870279db4bd46d40))
* ruff format ([34b0e84](https://github.com/core0-io/pythia/commit/34b0e84a8c2803c2add34fcc03db9d8a243b2a75))
* ruff unsafe fix ([4439066](https://github.com/core0-io/pythia/commit/4439066c1fa8d138177e025171d7cf9386f64f96))
* ruff lint ([4afa681](https://github.com/core0-io/pythia/commit/4afa681dd6f93e62fe27b0c9c04ddb33fa8304fc))
* ruff format ([bbd9f8e](https://github.com/core0-io/pythia/commit/bbd9f8e9345e3af27fa34b6c390772b2a7ff5a16))
* ruff lint fix ([8b5ad89](https://github.com/core0-io/pythia/commit/8b5ad89291280b9d2531986bb539ddda0e7a5c68))
* histogram for mcs and nss value ([c62d89d](https://github.com/core0-io/pythia/commit/c62d89de5898aa0479a43a6ff643f5d041a3aa1c))
* **sks:** mcs and nss calculation ([7e655f8](https://github.com/core0-io/pythia/commit/7e655f8779ed27b17cda44a7971f5bb7d867b4ec))
* **sks:** mcs nss le can be inf ([364d0fe](https://github.com/core0-io/pythia/commit/364d0fe723ec394b05058985752802ea56b05170))
* **sks:** update description ([7574130](https://github.com/core0-io/pythia/commit/7574130d4ac482051f1a9a2b6c8e3ca0ecde713f))
* **sks:** update the nss and mcs metrics to counter ([3611ca1](https://github.com/core0-io/pythia/commit/3611ca1dc6de7a4aeacc95347cdbcbd07311f191))
* **sks:** tweak markers relation ([0518eda](https://github.com/core0-io/pythia/commit/0518eda44d284405de8982c04aca2913884258d5))
* **sks:** improve qoe calculation ([827749c](https://github.com/core0-io/pythia/commit/827749c5a36f18156fa26a2e3b6314322e7b034a))
* improve throughput success rate qoe ([fde7e8f](https://github.com/core0-io/pythia/commit/fde7e8f09622bf82e04475c3e8d2e35edf9bd0ca))
* improve rssi qoe ([9103083](https://github.com/core0-io/pythia/commit/910308357aaf5e5a6209dd3dec1449b48f37f931))
* add description for rssi ([2fcbaca](https://github.com/core0-io/pythia/commit/2fcbaca573e80fc6a9917cfe01257cd1de3ba911))
* **sks:** add rssi score to throughput qoe ([2c1ff59](https://github.com/core0-io/pythia/commit/2c1ff591a5a2d17d1b76ce5082bca9918bdbe7f1))
* **sks:** handle zero mcs or nss correctly ([60e0674](https://github.com/core0-io/pythia/commit/60e0674e0bd8c5a7393d68c63210bdcfc462759d))
* **sks:** step defaults to 10m ([a3248b5](https://github.com/core0-io/pythia/commit/a3248b5164b8b991623278c6d204b0ecc03a0186))
* **sks:** qoe calculation add tolerance ([8d533a2](https://github.com/core0-io/pythia/commit/8d533a293e1e8443c45f4030561767addeba2019))
* **sks:** inconsistent qoe values ([36a2304](https://github.com/core0-io/pythia/commit/36a23046a35b112ffcc478b62fac769b7fce69b4))
* **sks:** improve connection sql and endtime minus 10mins ([0b131aa](https://github.com/core0-io/pythia/commit/0b131aaec89bace08dfd0e5f6d9929e6ecbaed29))
* **sks:** use open_process instead of anyio to_process ([0d55d81](https://github.com/core0-io/pythia/commit/0d55d81bf036105ed341ed59b56c9328fb460e02))
* **sks:** ap_radio_vap_node_band_width can be empty ([a38f84a](https://github.com/core0-io/pythia/commit/a38f84a87600fb72b38c81eb1aa18f8e77f067b6))
* **sks:** skip empty df ([cc6c6da](https://github.com/core0-io/pythia/commit/cc6c6dab8b6dae46b9dc9909bf94c93c9fc39d68))
* sks qoe markers ([41c5612](https://github.com/core0-io/pythia/commit/41c561254159b745e1c96562c3df6dd52175a5d9))
* use gpt-4.1-mini for sks env ([a4fa0ca](https://github.com/core0-io/pythia/commit/a4fa0ca346a01c7bda08eca297cc5d1f185d44db))
* add tiktoken cache, and increase greptime client timeout ([7b10a48](https://github.com/core0-io/pythia/commit/7b10a4871daa04bb0438371d2086cb66a771c7e4))
* remove @next_jobs_idx ([8c78ae0](https://github.com/core0-io/pythia/commit/8c78ae051804313c7a4c8ae1b501fd1feaadbf4d))
* change s3 env to S3_PASS and upgrade anyio to 4.9 ([d4585fc](https://github.com/core0-io/pythia/commit/d4585fc50937bb28640b5fcbac0489dad72e5ff0))
* increase interval from 5m to 10m ([9ff949d](https://github.com/core0-io/pythia/commit/9ff949d9f95bc8a8795870c62f806b9f5508a3c9))
* qoe prompt ([b2f5a5b](https://github.com/core0-io/pythia/commit/b2f5a5b56eccd817ae433870fc808df48e8e05a1))
* adjust query endtime ([75bf87b](https://github.com/core0-io/pythia/commit/75bf87b3578b5c5f94a8c666de3126282e6d1865))
* avoid throw 404 error in sks env ([5666c50](https://github.com/core0-io/pythia/commit/5666c508376814a47c79d491dfe42b685751456f))
* skip translate empty advisory ([cf58114](https://github.com/core0-io/pythia/commit/cf581145b02e2185c74c31c73c9893da722c8c25))
* coverage qoe ([52723fe](https://github.com/core0-io/pythia/commit/52723fe4b2cbc2498b66930a8377dd784173365f))
* interference score ([ecd007e](https://github.com/core0-io/pythia/commit/ecd007e964fa676115652fcda6cc8468f5647893))
* qoe calculation ([2c4634c](https://github.com/core0-io/pythia/commit/2c4634c41efb9668b46ce3b8517577ee5f91e690))
* qoe calculation ([ec80896](https://github.com/core0-io/pythia/commit/ec808965d6f6aa0385ae98e4a63b42bacfbca655))
* remove unused files ([79fbd77](https://github.com/core0-io/pythia/commit/79fbd77c5f6630a2d1c9240d713cc44a6fa10cbd))
* continue inference if not logs but has markers ([6af5ce7](https://github.com/core0-io/pythia/commit/6af5ce7ec398c78039d8f23946ab56997e0951a5))
* improve translator prompt ([60f5b98](https://github.com/core0-io/pythia/commit/60f5b98959a58dc177e4eb10ba58c83e4c6b9f16))
* lower advisory lang ([c0e0c3c](https://github.com/core0-io/pythia/commit/c0e0c3c322ed7c89171cf2218522f05d0777e6b8))
* qoe scores to 5m interval ([1e3fa81](https://github.com/core0-io/pythia/commit/1e3fa81997bbe6016a5645458473b5120a1c125e))
* csv .2 format for float ([190ea4e](https://github.com/core0-io/pythia/commit/190ea4ebbe55e84ea6fe6fe782f09cf78c7b02b2))
* throughput issue ([60a5d9c](https://github.com/core0-io/pythia/commit/60a5d9c57dc11da18dda87242c0b91a39cb68081))
* skip prophecy model less than 3 rows ([77e54b1](https://github.com/core0-io/pythia/commit/77e54b140b17cb14ab36973bde9bea8863249097))
* continue metric report if some metrics are missing ([b4a04d6](https://github.com/core0-io/pythia/commit/b4a04d651248233c66b0ebce7c6cc393d031a2aa))
* add log filter ([f5d507a](https://github.com/core0-io/pythia/commit/f5d507a1e75ad321c99dfa0aa27a7255464e1ece))
* use latest docker buildx version ([6eeff3d](https://github.com/core0-io/pythia/commit/6eeff3d44065d1854396d438a4e2a59499bfc7e9))
* add TableNotFound exception ([282d9c8](https://github.com/core0-io/pythia/commit/282d9c86d0c7cb19fd00f9dc0423a90c2c7d7e81))
* handle colon in metric name ([bf815d4](https://github.com/core0-io/pythia/commit/bf815d4d563182fba3b286fe45d9253e04542f26))
* use to_process to start jobs ([436ce31](https://github.com/core0-io/pythia/commit/436ce3174f511202d1ad66c77a1c73bd4743c5b6))
* avoid scientific notatio timestamp ([977e97c](https://github.com/core0-io/pythia/commit/977e97cad9f0daba998d95dbc97ee22857ee4240))
* compatible with sks env ([04454a5](https://github.com/core0-io/pythia/commit/04454a5c5bc88d6e0f904a8a519c07e660491a54))
* query markers promql query ([f6a3772](https://github.com/core0-io/pythia/commit/f6a377235635dbb1acc3a1d6eb2c8a3af6e0045a))
* query sub marker ([677526e](https://github.com/core0-io/pythia/commit/677526eef09bbd3583f5f916195c8e7794f17a77))
* **ci:** download geo resource ([b50348a](https://github.com/core0-io/pythia/commit/b50348af3b31965d6a59010b8f7a2ba98db07fb5))
* qoe marker sorted value ([7fd7f52](https://github.com/core0-io/pythia/commit/7fd7f525d99bef6b23e9bbdd3c84a9093c90ac00))
* sorted timestamp for qoe and raw markers ([7c8b61a](https://github.com/core0-io/pythia/commit/7c8b61a06e6594573d20e44b054fa8e578012183))
* function name conflict ([58d1d7b](https://github.com/core0-io/pythia/commit/58d1d7b482c8c592d022b9d6781d89ea530f387c))
* empty dfs for qoe marker ([c4a51e7](https://github.com/core0-io/pythia/commit/c4a51e716c04b2b939929e45bdbc180d46fcafab))
* return empty result if not prophecy ([17a82d9](https://github.com/core0-io/pythia/commit/17a82d9aaf0e21423afe7441fee01dafd674b61e))
* raw markers return description ([468e189](https://github.com/core0-io/pythia/commit/468e1896d3868c3f5c1116e3a8e5344969a64d8e))
* use qoe_markers ([7779d5c](https://github.com/core0-io/pythia/commit/7779d5c7eded9f2b4b3344e0ee0f84b506940e5d))
* missing sub markers for marker query ([6de6a63](https://github.com/core0-io/pythia/commit/6de6a632d6ce9682939bcbf8970fb13213dd5511))
* ondone hook ([cc1e239](https://github.com/core0-io/pythia/commit/cc1e2395def80dcd37c9efbf01889ee8e4633fd8))
* scores return seconds timestamp ([1159e9e](https://github.com/core0-io/pythia/commit/1159e9efd52da5c1d379d44515b93f569ab87275))
* return object for marker query ([734b2ee](https://github.com/core0-io/pythia/commit/734b2ee635e36b5d38d14033c3554c284ede2a3f))
* remove main.py ([3313553](https://github.com/core0-io/pythia/commit/3313553929a9af9a01c587b2584484422254ff48))
* uuid to str ([40290ca](https://github.com/core0-io/pythia/commit/40290ca6e1aa8be38923c70de3cfa3ffae86f651))
* support non uuid ([09b9ece](https://github.com/core0-io/pythia/commit/09b9ece67bd12bf8c6173e8e0065f56e53c98db2))
* don't use batch execute for querying scores ([28355d6](https://github.com/core0-io/pythia/commit/28355d65b536c9398c95b852adaeb93154f3a9b6))

### Other Changes
* rename expections to custom_exceptions, and extract LADOption ([d433dc2](https://github.com/core0-io/pythia/commit/d433dc23d46d80104eedb35b4b8580ec5cd795db))
* move build_metric_points to utils ([1256e61](https://github.com/core0-io/pythia/commit/1256e6187e8ade1d6bd4911861d71618ae02c724))

## [1.18.2](https://github.com/core0-io/pythia/compare/1.18.1...1.18.2) - 2025-06-29

### Bug Fixes
* **ci:** only tag latest for master commits ([00f1ecd](https://github.com/core0-io/pythia/commit/00f1ecd88dbf18aee4722758e47314bd45a28880))
* logging ([bd7a2e7](https://github.com/core0-io/pythia/commit/bd7a2e782c946ee84fc776438b71adf39e2688e7))
* bert problematic log groups can be empty ([93b0aaf](https://github.com/core0-io/pythia/commit/93b0aaf2f5f266a2b64191c229b22ed8cd5d4e82))

## [1.18.1](https://github.com/core0-io/pythia/compare/1.18.0...1.18.1) - 2025-06-11

### Bug Fixes
* add retry for root cause advisory ([50fc0bc](https://github.com/core0-io/pythia/commit/50fc0bcaa1916658d2535bd8adc502764adc8d2e))
* increase openai conversation attempts ([fa324f2](https://github.com/core0-io/pythia/commit/fa324f20230d1e3b485d211b21f09ef673912575))
* improve logging ([0d39533](https://github.com/core0-io/pythia/commit/0d3953312608c7de5bce44a043ecb3b07db71733))
* remove models prefix for gemini text embedding ([4c57220](https://github.com/core0-io/pythia/commit/4c5722039efb492574f39079451b080ceb9d33df))
* change from gemini to vertex ([b6ecd10](https://github.com/core0-io/pythia/commit/b6ecd10c1e9fe36aa44c86bea83d0cb28e712362))
* missing structlog import ([5eb84d7](https://github.com/core0-io/pythia/commit/5eb84d7dba70e5b687fd5088e16030bd7fc60869))
* gemini indent ([768bbef](https://github.com/core0-io/pythia/commit/768bbef828aeaed85e794a77cd5bffedd9e7d5e1))
* optimise logging ([b90a87d](https://github.com/core0-io/pythia/commit/b90a87da401cf186581f858fc488fe47cd6ab1a2))
* add logs ([687328b](https://github.com/core0-io/pythia/commit/687328bfafb01bd6a65cc075688bf0832ee4a024))
* change llm_past_suppression_model back to qwen3 ([ee81693](https://github.com/core0-io/pythia/commit/ee816938ff8ed4383179234d8a44a26832000e73))
* improve logging ([d7ee374](https://github.com/core0-io/pythia/commit/d7ee37461ab5da5801e72c3048b0e420820a7913))
* reduce the vector search logs to top5 from top8 ([93bdb1e](https://github.com/core0-io/pythia/commit/93bdb1e3643e47e757dc47f3992066f789ffaefa))
* token limit past logs and change back to qwq ([7a6a295](https://github.com/core0-io/pythia/commit/7a6a2959f3e38ca9ac0b802568a375455a3f5aa1))
* log llm requests to ffwd ([6730aa0](https://github.com/core0-io/pythia/commit/6730aa0c545f0bd829f0bfb8f1f91e5d58107e97))

## [1.18.0](https://github.com/core0-io/pythia/compare/1.17.0...1.18.0) - 2025-05-29

### Features
* add openrouter ([4e51577](https://github.com/core0-io/pythia/commit/4e515770afe75afad724cc2131a1aa347287dfc5))
* add problematic_logs to the report ([1bf3a43](https://github.com/core0-io/pythia/commit/1bf3a43950f884f5b08304341ae0907cf62d8b14))

### Bug Fixes
* add qwen3 and use qwen3 325b for past suppression ([5ba4133](https://github.com/core0-io/pythia/commit/5ba4133012909ce2f2ce5b90c7ccf6c547d8766a))
* improve retry strategy ([5e750b5](https://github.com/core0-io/pythia/commit/5e750b5d3dd9b75e7ebd99008953286565fad30a))
* improve retry strategy ([23be58b](https://github.com/core0-io/pythia/commit/23be58b3c5a319512928710686d4d70d9458c416))

## [1.17.0](https://github.com/core0-io/pythia/compare/1.16.5...1.17.0) - 2025-05-26

### Features
* add processed_logs to the report ([ee5e652](https://github.com/core0-io/pythia/commit/ee5e652ba39199683b70746ecbe6c192bc758ca4))

### Bug Fixes
* improve root cause summary ([02329b9](https://github.com/core0-io/pythia/commit/02329b9e244290e29e406339143d1d3d8f41ea0b))
* add default advisory for log structure and log sequence ([fee34fd](https://github.com/core0-io/pythia/commit/fee34fd115382682969727ee89e4cab169dcae8d))
* don't generate empty full inference reports ([aae5a6d](https://github.com/core0-io/pythia/commit/aae5a6d93292abb3d66282e2857d5993288a779d))
* add default values to report if it's empty ([9b359d7](https://github.com/core0-io/pythia/commit/9b359d7a64790fac34dd9aeb31c6b39ff912e5d0))
* add missing reasoning score to empty report ([1dd6157](https://github.com/core0-io/pythia/commit/1dd615733143303e9535e07152db3a671ed16ced))
* lower 4.1-mini context limit to 60k ([78e521a](https://github.com/core0-io/pythia/commit/78e521a20e5fd3cb5dcf3f0d22c7889931453407))
* improve prompt ([52002d1](https://github.com/core0-io/pythia/commit/52002d1dc6a804aeddd0c278a3c97e4f1011dd00))

## [1.16.5](https://github.com/core0-io/pythia/compare/1.16.4...1.16.5) - 2025-05-21

### Bug Fixes
* log anomaly deletion job ([42e0b58](https://github.com/core0-io/pythia/commit/42e0b58865503e0c818e3fcdfe23dd30c0946531))
* remove staging base path ([9162202](https://github.com/core0-io/pythia/commit/9162202c3250bf4c38814c2791f4ecf9d7d72380))
* anomaly detector create-job ([0777f4c](https://github.com/core0-io/pythia/commit/0777f4cfefd8e8b48d39247159ed9ceec7301c40))
* add anomaly deletion job to clean up garage ([8987400](https://github.com/core0-io/pythia/commit/8987400bf8549a010eb91ae2647ccb70442f2353))
* avoid tag cuda image to latest ([9f725db](https://github.com/core0-io/pythia/commit/9f725db1d007b320fa38b9bcebe32e5f53809257))

## [1.16.4](https://github.com/core0-io/pythia/compare/1.16.3...1.16.4) - 2025-05-21

### Bug Fixes
* use model name for knowledge base requests ([fc71cd0](https://github.com/core0-io/pythia/commit/fc71cd06a8d50b489147eb4dbc6541934e13889d))
* add on_premises_model config ([5cda822](https://github.com/core0-io/pythia/commit/5cda8229d7c76461ccf358fe231ef24a1daaa075))
* add try except to contextual ranking ([707d15f](https://github.com/core0-io/pythia/commit/707d15f4893d18e2852ee4889b3ce009aeac8346))

## [1.16.3](https://github.com/core0-io/pythia/compare/1.16.2...1.16.3) - 2025-05-19

### Bug Fixes
* docker file ([3c63dcb](https://github.com/core0-io/pythia/commit/3c63dcbf4810b9355b9129bce1492d4e04ff4095))
* download tiktoken models in github action ([2b897ba](https://github.com/core0-io/pythia/commit/2b897baa75328c452cbca57850544be736e09c5b))
* download tiktoken files into docker image ([a881199](https://github.com/core0-io/pythia/commit/a881199adc9c590633905c2d40f5a8fafd4f8fcc))

## [1.16.2](https://github.com/core0-io/pythia/compare/1.16.1...1.16.2) - 2025-05-15

### Bug Fixes
* raise ServiceError instead of RecoverableError ([ba2438a](https://github.com/core0-io/pythia/commit/ba2438aef8a6236ecc434819bf447179ad3b9e26))

## [1.16.1](https://github.com/core0-io/pythia/compare/1.16.0...1.16.1) - 2025-05-14

### Bug Fixes
* add retry to embedding ([4d6989b](https://github.com/core0-io/pythia/commit/4d6989b926a70d20b6caa9b7fd22bdc74f827a15))

## [1.16.0](https://github.com/core0-io/pythia/compare/1.15.1...1.16.0) - 2025-05-14

### Features
* add overall scores ([66db499](https://github.com/core0-io/pythia/commit/66db4997679f7a10acd4b83713e8b4184fc2f749))

### Bug Fixes
* the sample from LLM ranking can be None ([ee4b816](https://github.com/core0-io/pythia/commit/ee4b816618c4a9cc6efee364d187207ea98f6570))
* remove thinking tag for reasoning models ([4c08465](https://github.com/core0-io/pythia/commit/4c084653a8bc8e3371cf7dfd9e6aa05127f2e2da))
* add vector_search parameters to train ([52447e0](https://github.com/core0-io/pythia/commit/52447e031508cd0be894cae6dd0230d0cabf5941))
* add missing score calculation file ([4bf9bc1](https://github.com/core0-io/pythia/commit/4bf9bc10355829ca187a79b81389ab2c66c3c04e))
* retry reasoning score 3 times ([7978d34](https://github.com/core0-io/pythia/commit/7978d349f562b1449a878b540c74dd4a21d7a76d))

## [1.15.1](https://github.com/core0-io/pythia/compare/1.15.0...1.15.1) - 2025-05-08

### Bug Fixes
* revert log structure score calculation to (actual - expected) / expected ([83c534b](https://github.com/core0-io/pythia/commit/83c534bf446232719e814cd6e5a84c690c8436ac))
* response from loki ([264c234](https://github.com/core0-io/pythia/commit/264c2345ce84b214b4d7e44228385c100bb91178))

## [1.15.0](https://github.com/core0-io/pythia/compare/1.14.0...1.15.0) - 2025-05-08

### Features
* implement llm conversation ([944fef9](https://github.com/core0-io/pythia/commit/944fef9d40f919ebfec2970a22746c353063b55a))

### Bug Fixes
* improve drain problematic log groups, add default value to correlated_markers ([4f1fa43](https://github.com/core0-io/pythia/commit/4f1fa43c007ed078aa376e295a2c66248f6fe752))
* use provider for advisories on-prem ([9e82631](https://github.com/core0-io/pythia/commit/9e82631aac50d4f5fad1fdd699d534ff04d7a369))
* invoke completion when create thread ([dda4fa0](https://github.com/core0-io/pythia/commit/dda4fa064201fd92e74432ce1bb80bfbe636b19e))

## [1.14.0](https://github.com/core0-io/pythia/compare/1.13.1...1.14.0) - 2025-05-07

### Features
* add correlation to report ([c7cde69](https://github.com/core0-io/pythia/commit/c7cde69b0c52db5683d46475ed9db6a21613024e))
* **llm:** improve all the prompts, add log grouping and past advisories (#162) ([4d2828e](https://github.com/core0-io/pythia/commit/4d2828e112ce3fdbebe3fe53817bff809981a56f))
* add greptime log ([237c519](https://github.com/core0-io/pythia/commit/237c5192091192620dd6c58a6d8a51ce7f0b3521))

### Bug Fixes
* greptime sql condition ([c24f0b6](https://github.com/core0-io/pythia/commit/c24f0b6f4e546817ab243978218df94e8e8819e1))
* ignore the metric type ([cd9a57e](https://github.com/core0-io/pythia/commit/cd9a57e2ea0aa220feee1adfa553551b99de3a51))
* drop nan for logbert dataframe ([73aa06b](https://github.com/core0-io/pythia/commit/73aa06b7eccedc32db0cd56549e59d94223d46b2))
* skip hopkins stats if less than 2 samples ([42c63ac](https://github.com/core0-io/pythia/commit/42c63ac26eb9493fc573dde7a31b26afc7703f2c))
* vector search bugs and use gpt-4.1 instead of gpt-4o ([e9a8dd1](https://github.com/core0-io/pythia/commit/e9a8dd184e9cacd32b722d35d3de0cec4f574178))
* compatible with pgsql ([f99e84f](https://github.com/core0-io/pythia/commit/f99e84f0d5097c2ecb1027d667d5d9b2b784e38f))
* compatible with pgsql. use case instead of if and cast interval ([82af30d](https://github.com/core0-io/pythia/commit/82af30d66a3719d510c58ad0b51a7b6a60f20aef))
* greptime log table ([7c1a6d3](https://github.com/core0-io/pythia/commit/7c1a6d3f92d365dde312af7e3687c921edcc6a61))
* remove index hint ([e9297d3](https://github.com/core0-io/pythia/commit/e9297d3a169d7416d79acfab2d8f59459820fc51))
* knowledge base prompt ([1689cab](https://github.com/core0-io/pythia/commit/1689cab46dd6c199115e0b6c45e86290a02493a6))
* use last_trained path for fine tune model ([3a10fa5](https://github.com/core0-io/pythia/commit/3a10fa52dc11f73cf17ce5cdb27aaa152ddb2a2d))

## [1.13.1](https://github.com/core0-io/pythia/compare/1.13.0...1.13.1) - 2025-02-20

## [1.13.0](https://github.com/core0-io/pythia/compare/1.12.22...1.13.0) - 2025-02-18

### Features
* use llamington knowledge base ([a0d9216](https://github.com/core0-io/pythia/commit/a0d9216168ca1116dc0f6c0fd132f2e70082fc87))

### Bug Fixes
* use last_trained path for fine tune model ([21eb5b0](https://github.com/core0-io/pythia/commit/21eb5b0407b5017593ad5fed4c57dff976d15c7a))
* remove annotation texts ([407c106](https://github.com/core0-io/pythia/commit/407c106c68577409cb7b8ab5f4a174bec882ba87))
* knowledge-base-id can be none ([5c667a2](https://github.com/core0-io/pythia/commit/5c667a213797cbf53559355796a99a38a8caaf2e))
* convert llamington exceptions to ServiceException ([5c15b46](https://github.com/core0-io/pythia/commit/5c15b46f79a0fea076cd4752bb3e594aa248a350))
* llamington service name ([7ace020](https://github.com/core0-io/pythia/commit/7ace020543464ec076fd99df8acf7df7f2ebf5b5))
* convert llamington exceptions to ServiceException ([88e4a6b](https://github.com/core0-io/pythia/commit/88e4a6b94e32c78088e59ab186402eeabd5ea5c9))
* improve prompt ([3609c25](https://github.com/core0-io/pythia/commit/3609c250526fab381e09fc9cf4855cd4ec883340))
* use multiprocessing for prophecy prom read ([fd297fd](https://github.com/core0-io/pythia/commit/fd297fd3f6e270e15c7bb3abe2fd42622fbd6aee))
* template training ([3bb5863](https://github.com/core0-io/pythia/commit/3bb5863f76f9c6fe7a6a76bc0a7431505672bb4b))

## [1.12.22](https://github.com/core0-io/pythia/compare/1.12.21...1.12.22) - 2025-02-21

### Bug Fixes
* use last_trained path for fine tune model ([21eb5b0](https://github.com/core0-io/pythia/commit/21eb5b0407b5017593ad5fed4c57dff976d15c7a))

## [1.12.21](https://github.com/core0-io/pythia/compare/1.12.20...1.12.21) - 2025-02-19

## [1.12.20](https://github.com/core0-io/pythia/compare/1.12.19...1.12.20) - 2025-01-29

### Bug Fixes
* manually call gc and delete dataframe ([b16be25](https://github.com/core0-io/pythia/commit/b16be25eff959b6cee7713fc0dbbb74539b7fbed))
* add metronome to job/services ([f653dbb](https://github.com/core0-io/pythia/commit/f653dbbf5a92a564770f84b9069db2e016aa2539))

## [1.12.19](https://github.com/core0-io/pythia/compare/1.12.18...1.12.19) - 2025-01-28

### Bug Fixes
* use last log volume profile is this time zero bytes ([29c4143](https://github.com/core0-io/pythia/commit/29c4143f0dbfe68eb2fdbb8b0471a6caeb93f274))
* calculate lines time slots when shouldn't retrieve all ([c3c4508](https://github.com/core0-io/pythia/commit/c3c4508ff6af06138596d2b9311f07a0bedd1038))
* drop prophecy nan rows ([1269883](https://github.com/core0-io/pythia/commit/12698837b5339da212a06f533b5a5124f9b67fc9))

## [1.12.18](https://github.com/core0-io/pythia/compare/1.12.17...1.12.18) - 2025-01-23

### Bug Fixes
* replace _reserved_flow_id with flow_id ([8651fab](https://github.com/core0-io/pythia/commit/8651fab340b3f0fdd990da7cf5b5cd1d5b1d1962))

## [1.12.17](https://github.com/core0-io/pythia/compare/1.12.16...1.12.17) - 2025-01-23

## [1.12.16](https://github.com/core0-io/pythia/compare/1.12.15...1.12.16) - 2025-01-23

### Bug Fixes
* invalidate mimir-frontend and handle empty metrcis ([8e7a52e](https://github.com/core0-io/pythia/commit/8e7a52e865a90f9e4ce2bb4e743025531b263d3c))

## [1.12.15](https://github.com/core0-io/pythia/compare/1.12.14...1.12.15) - 2025-01-22

## [1.12.14](https://github.com/core0-io/pythia/compare/1.12.13...1.12.14) - 2025-01-21

## [1.12.13](https://github.com/core0-io/pythia/compare/1.12.12...1.12.13) - 2025-01-21

### Bug Fixes
* use metronome querying for accounting regardless mimir-enabled ([e59ef15](https://github.com/core0-io/pythia/commit/e59ef15b723d95dfebb0ce0bb0ca54cdaae7486e))
* convert numpy float64 to python number ([cc89a53](https://github.com/core0-io/pythia/commit/cc89a532b25821a82a83aad094863bc368e5042a))
* support mimir_enabled toggle ([6f2bc07](https://github.com/core0-io/pythia/commit/6f2bc07d5274a2727aa65e678321d9fb32289e98))
* duplicate logger handler ([b17d67b](https://github.com/core0-io/pythia/commit/b17d67b361b1fe7687d9d7549d12c3b22dbb439f))

## [1.12.12](https://github.com/core0-io/pythia/compare/1.12.11...1.12.12) - 2025-01-20

### Bug Fixes
* summary structure ([5260869](https://github.com/core0-io/pythia/commit/5260869c213a7bc16fcd45476a22a6cace8081d7))

## [1.12.11](https://github.com/core0-io/pythia/compare/1.12.10...1.12.11) - 2025-01-20

### Bug Fixes
* return a message when no symptoms in the report ([f2baeb4](https://github.com/core0-io/pythia/commit/f2baeb4d0d3b5b9e235ceb766f3bb716d6251249))

## [1.12.10](https://github.com/core0-io/pythia/compare/1.12.9...1.12.10) - 2025-01-17

### Bug Fixes
* don't fail job when s3 cleanup failed ([4773137](https://github.com/core0-io/pythia/commit/4773137c79e1dd8d6af5b2c50d0b7003b606e63b))
* use --only main instead of --no-dev for poetry ([71282f8](https://github.com/core0-io/pythia/commit/71282f80f50e33ea0af59359e20b07b539a46a12))
* missing return samples ([07a7302](https://github.com/core0-io/pythia/commit/07a7302c2d184a429efbd0b9142e9400491f14a0))
* avoid overriding ServiceError ([842ac18](https://github.com/core0-io/pythia/commit/842ac18621d012dbb7cc68ad7a6c9bd1b3721b02))
* none if openai_url is empty string ([b2cfb46](https://github.com/core0-io/pythia/commit/b2cfb46981d5e71023864aff477233fefb7c0aa0))
* use boolean reader for on-premises-gpu? ([9d3cd5f](https://github.com/core0-io/pythia/commit/9d3cd5ff0c363394e452dc7baea369a44aba1013))
* on-premises-gpu env ([55b3c9f](https://github.com/core0-io/pythia/commit/55b3c9fda6f80d16c7080529de55de969a5510ae))

## [1.12.9](https://github.com/core0-io/pythia/compare/1.12.8...1.12.9) - 2024-12-19

### Bug Fixes
* support multi streams of log rate ([44b726f](https://github.com/core0-io/pythia/commit/44b726f405b0b81c79e82c239c3f7665a9920c9f))

## [1.12.8](https://github.com/core0-io/pythia/compare/1.12.7...1.12.8) - 2024-12-18

### Bug Fixes
* use AutoModelForMaskedLM for retrain ([1b39cce](https://github.com/core0-io/pythia/commit/1b39cce6f0708dcee939344b527ad7764bc5e4fe))

## [1.12.7](https://github.com/core0-io/pythia/compare/1.12.6...1.12.7) - 2024-12-18

### Bug Fixes
* crystal-ball working dir ([08d0768](https://github.com/core0-io/pythia/commit/08d0768e3cb1f49d7b89268cef7afc18f2da6424))

## [1.12.6](https://github.com/core0-io/pythia/compare/1.12.5...1.12.6) - 2024-12-18

### Bug Fixes
* add torch torchvision torchaudio, move accelerate to gpu group ([e07e6af](https://github.com/core0-io/pythia/commit/e07e6afd829a421f5d592066665488f0c0bc3b3b))

## [1.12.5](https://github.com/core0-io/pythia/compare/1.12.4...1.12.5) - 2024-12-18

### Bug Fixes
* add train and trainer ([952ac22](https://github.com/core0-io/pythia/commit/952ac223ab7d5138f1e5d15297519d34bc6d3308))

## [1.12.4](https://github.com/core0-io/pythia/compare/1.12.3...1.12.4) - 2024-12-18

### Bug Fixes
* don't use train script on hosted gpu as the docker image doesn't have python ([d29480e](https://github.com/core0-io/pythia/commit/d29480e8b7b38060d4e2ae477e3b15ab015109a9))

## [1.12.3](https://github.com/core0-io/pythia/compare/1.12.2...1.12.3) - 2024-12-17

### Bug Fixes
* logbert gpu training ([4be8f93](https://github.com/core0-io/pythia/commit/4be8f936301d676fc0dd9630ed370bae106488d7))

## [1.12.2](https://github.com/core0-io/pythia/compare/1.12.1...1.12.2) - 2024-12-17

### Bug Fixes
* batch.start,end can be null ([41fcab5](https://github.com/core0-io/pythia/commit/41fcab5491ed7f3af750995e659912543c783e57))

## [1.12.1](https://github.com/core0-io/pythia/compare/1.12.0...1.12.1) - 2024-12-17

### Bug Fixes
* none if openai_url is empty string ([0d4afcb](https://github.com/core0-io/pythia/commit/0d4afcb86c3c68232276669809b83ac947a0d56a))
* use boolean reader for on-premises-gpu? ([16cac74](https://github.com/core0-io/pythia/commit/16cac7443645371f6b6c39ff4b62d1485e43415c))
* on-premises-gpu env ([982553d](https://github.com/core0-io/pythia/commit/982553d20f13e9088f0ca752cdfbeb9d05050011))

## [1.12.0](https://github.com/core0-io/pythia/compare/1.11.0...1.12.0) - 2024-12-16

### Features
* use wavelet to detect spikes ([1c3f7f3](https://github.com/core0-io/pythia/commit/1c3f7f3f95092a6d1bb89b2be62287213845be71))
* support training on hosted gpu (#137) ([a5f4732](https://github.com/core0-io/pythia/commit/a5f4732c3a0862995047d52167358722e094b30b))

### Bug Fixes
* on-premises-gpu config ([37435a6](https://github.com/core0-io/pythia/commit/37435a6c7ef8ff0495cbd4aa10b9eeec85b1da5e))
* lazy load torch ([812c0e5](https://github.com/core0-io/pythia/commit/812c0e5e320cb87b9065688bde6041e4fbca142a))
* openai_base_url ([f74c4cc](https://github.com/core0-io/pythia/commit/f74c4cc87fded465a6f37d0a9cfe9cab873767f9))
* update ollama base url ([cf65477](https://github.com/core0-io/pythia/commit/cf65477d1008d66757ac54abf9db5b972fef315f))
* use llama on premises gpu ([411b883](https://github.com/core0-io/pythia/commit/411b88343efeb27a1c7f8481fee7d3563556f0ab))
* **ci:** GeoLite City DB url ([5163371](https://github.com/core0-io/pythia/commit/516337175e8b3d86658c78d1dbf16110ad8507ed))
* rename hosted to on_premises ([4828c8d](https://github.com/core0-io/pythia/commit/4828c8d42d7815734f934c97c3c4951bc36c4f24))
* migrate from metronome batches to vector_component_sent_event_bytes_total ([8d9c204](https://github.com/core0-io/pythia/commit/8d9c20494ba02fe922d94215da3689feec8cbeaf))
* **anomaly-detector:** wrap preprocessing stage to recoverable ([95edc2b](https://github.com/core0-io/pythia/commit/95edc2bb19c2eae92d64f8e86d468dddeeee44ac))

## [1.11.0](https://github.com/core0-io/pythia/compare/1.10.0...1.11.0) - 2024-11-28

### Features
* cleanup inference and parquet files ([a8ca5ca](https://github.com/core0-io/pythia/commit/a8ca5ca118e72e9f14053f9dec10e1dd5ccd9c03))

### Bug Fixes
* prophecy next-run after canceled retrain ([5a04ca2](https://github.com/core0-io/pythia/commit/5a04ca2943513200f0a91b23793442c7ee009a26))
* skip bert task if it has been done ([a7d8b1f](https://github.com/core0-io/pythia/commit/a7d8b1f5378e52070ef30293a75a4255fc069574))
* use 4o-mini for translate advisory ([bfaa7d1](https://github.com/core0-io/pythia/commit/bfaa7d1ff8e13c8becfb4afdefbd2588afaf1bb7))
* cleaner days ([f91e3d2](https://github.com/core0-io/pythia/commit/f91e3d2501c02adb22e90bcc075459c5e27d718a))
* set inference retention days to 30 ([033e4df](https://github.com/core0-io/pythia/commit/033e4dfe34a24c6cc07b5bf5a4b734e36795687e))
* s3 config ([17c125c](https://github.com/core0-io/pythia/commit/17c125c82410ecd74ffd6dd429b5a52eaa039930))
* **anomaly-detector:** reuse preprocessed file during inference ([70f12b1](https://github.com/core0-io/pythia/commit/70f12b10dd21cd28815ab1d3072b685712524c58))
* **anomaly-detector:**  bypass preprocessing if already completed ([b26d01e](https://github.com/core0-io/pythia/commit/b26d01ebfaa5c2894d5cf45f5acd948dcba31d47))
* **anomaly-detector:** sample to 100k for hopkins stats ([c1d2d88](https://github.com/core0-io/pythia/commit/c1d2d8836c1cbcee6fa0d2bd7f9723d6d93c8067))

## [1.10.0](https://github.com/core0-io/pythia/compare/1.9.2...1.10.0) - 2024-10-24

### Features
* **anomaly-detector:** cancel anomaly-detector retrain ([eb60a41](https://github.com/core0-io/pythia/commit/eb60a4172d9d26acf072ec045f76513a68c7c8ab))

### Bug Fixes
* **anomaly-detector:** calculation of total batches in log volume ([11512d3](https://github.com/core0-io/pythia/commit/11512d3a9face8ee18544b2058deabba7c0bb35d))
* **anomaly-detector:** use trained-date instead of trained ([c1dff56](https://github.com/core0-io/pythia/commit/c1dff56169eb67eac56d8083021149a779004914))
* **anomaly-detector:** dissoc scheduled train date after training ([a6d54e1](https://github.com/core0-io/pythia/commit/a6d54e1e4ae484e5cff1f65f646bc247e7925dd2))

## [1.9.2](https://github.com/core0-io/pythia/compare/1.9.1...1.9.2) - 2024-10-21

### Bug Fixes
* revert to prism 1.2.0 ([f256c73](https://github.com/core0-io/pythia/commit/f256c733c0d9b823ecc3e7f4f7ac311348c50350))

## [1.9.1](https://github.com/core0-io/pythia/compare/1.9.0...1.9.1) - 2024-10-18

## [1.9.0](https://github.com/core0-io/pythia/compare/1.8.2...1.9.0) - 2024-10-09

### Features
* **anomaly-detector:** 7 days training ([23ea6a7](https://github.com/core0-io/pythia/commit/23ea6a7bab61e171781db3121cf99c11ada38d66))

### Bug Fixes
* **anomaly-detector:** lint ([8d4bd41](https://github.com/core0-io/pythia/commit/8d4bd416b0a6206c2903f28925c667b6fe9d3d78))
* **anomaly-detector:** read 7 days file during retraining ([8a6cbd4](https://github.com/core0-io/pythia/commit/8a6cbd4531d97031996c8340f5eb985228559622))
* **anomaly-detector:** assign scheduled-train-date when retrain ([783f060](https://github.com/core0-io/pythia/commit/783f060cde49fbfc6a03feb67747ff3362112678))
* create physical_table manually and remove ttl temporary ([a2fe1a9](https://github.com/core0-io/pythia/commit/a2fe1a923f5c0d8f47492e28bdb08dca5c31abe7))
* **anomaly-detector:** preprocess reader _is_train typo ([1551678](https://github.com/core0-io/pythia/commit/155167884f0f2641c41b9e1c4cc83236eedc1d7e))
* **anomaly-detector:** only add is-preprocess true if next-run not equal scheduled-train-date ([27070ef](https://github.com/core0-io/pythia/commit/27070efe5090b9747a16bed4698abd20d7290407))
* **anomaly-detector:** ensure to use retrained date one the next day ([db4e6a1](https://github.com/core0-io/pythia/commit/db4e6a1f65f441091ccf8e1994bbd6e67db391e9))
* add http2 dep ([98ffa6a](https://github.com/core0-io/pythia/commit/98ffa6af2f08661d98c640ee563f75124e55bac1))
* add reitit deps for prism ([460fc46](https://github.com/core0-io/pythia/commit/460fc46d84e41d1bb09dfb3a9d89ca5232afa1d2))
* **prophecy:** disable prophet trend by growth=flat ([01b6e1e](https://github.com/core0-io/pythia/commit/01b6e1edbe1bf0bdb97348be74a5efc31ba87fec))
* **anomaly-detector:** skip load model schema in retraining ([2233686](https://github.com/core0-io/pythia/commit/2233686fdb6a2182125b6008acc8759e67cb4412))
* add support for 7 days training ([fc61682](https://github.com/core0-io/pythia/commit/fc61682b3a7187f8799c314193e67a213474d8ce))
* typo ([3c41c01](https://github.com/core0-io/pythia/commit/3c41c01e4e6f561d91063b67be099e2311d0b741))
* return parquet files list ([a4f667e](https://github.com/core0-io/pythia/commit/a4f667e723287df3f941a66ab28e6e9a6b403d09))
* the truth value of a DataFrame is ambiguous ([616beeb](https://github.com/core0-io/pythia/commit/616beeb4f9d683bbcc7eb4e3a175dea170c7ee45))
* skip sequence if worst df failed ([564d727](https://github.com/core0-io/pythia/commit/564d7273f28b2c8a2e1326be739d827af80ab2c9))
* **anomaly-detector:** skip worst df ([faec35b](https://github.com/core0-io/pythia/commit/faec35bb0299044aed31f9031fcfd77e28090d58))
* add log for log rate ([98c0256](https://github.com/core0-io/pythia/commit/98c0256e72a06f4646bbe6a65c7702a84f084a16))
* **prophecy:** train past 14 days instead of 30 days ([70e9440](https://github.com/core0-io/pythia/commit/70e944021b11fa6468b666688e8295992f269711))

### Performance Improvements
* limit number of stored errors for a job ([f4b4508](https://github.com/core0-io/pythia/commit/f4b450826088926bf8de3cab3d85e765c2c4abf9))

### Other Changes
* use prism http server ([e1a3c82](https://github.com/core0-io/pythia/commit/e1a3c8289f017dd3db686b34cc86fd198b390973))

## [1.8.2](https://github.com/core0-io/pythia/compare/1.8.1...1.8.2) - 2024-09-24

### Bug Fixes
* **anomaly-detector:** save log rate report in minio as cache ([2491952](https://github.com/core0-io/pythia/commit/2491952f72c7f0b9569bf62f95054321e719a911))

## [1.8.1](https://github.com/core0-io/pythia/compare/1.8.0...1.8.1) - 2024-09-19

### Bug Fixes
* **anomaly-detector:** avoid updating the start date ([8d2d7ce](https://github.com/core0-io/pythia/commit/8d2d7cea490656a9c709b793f427bbaf0603b41f))

## [1.8.0](https://github.com/core0-io/pythia/compare/1.7.0...1.8.0) - 2024-09-02

### Features
* retrain prophecy (#110) ([1b795ed](https://github.com/core0-io/pythia/commit/1b795ed75efe1c19c0de237debc4fd14ff71dc34))

## [1.7.0](https://github.com/core0-io/pythia/compare/1.6.0...1.7.0) - 2024-08-23

### Features
* support adjust log volume during inference ([a379b43](https://github.com/core0-io/pythia/commit/a379b4381f06aa2c55d465df2db0929e876cada5))

## [1.6.0](https://github.com/core0-io/pythia/compare/1.5.1...1.6.0) - 2024-08-23

### Features
* reuse existing parquet file during training ([bf9c8ea](https://github.com/core0-io/pythia/commit/bf9c8ea90e04b5f9a706ed5e1497037122ada4c6))

## [1.5.1](https://github.com/core0-io/pythia/compare/1.5.0...1.5.1) - 2024-08-23

### Bug Fixes
* reduce dataset size for trainining ([f48c562](https://github.com/core0-io/pythia/commit/f48c56285de0e0b1ed857b8fdfe754deaff4b51e))

## [1.5.0](https://github.com/core0-io/pythia/compare/1.4.0...1.5.0) - 2024-08-20

### Features
* add baseline context to root cause summary ([a816a40](https://github.com/core0-io/pythia/commit/a816a40fcbed7d94059a0590c4a850bc39c23498))
* add log rate log sample ([07a1858](https://github.com/core0-io/pythia/commit/07a1858e41171def6245487355216b90fafcb58f))

## [1.4.0](https://github.com/core0-io/pythia/compare/1.3.7...1.4.0) - 2024-08-15

### Features
* add baseline context ([2a3b2e7](https://github.com/core0-io/pythia/commit/2a3b2e7bce9cf6b79384ee91faead1c215265e7f))
* multi-step prompts for log sequences and structure ([ee3da86](https://github.com/core0-io/pythia/commit/ee3da867ff5c31aff26b7c399a3d446fe56e47d9))

### Bug Fixes
* format import order ([6c358c7](https://github.com/core0-io/pythia/commit/6c358c76c539b2521bb063a0234b501739f62375))

## [1.3.7](https://github.com/core0-io/pythia/compare/1.3.6...1.3.7) - 2024-08-05

### Bug Fixes
* **anomaly-detector:** reset index before get valume column ([f8330d1](https://github.com/core0-io/pythia/commit/f8330d1978264f511ce91954a2d3cbda934be77e))

## [1.3.6](https://github.com/core0-io/pythia/compare/1.3.5...1.3.6) - 2024-07-31

### Bug Fixes
* reset_index before to_dict() in jsd calculation ([ae6698a](https://github.com/core0-io/pythia/commit/ae6698a2ccd3d1b7a03caa45a1a533a75557315c))

## [1.3.5](https://github.com/core0-io/pythia/compare/1.3.4...1.3.5) - 2024-07-30

### Bug Fixes
* change to 4o-mini for contextual ranking ([422980e](https://github.com/core0-io/pythia/commit/422980e436eef7851c664bebb79ddfb9d556463f))

## [1.3.4](https://github.com/core0-io/pythia/compare/1.3.3...1.3.4) - 2024-07-18

## [1.3.3](https://github.com/core0-io/pythia/compare/1.3.1...1.3.3) - 2024-07-17

### Bug Fixes
* update prism ([5102eb4](https://github.com/core0-io/pythia/commit/5102eb43943511c47ec980a66a4eb0f6a947a30f))

## [1.3.1](https://github.com/core0-io/pythia/compare/1.3.0...1.3.1) - 2024-07-17

## [1.3.0](https://github.com/core0-io/pythia/compare/1.2.4...1.3.0) - 2024-07-16

### Features
* add japanese advisory ([e55e721](https://github.com/core0-io/pythia/commit/e55e721acaee625c6311b68c0c730fb66dc98f07))
* add lang to get advisory ([d475878](https://github.com/core0-io/pythia/commit/d4758786f8a67a312e8d68795eab467defa2adcc))
* **anomaly-detector:** update schema ([ae1a0d8](https://github.com/core0-io/pythia/commit/ae1a0d86afa93332c893d5701c8677d07fae671d))

### Bug Fixes
* compatible with legacy schema ([1058ff9](https://github.com/core0-io/pythia/commit/1058ff9aa5afd76323aff78d93f8ec6b3dfeafab))
* update the geo fields schema ([17fee23](https://github.com/core0-io/pythia/commit/17fee232f0a67a8d815a9eb3a407731477dc0de7))
* panel analysis temperature to 0.5 ([027d0b5](https://github.com/core0-io/pythia/commit/027d0b5236086276093affc23269600ec067b5bf))
* lang defaults to "en" ([6a7ef47](https://github.com/core0-io/pythia/commit/6a7ef47f5cd8576dbefadbab4bec306253cc302d))
* drain overall samples ([06308f3](https://github.com/core0-io/pythia/commit/06308f34d511a899d002f69bc3ae47a91d7bdc4d))
* **anomaly-detector:** flatten nested objects with [] ([b318c99](https://github.com/core0-io/pythia/commit/b318c9928cb2007cc63a2c6ccb0232ea068029bf))

## [1.2.4](https://github.com/core0-io/pythia/compare/1.2.3...1.2.4) - 2024-06-05

### Bug Fixes
* **anomaly-detector:** add more context fields for inference logger ([e1d2245](https://github.com/core0-io/pythia/commit/e1d2245c5f2d60f953c138d96ed63bf014df2538))
* **anomaly-detector:** change to warn level for "field not found" ([c166279](https://github.com/core0-io/pythia/commit/c1662790c5f1567baaae842bf5424405d075bfce))
* **anomaly-detector:** cast int64 to double manually ([ee50ed1](https://github.com/core0-io/pythia/commit/ee50ed1fd690d927757b04f23d9fbcf91776f011))
* **anomaly-detector:** load log samples manually ([f31815b](https://github.com/core0-io/pythia/commit/f31815b181942e49e38ed08cd7d12174172ba61e))

## [1.2.3](https://github.com/core0-io/pythia/compare/1.2.2...1.2.3) - 2024-06-03

### Bug Fixes
* **anomaly-detector:** add lru cache for extract ip ([8bc3de5](https://github.com/core0-io/pythia/commit/8bc3de51a41094a08fe80dcfcc6daf7fcd002bf8))
* **anomaly-detector:** add lru cache for extract ip ([35c04d6](https://github.com/core0-io/pythia/commit/35c04d695dd0066b9af64129f0945d458be1e80e))
* increase the inference duration ([818eae6](https://github.com/core0-io/pythia/commit/818eae6630e0d23c5b85e5915091006aa2f53801))
* **llm:** optimise llm prompt for new discrete fields ([25c60d0](https://github.com/core0-io/pythia/commit/25c60d018ae15e9718157cd1c4a38d6b2b200279))
* **anomaly-detector:** log retriever is incorrect when using log volume profile ([e2e2315](https://github.com/core0-io/pythia/commit/e2e23155138c2537e31fca8a47957b4c3d3fde1a))

## [1.2.2](https://github.com/core0-io/pythia/compare/1.2.1...1.2.2) - 2024-05-31

### Bug Fixes
* improve log rate advisory output ([570365d](https://github.com/core0-io/pythia/commit/570365d2ef0adacb1bea43dcb62a42ff678f956b))
* increase the cost of 10min report to 30 because of mem usage ([2da3dc3](https://github.com/core0-io/pythia/commit/2da3dc352e0e59a09724acdac3350fbd745f1802))
* **anomaly-detector:** improve log rate graph and prompt ([55d6795](https://github.com/core0-io/pythia/commit/55d6795f1162b6e92a3a0e5f3258a60dd6b5a0f6))
* use ex-cause to get the exception from CompletionException ([aa03b8b](https://github.com/core0-io/pythia/commit/aa03b8be5fac04afbefbb1700f8c5cc1379093ae))
* **anomaly-detector:** skip unnecessary step for 10min inference ([4d27b25](https://github.com/core0-io/pythia/commit/4d27b2587eb20a5ed4fd337599af3a41d1e741b5))

## [1.2.1](https://github.com/core0-io/pythia/compare/1.2.0...1.2.1) - 2024-05-30

### Bug Fixes
* **anomaly-detector:** read numeric columns from original df ([b05efb6](https://github.com/core0-io/pythia/commit/b05efb66c2aaad71fdbfd58effdc3cdf31761ed2))

## [1.2.0](https://github.com/core0-io/pythia/compare/1.1.2...1.2.0) - 2024-05-30

### Features
* **anomaly-detector:** add summary advisory ([288a612](https://github.com/core0-io/pythia/commit/288a61280754920c3754ff52aad57642f5eadf73))
* panel advisory ([6b018f6](https://github.com/core0-io/pythia/commit/6b018f61862cd636331ee292fd3e02dcafce5a79))
* **llm:** count token and limit log lines ([b986ed9](https://github.com/core0-io/pythia/commit/b986ed9e134866f6c53db30f9b1f53bf92a67f4a))
* contextual ranking for log sequences and structures (#84) ([46d26fd](https://github.com/core0-io/pythia/commit/46d26fddc1480b5d678cc1d59e4a0ff807c870eb))
* sort log samples with matched keyword count ([b605052](https://github.com/core0-io/pythia/commit/b60505205be171fd0df4a75cf37b5b2885515974))

### Bug Fixes
* **llm:** tweak log rate prompt ([87e3fbf](https://github.com/core0-io/pythia/commit/87e3fbff29072eb2df8a68f8342064e408c9dc61))
* **llm:** tweak log rate prompts ([1c2d524](https://github.com/core0-io/pythia/commit/1c2d524c944fec3a7213f74d7e1d1ec3a24bd3a8))
* multi rounds for log rate advisory ([fff5eb4](https://github.com/core0-io/pythia/commit/fff5eb45232e322e589c443cdb0de1871f9e60e0))
* each log structure only return 10 samples ([fa6afea](https://github.com/core0-io/pythia/commit/fa6afea104ffe08c76bdcbc94cc5074d8acf3abf))
* add greptime db to config edn ([dde47e4](https://github.com/core0-io/pythia/commit/dde47e48e7c57945aca6ae560d93b7283fe885ef))
* **pythia:** delete objects from minio recursively ([5cc9a78](https://github.com/core0-io/pythia/commit/5cc9a78864834533fdc5000b1fcc5610671af6e3))
* **prophecy:** execute batch usage (#86) ([9b07736](https://github.com/core0-io/pythia/commit/9b077365404fe68f7c78c3fc8ca9466258f94fa3))
* avoid nested transaction (#85) ([f36be4c](https://github.com/core0-io/pythia/commit/f36be4cbb0cda87b0b222baba074e456663d3f26))
* remove unused print ([95afd4b](https://github.com/core0-io/pythia/commit/95afd4b8ba231efee905f01738ac658bb16f1840))
* post run cleanup when job failed ([98cdfcd](https://github.com/core0-io/pythia/commit/98cdfcd426ee80cffc678e8e068cd76f1348ea56))
* pick the samples from template first ([ece2e28](https://github.com/core0-io/pythia/commit/ece2e28c8ee98d8713312e32fa576c3dee592fc3))
* decrease the gpt-4o token limit to 10k ([0318ea6](https://github.com/core0-io/pythia/commit/0318ea6a4600bb4cb94a56077dbedbb42668e4e1))
* use Model instead of string ([b295ef7](https://github.com/core0-io/pythia/commit/b295ef7b15182f8a8772a7d2866f9d768f0b0f8d))
* add missing hidden import for tiktoken ([fa7c2e7](https://github.com/core0-io/pythia/commit/fa7c2e7760f6373808ebfd82dc46f79d7d593ad6))
* **anomaly-detector:** improve prompt and sort the ranking result ([5ce6dc8](https://github.com/core0-io/pythia/commit/5ce6dc8b666fd6893c071555d7e358c8e1a3d315))
* contextual_ranking is optional for drain ([5d978ee](https://github.com/core0-io/pythia/commit/5d978eed8cb486edbb33e925a1fd4aad4105fab6))
* contextual ranking only daily ([acce3cc](https://github.com/core0-io/pythia/commit/acce3ccce818242d3a081b939e10823e4bd51b2a))
* add job-threads config ([eb4c169](https://github.com/core0-io/pythia/commit/eb4c16957d8363e315c025d9ac16c5ad1ff2f577))
* typo ([6fe215c](https://github.com/core0-io/pythia/commit/6fe215c6d210d2b960cc32e2e41bef39678979fb))
* filter the templates that not in samples ([c4f401e](https://github.com/core0-io/pythia/commit/c4f401ee28d588e500e8669d427b7c1b5c7af2ab))
* **anomaly-detector:** collect samples from inference and trained templates ([9a7086e](https://github.com/core0-io/pythia/commit/9a7086e08b6e855eeb3a9bf1abce7a1926b248bb))
* reverse the template order ([a1f149b](https://github.com/core0-io/pythia/commit/a1f149b9cacb4db7e44bf3c3e5f11b6342f1b914))
* update the overall samples structure for log structure ([ab281e5](https://github.com/core0-io/pythia/commit/ab281e5dd3730d24e6853efb4a72b614a3dc3e05))
* **anomaly-detector:** expected can be zero ([44e5621](https://github.com/core0-io/pythia/commit/44e562129254ef9b21479bdfc1ce0b87c446997e))
* **anomaly-detector:** template_count can be zero ([9850e81](https://github.com/core0-io/pythia/commit/9850e81496555a3006404ae38bfed47a28fae8de))
* improve overall samples for log structure ([b038c17](https://github.com/core0-io/pythia/commit/b038c17bbb7eed38f78f88fd40cb22551dd449db))
* sequences logs ([509e0fe](https://github.com/core0-io/pythia/commit/509e0fe7a0119a6c0b5365db8d85f7178d957cda))
* organise keywords to partial and whole ([7b3d73e](https://github.com/core0-io/pythia/commit/7b3d73ed104e941db5eb3bf7c305b4f960a9fd20))
* **anomaly-detector:** typo for numeric fields ([bb7971d](https://github.com/core0-io/pythia/commit/bb7971de7a73eab8561258ddef1aec92e11dbf75))
* improve jsd calculation ([fbb5e32](https://github.com/core0-io/pythia/commit/fbb5e3211e1db0d88c9464a8fe858913f138ce02))
* typo of discrete values ([c5b9770](https://github.com/core0-io/pythia/commit/c5b97704c6347f0bf64ea7dd9f4e241d96fd34f6))
* set outliers to empty rather than series ([eaedc2a](https://github.com/core0-io/pythia/commit/eaedc2a2f8be6beca73c436cf80caf96ef1f5ed0))
* **compile:** add hidden import scipy ([46bd1f7](https://github.com/core0-io/pythia/commit/46bd1f7dc10f6683966618d28696d8b5629f6419))

### Other Changes
* use prism (#82) ([4c0c2ff](https://github.com/core0-io/pythia/commit/4c0c2fff17643129f2e51ad69929530190ecf6f3))
* **prophecies:** use batch execution for table creation/deletion ([ea9ca4c](https://github.com/core0-io/pythia/commit/ea9ca4ca10a80e1dc36aaaf74e14afb8815569d3))
* move out log sample persist ([6421014](https://github.com/core0-io/pythia/commit/6421014048831f52734679d13bbb1bf6b15fa801))

## [1.1.2](https://github.com/core0-io/pythia/compare/1.1.1...1.1.2) - 2024-04-30

### Bug Fixes
* enable fp16, decrease training batch size, limit volume profile to 10g, decrease record batch size, increase training maximum duration ([fc6785f](https://github.com/core0-io/pythia/commit/fc6785f059bca28202302a103125b514d80108bb))
* reflection, offsetting ([30f8545](https://github.com/core0-io/pythia/commit/30f8545cb9ff3465a9cf8822a941bdac14f0ce55))
* offset training window by 5 minutes to avoid considering data that hasn't been received yet ([539f85f](https://github.com/core0-io/pythia/commit/539f85ff0b47782939216ff086a60e8cc0755d34))
* set score to zero if no rows for numeric fields ([e16a8b0](https://github.com/core0-io/pythia/commit/e16a8b098dd291b195d70b709e1fb3de714e8113))

## [1.1.1](https://github.com/core0-io/pythia/compare/1.1.0...1.1.1) - 2024-04-05

## [1.1.0](https://github.com/core0-io/pythia/compare/1.0.5...1.1.0) - 2024-04-04

### Features
* add skip-public-holiday and initial-trained-date (#68) ([7a32e81](https://github.com/core0-io/pythia/commit/7a32e81458c925f1b34d3f3a57a4cb5b5def6506))

### Bug Fixes
* **log-rate:** fill 0 if no data points when calculate log rate (#69) ([3b78ccf](https://github.com/core0-io/pythia/commit/3b78ccfa3d0d07e06318d5ff1698dc6154f07c70))
* remove holiday setting check ([e1a5610](https://github.com/core0-io/pythia/commit/e1a56108138330db25e383a4067a8f7c86897c71))

## [1.0.5](https://github.com/core0-io/pythia/compare/1.0.4...1.0.5) - 2024-03-26

### Bug Fixes
* if the field exists in the trained histogram calculate jsd anyway ([3de81d1](https://github.com/core0-io/pythia/commit/3de81d1b8b94e2268ffe894064e4945f8073fbec))

## [1.0.4](https://github.com/core0-io/pythia/compare/1.0.3...1.0.4) - 2024-03-26

### Bug Fixes
* filter out the discrete fields with low cardinality ([1031007](https://github.com/core0-io/pythia/commit/103100795885b1ae738a7b895a97104feaa83e4f))
* the score of missing or new fields in discrete value should be 1 ([f959a4b](https://github.com/core0-io/pythia/commit/f959a4baaaeac308cae03c4b4f7ae7bd78660405))

## [1.0.3](https://github.com/core0-io/pythia/compare/1.0.2...1.0.3) - 2024-03-26

### Bug Fixes
* remove the fields with low cardinality when calculate notable fields ([f4243d9](https://github.com/core0-io/pythia/commit/f4243d9833e226857c28b476ad406572a02a572f))
* histogram jsd keep the other fields in inference ([410451c](https://github.com/core0-io/pythia/commit/410451c7bdf64a481e2c191e90a06e8b4a0e4704))
* fix unnecessary error coercion ([6ccad1a](https://github.com/core0-io/pythia/commit/6ccad1a7f868854f2f57c810618699292f994e02))

## [1.0.2](https://github.com/core0-io/pythia/compare/1.0.1...1.0.2) - 2024-03-21

### Bug Fixes
* keep template_samples if not skip_drain ([dc54efe](https://github.com/core0-io/pythia/commit/dc54efe7b3c0c0cd76757e61aca5f8ae9be8c2f0))

## [1.0.1](https://github.com/core0-io/pythia/compare/1.0.0...1.0.1) - 2024-03-20

### Bug Fixes
* fix serialising of certain exception types ([a0c8269](https://github.com/core0-io/pythia/commit/a0c826912db7feb5e04c56576689a6a7b0cf95c7))
* update retrained-date calculation ([d26f125](https://github.com/core0-io/pythia/commit/d26f125f9c8a73c6a1691a0a500a82ec0587d45c))
* retrained-date should be parsed to inst ([ccc9412](https://github.com/core0-io/pythia/commit/ccc9412980861c2f2c48871af58f4c711fce1cb1))

## [1.0.0](https://github.com/core0-io/pythia/compare/33f25efdc32e98c534e93f898582177f751b7d62...1.0.0) - 2024-03-20

### Features
* **anomaly-detector:** collect accounting metrics (#53) ([f4ca1a5](https://github.com/core0-io/pythia/commit/f4ca1a539f0f36b4f09ad953d7ee7ddb9bc12329))
* **anomaly-detector:** add scheduled train date to status ([bea246d](https://github.com/core0-io/pythia/commit/bea246dcfe0b1f4ddbe0bacb51def65ebdfcecb8))
* **anomaly-detector:** add log rate to inference (#40) ([cbc2f05](https://github.com/core0-io/pythia/commit/cbc2f05f36a50c41d828af02b5663b3015ebeaa3))
* **anomaly-detector:** filter metrics by full or 10m (#41) ([9979a8a](https://github.com/core0-io/pythia/commit/9979a8a5f22fb7e3c53af515665605218bef5e86))
* **anomaly-detector:** inject sagemaker environment variables (#36) ([2a2984a](https://github.com/core0-io/pythia/commit/2a2984ac06b16958d0bd3d769f26ac796e7a10df))
* **anomaly-detector:** parallel greptime metrics requests (#34) ([bcb2dfe](https://github.com/core0-io/pythia/commit/bcb2dfe8edbced71bf8ff24e3fc444b4d3c39026))
* split log stream of python scripts to stdout and stderr (#29) ([d676942](https://github.com/core0-io/pythia/commit/d6769426172f4ffaa32fe851b1a540f007ba9907))
* support retrain anomaly detector and 10min inference (#24) ([6c21fd1](https://github.com/core0-io/pythia/commit/6c21fd14492f64d85327adfeebb3dd1cd4235740))
* support skip public holiday ([4a18e5e](https://github.com/core0-io/pythia/commit/4a18e5e4cffe2d103105f9174131e7d663097032))
* Custom prophecy holidays (#19) ([8a16f02](https://github.com/core0-io/pythia/commit/8a16f02ba360a11e7fe31389d73ec0b44e4aa1e0))
* add log-anomaly creation/deletion endpoints ([4fd7951](https://github.com/core0-io/pythia/commit/4fd7951046d4bb7c200f34145225c6f4cf05de5c))
* add query endpoint to avoid querying ingester after flush ([c280360](https://github.com/core0-io/pythia/commit/c280360516bb75e3a0b6bb48d46a9ad51fc0a5c6))
* **logbert:** inference candidate_size is decided by the vocab size ([ff8085d](https://github.com/core0-io/pythia/commit/ff8085dd9ca90d99f6043877d7a22c5f30fe49b2))
* add support for log anomaly jobs ([6445aeb](https://github.com/core0-io/pythia/commit/6445aeb80adbcb2b55d62651088667f4afdfaf61))
* anomaly detector and refactor the command line (#11) ([fdb58da](https://github.com/core0-io/pythia/commit/fdb58dada04c8b49dd83fb5564c32cd0ada16dd4))
* prelim prophet service scripts (#1) ([69469ea](https://github.com/core0-io/pythia/commit/69469eae15324235ea1e13dd619c5c3f1062abfb))

### Bug Fixes
* **anomaly-detector:** seperate inference and train staging path ([d660918](https://github.com/core0-io/pythia/commit/d66091894d1ebad330c232bf7fea8cbd6bcd78a8))
* **anomaly-detector:** log sequences can be empty when full ([bbaa2ed](https://github.com/core0-io/pythia/commit/bbaa2edce8d9e9b81c12906e8e194201ce9ce10f))
* **anomaly-detector:** skip drain ([cf2b37c](https://github.com/core0-io/pythia/commit/cf2b37c5e6f22de3789020dd52a31f6d67453d98))
* **anomaly-detector:** the inference should use the retrained model on the day after training (#64) ([aa6a0da](https://github.com/core0-io/pythia/commit/aa6a0da52472d79708427fff25e705aabcac6c3e))
* **anomaly-detector:** should skip typo ([3537ead](https://github.com/core0-io/pythia/commit/3537ead4effd803c853ef80b45f1efe5e1735874))
* **anomaly-detector:** typo of force save state ([787ad22](https://github.com/core0-io/pythia/commit/787ad22d650f9b8f6f8398ab0a20978756b8bd46))
* increase the max drain cluster count to 2048 ([2c56d37](https://github.com/core0-io/pythia/commit/2c56d375b8e28f377947e70cdf65eaa3918fd8e6))
* enable accounting collector ([1a9a761](https://github.com/core0-io/pythia/commit/1a9a7613db994372027a182755d75dbabf7fb8d6))
* **anomaly-detector:** skip drain templating and logbert if more than 2000 templates ([8973e00](https://github.com/core0-io/pythia/commit/8973e00b1123720604efa936534cdad8f143f117))
* destroy process when job timed-out (#63) ([9cd898d](https://github.com/core0-io/pythia/commit/9cd898dee4313de674f9a92ca91b714daa802032))
* indent ([ae22be4](https://github.com/core0-io/pythia/commit/ae22be4e8ff6314349698001b15c34ee22658ba5))
* remove greptime zero values hotfix ([8b8109a](https://github.com/core0-io/pythia/commit/8b8109a8a0540214776eee16d19e0e504323ae5c))
* **anomaly-detector:** the second parameter of slice should be length ([4079dff](https://github.com/core0-io/pythia/commit/4079dff438a81e128795c969b1f53bc74893518b))
* **anomaly-detector:** use sql to override incorrect zero values after writing (#59) ([fd0c4e4](https://github.com/core0-io/pythia/commit/fd0c4e474ae928e28487b5fed8822ce8c1b93205))
* **anomaly-metrics:** put accounting data as json ([46866da](https://github.com/core0-io/pythia/commit/46866da461f579230b1658882070cc356082b7f1))
* remove accidentally left in change for testing ([691f424](https://github.com/core0-io/pythia/commit/691f424da4772f1a3ca290ace8da0be184da872e))
* handle ConnectionErrors as ServiceErrors ([b019765](https://github.com/core0-io/pythia/commit/b0197651a1063fd351ab0c5fad60f77b2db4ec0d))
* replace deprecated 'promote' option ([9b1e0b0](https://github.com/core0-io/pythia/commit/9b1e0b0d241bc6eaa03a2e7a11528f044d307d9b))
* use s instead of deprecated S to indicate pandas frequency ([a6c46d5](https://github.com/core0-io/pythia/commit/a6c46d5c8e537f79c0b2f2059a4f6e69eb304a64))
* fix python log level syntax ([89d5fc7](https://github.com/core0-io/pythia/commit/89d5fc763087a67364957b0baec1614e31352572))
* invalid Timbre logging level ([d51503e](https://github.com/core0-io/pythia/commit/d51503e8ab7ad769efa2e542290ed00207af640d))
* temporarily disable writing accounting metrics ([fc8d069](https://github.com/core0-io/pythia/commit/fc8d069f498405a6483c984cb3066aab7d7f23b9))
* raise ServiceError when prom write failed ([b6b1032](https://github.com/core0-io/pythia/commit/b6b10322c0634742ca36c649f0d8087a9a7b8da8))
* **anomaly-detector:** use fromtimestamp instead of utcfromtimestamp ([b55d111](https://github.com/core0-io/pythia/commit/b55d111f91115fbc23b402b2428ff3f6efc4a810))
* method put to save accounting metrics ([29dfb40](https://github.com/core0-io/pythia/commit/29dfb40acdd1fa86628992cef4c38e99e3f3a936))
* **anomaly-detector:** add retry and raise ServiceError when failed to retrieve logs from loki ([c7af561](https://github.com/core0-io/pythia/commit/c7af5612dc3698fa4a75c3ba17334fd5498374be))
* add missing handle failed ([ee1cd56](https://github.com/core0-io/pythia/commit/ee1cd56f5aed7e55c2dacd437499ebca0e90a198))
* missing accounting collector ([2aae38b](https://github.com/core0-io/pythia/commit/2aae38b0780c3a2c1b8c6d07c99876fa541f98d2))
* **anomaly-detector:** circualr import accounting collector ([94ed4f5](https://github.com/core0-io/pythia/commit/94ed4f5e4a9ef8847328c81437af37265cc0edf8))
* **anomaly-detector:** calculate hdbscan outliers based on inference dataframe rows ([1503436](https://github.com/core0-io/pythia/commit/1503436f74ba14d3cccbce8b470feb9be84d5bc9))
* **greptime:** return nil instead of empty vec when table does not exist ([0bf2d88](https://github.com/core0-io/pythia/commit/0bf2d88cc167c4c0bd20d7f0720ab7341f673e30))
* **anomaly-detector:** drop metric tables when delete an anomaly-detector (#49) ([b79f7ed](https://github.com/core0-io/pythia/commit/b79f7eda41bcefe09728d5715e3c4470af96ae22))
* set inference jobs to failed if training failed (#50) ([95e3d60](https://github.com/core0-io/pythia/commit/95e3d60b3aab82514ede63cc7eb9f4e4cea56536))
* **anomaly-detector:** set next-run to the next-day at 00:00 in tenant's timezone (#48) ([7e7db64](https://github.com/core0-io/pythia/commit/7e7db647e49bb5cbce3bfbabd506f4df9efdc13d))
* **anomaly-detector:** add retrain-date to status (#47) ([60710ca](https://github.com/core0-io/pythia/commit/60710ca2ca1a1fb01986698c6644532d715bbb2e))
* **anomaly-detector:** new tokens set can be empty ([be78e58](https://github.com/core0-io/pythia/commit/be78e58a30b9796653e05e65f94fc072488c6fd1))
* **anomaly-detector:** ensure logs are in the time range ([0277597](https://github.com/core0-io/pythia/commit/0277597d5bd70dd5c900901e3c47efab9e8e8cc2))
* **anomaly-detector:** remove special keys in top fields ([2b25954](https://github.com/core0-io/pythia/commit/2b259540ff5673c687c748f389206261704fcec7))
* **anomaly-detector:** metric should include the end timestamp ([9939628](https://github.com/core0-io/pythia/commit/99396287d1b740c741776fce8f504c5037e1c993))
* **anomaly-detector:** log rate score can be zero ([40c97c8](https://github.com/core0-io/pythia/commit/40c97c8a02a7b2c28165b03bae628251284fcc23))
* log rate can be empty ([7ae8bc2](https://github.com/core0-io/pythia/commit/7ae8bc2a42b72ddac47e3580458b351e3116248e))
* lower period-ms ([8c92ace](https://github.com/core0-io/pythia/commit/8c92ace9b77bb1c18efebd2cd040216eef15fd93))
* support 10m and full day parquet file format ([5797cef](https://github.com/core0-io/pythia/commit/5797cef0aa36b8dafe3d7b334c90bf5579cd2931))
* **anomaly-detector:** parquet file name during inference ([79b8662](https://github.com/core0-io/pythia/commit/79b8662ee4ea95a025188a499f6c3b1861814af8))
* prophecy-id requirement ([0f38970](https://github.com/core0-io/pythia/commit/0f3897011a878d6f79b339c73fbdcea69c70b8b7))
* log rate calculation ([73f2074](https://github.com/core0-io/pythia/commit/73f2074a9cfa56d86ad69d3e4f5b6fc3fb86e7cb))
* **anomaly-detector:** don't resume from checkpoint when training ([0284de1](https://github.com/core0-io/pythia/commit/0284de11e51d840f76eb8c29ef991a990491d961))
* **anomaly-detector:** log rate band is 3 sigma ([d0d2ad7](https://github.com/core0-io/pythia/commit/d0d2ad7917edec92ce2de6d2ab963f67653d18ec))
* **anomaly-detector:** inference field occurrence ([04530d6](https://github.com/core0-io/pythia/commit/04530d6cca56e91a808a69705f26920226260cee))
* **anomaly-detectors:** remove unused token and calculate geo field ([9f936cd](https://github.com/core0-io/pythia/commit/9f936cdd6f9723d21c9ba94d12ff692070cf30cc))
* **anomaly-detector:** empty log rate ([a1e7b65](https://github.com/core0-io/pythia/commit/a1e7b65ca053e1e3d62f3067b5774216e505fa34))
* **anomaly-detector:** calculate fields occurrence during preprocessing (#44) ([cee8b32](https://github.com/core0-io/pythia/commit/cee8b32715ec91ac2d217a308a2a9b847b0aeeea))
* **anomaly-detector:** remove geolocation@city ([2ae4674](https://github.com/core0-io/pythia/commit/2ae46742f78c7e9e1829d323cdab7e6fcbdf153e))
* **anomaly-detector:** prophecy id to snake case ([b634fbd](https://github.com/core0-io/pythia/commit/b634fbd6efac3c93feb41e3dc0748dfea4a9df6a))
* **anomaly-detector:** prophecy id ([b1f6063](https://github.com/core0-io/pythia/commit/b1f6063cff7190e7d7c39c51fb88e0923f734d1e))
* **anomaly-detector:** exit 0 when no logs during inference ([00ab39e](https://github.com/core0-io/pythia/commit/00ab39ecd612ede90be9c78945f6434855c31d30))
* **anomaly-detector:** add label "inference" for metrics ([36c79f3](https://github.com/core0-io/pythia/commit/36c79f3fd29ed03e2ae4a943686c69a46e619611))
* allow empty sub queries (#39) ([7f28562](https://github.com/core0-io/pythia/commit/7f28562aafca885d8352ae05bc23a1e1c60d5d12))
* **anomaly-detector:** rename aws_region to sagemaker_Aws_region ([ac453c1](https://github.com/core0-io/pythia/commit/ac453c15142e6aff8cb4432fdcbb3da307b0e3d1))
* **anomaly-detector:** return metric numeric_clusters:mean as well ([3a40982](https://github.com/core0-io/pythia/commit/3a409823f820d8f25b40b9f8450e527924534184))
* **anomaly-detector:** short inference interval is 10 min ([db7a391](https://github.com/core0-io/pythia/commit/db7a39113234832f6a96ae6a2d5b81f3b35599b0))
* **anomaly-detector:** greptime env for inference ([c3dca2f](https://github.com/core0-io/pythia/commit/c3dca2f154c3684e7a88f1617dc5798e1adb852a))
* **anomaly-detector:** greptime_url and greptime_db ([bc6ad7f](https://github.com/core0-io/pythia/commit/bc6ad7f3c7f5dbf00c366a0baf1c1713a803abc9))
* **anomaly-detector:** replace mimir with greptime (#33) ([3755c4e](https://github.com/core0-io/pythia/commit/3755c4e560ded2a6fd748ae39375b71a48bd109e))
* fix job fetching ([713a376](https://github.com/core0-io/pythia/commit/713a376e15a786e70271254f66b119c253f04cb8))
* improve job capacity thread safety ([0f62338](https://github.com/core0-io/pythia/commit/0f62338bf26239ab7e9c0603fe43ef769915a133))
* remove use of STM ([19a52e8](https://github.com/core0-io/pythia/commit/19a52e84c2f2a4edefcaaf03a00a06787b86ce5d))
* fix job completion arity, txn ([9f37fdf](https://github.com/core0-io/pythia/commit/9f37fdf1e47e7abe6ac5a749a21590f34d9cc646))
* **anomaly-detector:** inference parameters and pyinstall compile (#32) ([4454ac8](https://github.com/core0-io/pythia/commit/4454ac8e4ed908bed12fe0c29e7b48b9c3c0284c))
* add log anomaly training args ([d3df793](https://github.com/core0-io/pythia/commit/d3df793fea78f9e0c5a737031ca8dd36d1ee7707))
* add negative ([593c294](https://github.com/core0-io/pythia/commit/593c29496d6ab680be4e58ef887f5ed9717bb114))
* job cleanup fix ([374eccc](https://github.com/core0-io/pythia/commit/374eccca439b3ecd60e963cf57bf3172efdb32bf))
* **anomaly-detector:** compatible with s3fs and minio ([e8df6a8](https://github.com/core0-io/pythia/commit/e8df6a806f14a55f34348d7cb6f3ef79225aa741))
* **anomaly-detector:** compile and public holiday and metric issue ([34ae5f4](https://github.com/core0-io/pythia/commit/34ae5f41a360037f84f9cc0025418e821a282c0d))
* **anomaly-detector:** resource path ([f12b469](https://github.com/core0-io/pythia/commit/f12b4692a5b1ec5bc429376efbd8c6c6e1465769))
* **anomaly-detector:** resource path ([72fa0e6](https://github.com/core0-io/pythia/commit/72fa0e64e873ee21ecebfb7d049029d936e88973))
* invalidate-service typo and quote "running" ([0f5fb26](https://github.com/core0-io/pythia/commit/0f5fb266c2952a33d8f735211c6bbb1da7aff56c))
* remove protobuf deps ([4121fd2](https://github.com/core0-io/pythia/commit/4121fd2fdcfd660100b61853381a42cf3eef649d))
* let greptime create metric tables, replace table ttl with scheduled deletions ([2896300](https://github.com/core0-io/pythia/commit/2896300a338bf6f69a474e3c89291fe090541ddc))
* set status back to pending after jobs time out or fail ([97f1226](https://github.com/core0-io/pythia/commit/97f122659d3e874dda4abda739af283550a7b0fd))
* set physical-metric-table ([88e79d2](https://github.com/core0-io/pythia/commit/88e79d287505d9d64d72be8225659514693ee01a))
* save output logs into db if failed or timed-out (#28) ([8d3ea62](https://github.com/core0-io/pythia/commit/8d3ea62732b9fdfda6597d082f3f46b077bc4182))
* delete LAD minio files within txn ([4df735c](https://github.com/core0-io/pythia/commit/4df735c4adc9f7b5f9b99fdba1a3e1dc2a8ca92a))
* **anomaly-detector:** raise ServiceError instead of RecoverableError when failed from mimir ([6e9e233](https://github.com/core0-io/pythia/commit/6e9e233b26f08105e72172b67fa433a577601603))
* **anomaly-detector:** add last-trained0date if it exists ([1e8c670](https://github.com/core0-io/pythia/commit/1e8c670b2a1b6307c1c233294325d5c51033f4e0))
* GREPTIME_DATABASE -> GREPTIME_DB ([81ab196](https://github.com/core0-io/pythia/commit/81ab1968f568c320bb0a4106c2777fc1bca77439))
* improve logging and error storage ([6cd626e](https://github.com/core0-io/pythia/commit/6cd626e4af198b8a32c624fc39e6cdc0b0c8605d))
* replace datetime.UTC which requires python 3.11 ([9013d03](https://github.com/core0-io/pythia/commit/9013d0311f595966b8d124399ef910d984ece2c7))
* pass tenant-country arg for prophecy training ([0bf6ffa](https://github.com/core0-io/pythia/commit/0bf6ffaa9d3809c82e35c7822296a8dd39afeb59))
* fix imports for prophecy ([ffba0d7](https://github.com/core0-io/pythia/commit/ffba0d72f627b90a444b329f6572fbaf409ef1d5))
* fix imports for command_train ([28bf56c](https://github.com/core0-io/pythia/commit/28bf56c5b821607c0f604b3d3f4d89c4bdb8f238))
* improve error handling, allow python script to handle staging file cleanup ([a251b80](https://github.com/core0-io/pythia/commit/a251b801bea223e7bc42d6a63806c25b5582ed97))
* avoid logging with job key that gets overriden ([778db5a](https://github.com/core0-io/pythia/commit/778db5a2b4bf8bf365a89bb4a8cec19a3bd11d87))
* **greptime:** handle missing code in response ([f7539f7](https://github.com/core0-io/pythia/commit/f7539f7cf10286c0d96c9e5e00e0dbc2e5cf88aa))
* **anomaly-detectors:** metric timestamp is the end time ([c168547](https://github.com/core0-io/pythia/commit/c1685471a385250d6bd17bd9efdddcb3f4588ce4))
* return body as stream ([fbc896d](https://github.com/core0-io/pythia/commit/fbc896d4b1814cfc1f09170d04f3716ac26340c5))
* **anomaly-detectors:** anomaly detector can be trained the next day ([687d7d1](https://github.com/core0-io/pythia/commit/687d7d17aa7d33d55feab3bc021b25b97ef033e5))
* correct ID key on AD creation/deletion ([2849c46](https://github.com/core0-io/pythia/commit/2849c4678231fdecf579a57fcb6c639d1d8823b3))
* handle boto3 client errors ([9d5b96b](https://github.com/core0-io/pythia/commit/9d5b96b4372f2593ce8556a17d4534f9a1e07407))
* fix lint ([ed75521](https://github.com/core0-io/pythia/commit/ed75521603b87729568bb73a1197db3723ff2201))
* change minio bucket env var ([008cc46](https://github.com/core0-io/pythia/commit/008cc46e808a08bccf434a9f5327698d34849ec7))
* increase DB connection lifetime to accomodate long executions ([86fa77b](https://github.com/core0-io/pythia/commit/86fa77b2f6e49f11993acd39f5719091a22323d6))
* **logbert:** load tokenizer before inference on sagemaker ([9c6a4d0](https://github.com/core0-io/pythia/commit/9c6a4d05413dc5c897500814054ce804370f72b5))
* add log anomaly ID arg, delete minio objects on log-anomaly deletion ([a64694c](https://github.com/core0-io/pythia/commit/a64694c4c4af527b65620245bbe76065f3f9b6e5))
* **logbert:** the higher score means worse performance ([e3981b4](https://github.com/core0-io/pythia/commit/e3981b40b33648c82a3e5153475352a9a95132cd))
* sample for hopkins score and added trained date (#13) ([8c9a62b](https://github.com/core0-io/pythia/commit/8c9a62b49a33d941340789c5fd0a32ee7047d87b))
* parse predicted ts before use, minio path fixes ([cb9e995](https://github.com/core0-io/pythia/commit/cb9e9956eb12ff7e08e7f13d835e4b60a13c2801))
* fix reflection ([93d8a39](https://github.com/core0-io/pythia/commit/93d8a397d5d8fe046ad9627bc48c88f12da8a3f8))
* handle prophet/prophecy change ([14d357d](https://github.com/core0-io/pythia/commit/14d357dffc5acca62cc6e42c689d4550e11c8c3d))
* **jobs:** write error string instead of byte array stream to db ([d97c5bd](https://github.com/core0-io/pythia/commit/d97c5bd8a683913da9e89b9c4ac04909db1e06f5))
* fix logging statement on errors ([42d3ed7](https://github.com/core0-io/pythia/commit/42d3ed7386d280f3cb77aa5f3c5c6b87505efe9a))
* **prediction:** handle predicted ts as string ([9b792f1](https://github.com/core0-io/pythia/commit/9b792f11b99b7d4c0aeeaf32cb7b2004b85bf260))
* write stddev of noise to mimir (#9) ([4591224](https://github.com/core0-io/pythia/commit/4591224af8f83dd6a03e48c91b670ed4f03a9c07))
* fix query for determining when to run prediction jobs ([a5e3b1f](https://github.com/core0-io/pythia/commit/a5e3b1f1069ed1f9258ce240686c0ff002dd32a4))
* parse path params on prophecy delete ([61a0a15](https://github.com/core0-io/pythia/commit/61a0a15617ddb4246382fdb65475bf71253fec4f))
* delete relevant minio object when deleting prophecy ([12adfc1](https://github.com/core0-io/pythia/commit/12adfc1f043bd6439b503b5f55ed02bf03ed94f0))
* **jobs:** DB query params on prediction ([43e1e07](https://github.com/core0-io/pythia/commit/43e1e07ebf99eea379bf449bb7b4c5088600d658))
* correct holidays module setup ([819ab48](https://github.com/core0-io/pythia/commit/819ab48a5bff4c111e53a3179acfe055c4f82455))
* add holidays dep ([a2b3fea](https://github.com/core0-io/pythia/commit/a2b3fea77056fd652bbc66cf7a9a32025cf2b7a4))
* **train:** do no fail if query has no data ([7600e9b](https://github.com/core0-io/pythia/commit/7600e9bacde617fbf30509439bcf66753e8219a1))
* pass start/end in seconds instead of ms, cap query concurrency at 4 ([4c03268](https://github.com/core0-io/pythia/commit/4c03268f81ad933ba55c9b1163979a00b1a39377))
* escape crystal-ball args ([df91390](https://github.com/core0-io/pythia/commit/df91390539d1b20107c96ff319164a9e941939da))
* add python deps ([a9a2786](https://github.com/core0-io/pythia/commit/a9a27865feda77d3e3a10ce0e78730f1b768e2cd))
* use named args ([ec833a2](https://github.com/core0-io/pythia/commit/ec833a257b521d4822d124018396781e0114311b))
* correct handling of exited job ([dd3f160](https://github.com/core0-io/pythia/commit/dd3f16089882a1f22eeb251386085f9ae7006703))
* correct bb process response handling ([4959a06](https://github.com/core0-io/pythia/commit/4959a06a686c973b9b98f90bb2c8fd0cf0c3580c))
* fix process args ([c9f25cc](https://github.com/core0-io/pythia/commit/c9f25ccb48592bc5c30c44b81d81c7144edf7cdd))
* executable path/file name ([f4e9967](https://github.com/core0-io/pythia/commit/f4e996765cdf51c75d5cc5c0bc04d7171b2c9bd1))
* **api:** accept data in body instead of query params ([94a4618](https://github.com/core0-io/pythia/commit/94a4618f7b41265c6a2168273383f0ee899bd193))
* **prophet:** batch mimir query (#4) ([aa5d5f4](https://github.com/core0-io/pythia/commit/aa5d5f4d6351664aec30a058e6189a0e0b68d78c))
* CI progress ([c53cec1](https://github.com/core0-io/pythia/commit/c53cec197ac7bbd220f62bbcfd93cdb37dba4755))
* **prophet:** timestamp bug and batch query (#3) ([48778b7](https://github.com/core0-io/pythia/commit/48778b79158c46e440df7e82853c8a1483d41d87))
* update CLI usage, add snappy dep ([a915f0a](https://github.com/core0-io/pythia/commit/a915f0afcba45f557e04b9da06e8929c6429437b))
* fix env vars, fix starting of executor ([4e5d573](https://github.com/core0-io/pythia/commit/4e5d5736fa8a18128c2e49e92ed09722d8b53014))
* add cache dep ([2176fce](https://github.com/core0-io/pythia/commit/2176fcee966fda4a2912681490eabfd82544c058))
* add middleware, remove job prioritisation due to incompatibility with 'for update' ([31cee58](https://github.com/core0-io/pythia/commit/31cee5818e2c8198778edc4c447d4aa9640e8976))

### Performance Improvements
* **anomaly-detector:** only persist drain state at the end of preprocessing ([84ed8c1](https://github.com/core0-io/pythia/commit/84ed8c1766059e8d0f7ede28c260c95caa7f3b12))
* increase prophecy prediction job cost, increase default number of threads ([285ae92](https://github.com/core0-io/pythia/commit/285ae92ae69443d14abdd275808c4e7a903dd5d3))
* use metric tables for prophecies ([5b49305](https://github.com/core0-io/pythia/commit/5b493052d3cd013ba0d6a8a294d828c0961aeeaa))
* dynamic cost ([845c978](https://github.com/core0-io/pythia/commit/845c978461358cd66cd3063c0c0ae5d94a3df9c7))
* use metric engine for prophecy metric tables ([e24acb3](https://github.com/core0-io/pythia/commit/e24acb371bbe729f60cfba9bc2e68615d6a24a80))

### Other Changes
* create prophecy metric tables with ttl instead of relying on automatic creation ([ab4a306](https://github.com/core0-io/pythia/commit/ab4a306a3236fa082a40714f20ad403d911a1427))
* prioritise jobs based on attempts ([4ded47b](https://github.com/core0-io/pythia/commit/4ded47b716c4fa8f2cd24f9a1078e7cc2ffb64ac))
* handle process results without blocking (#30) ([85dfd60](https://github.com/core0-io/pythia/commit/85dfd60d0b415ddb85332339acb6834e45390570))
* remove redundant update request ([67ea0f1](https://github.com/core0-io/pythia/commit/67ea0f1f95ed873c88db02c822c615367f3668ac))
* avoid longer term locks on jobs (#27) ([895b709](https://github.com/core0-io/pythia/commit/895b70965391f9d616b3d0678b4823b7a5412db5))
* remove protobuf and query endpoint ([b394684](https://github.com/core0-io/pythia/commit/b3946849216e871d8c744a1204cf716906f2e564))
* **prophecy:** add error handling for greptime writes ([9b82383](https://github.com/core0-io/pythia/commit/9b8238365d1e1d79357ae419b9e3e1f6a6933a87))
* Greptime prophecies (#21) ([1422d73](https://github.com/core0-io/pythia/commit/1422d739e8348c8b78a308b4e1b9e6f7ba37ac05))
* Decouple jobs from prophecies, improve error handling & scheduling (#10) ([089ae00](https://github.com/core0-io/pythia/commit/089ae00a49b2b8e1645b44312434ce691af4fbe0))
* write metrics without labels ([0404caa](https://github.com/core0-io/pythia/commit/0404caad90d970c940c7b342adeb2b5b8151c4be))

### Reverts
* rename score to hopkins_stats ([70ff824](https://github.com/core0-io/pythia/commit/70ff8249846a2b606f724e1e698fcf424b1bd6ea))
* revert custom query endpoint ([3e3132a](https://github.com/core0-io/pythia/commit/3e3132a7f038b4cf6cdab155dbdff211e8d9f162))

### Documentation
* add log-anomaly-id arg to readme ([32c6760](https://github.com/core0-io/pythia/commit/32c6760d80be411dbed2be45f53ddffdd6c655b6))
* remove docs in favour of help comment on script ([0ae7738](https://github.com/core0-io/pythia/commit/0ae7738734bd34ff16ab7830f2cd4d1d61408152))


