# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Setup & Environment

```bash
poetry install          # Install dependencies
poetry shell           # Activate virtual environment
pre-commit install     # Install pre-commit hooks
```

### Code Quality

```bash
ruff check --fix       # Run linter with auto-fixes
ruff format           # Format code
pre-commit run        # Run pre-commit hooks
```

### Running the Application

```bash
# Development server
fastapi dev src/main.py --port 3000

# Production server
fastapi run src/main.py --proxy-headers --port 3000
```

### CLI Tools

```bash
# Prophet forecasting
python3 src prophecy train --mimir-query "..." --tenant-id "..." --prophecy-id "..." --start <timestamp> --end <timestamp>
python3 src prophecy predict --prophecy-id "..." --start <timestamp> --end <timestamp>

# Log anomaly detection
python3 src log-anomaly train --tenant-id "..." --flow-id "..." --anomaly-detector-id "..." --start <timestamp> --end <timestamp>
python3 src log-anomaly predict --tenant-id "..." --flow-id "..." --anomaly-detector-id "..." --trained-date <timestamp> --start <timestamp> --end <timestamp>
```

## Architecture Overview

### Core Components

- **FastAPI Application** (`src/main.py`) - Main service entry point with job scheduler
- **Job System** (`src/job/`) - Multi-threaded job processing with PostgreSQL scheduling
- **Anomaly Detection** (`src/anomaly_detector/`) - LogBERT, HDBSCAN, and LLM-based analysis
- **Prophecy** (`src/prophecy/`) - Facebook Prophet time-series forecasting
- **SKS Environment** (`src/sks/`) - Quality of Experience scoring and MCS/NSS analysis

### Job Types

- `log-anomaly` - Log structure and sequence anomaly detection
- `prophecy` - Time-series forecasting with confidence intervals
- `sks-mcs-nss` - SKS-specific correlation analysis

### Key Dependencies

- **FastAPI** for API framework
- **Prophet** for time-series forecasting
- **Transformers/LogBERT** for log anomaly detection
- **HDBSCAN** for clustering analysis
- **Drain3** for log parsing
- **PostgreSQL** for job scheduling
- **GreptimeDB** for time-series storage
- **S3/MinIO** for model artifacts

### Infrastructure Integration

- **Loki** for log aggregation
- **Mimir/Prometheus** for metrics
- **Consul** for service discovery
- **OpenAI/Gemini** for LLM analysis
- **SageMaker** for GPU training

### Development Notes

- Uses **Pydantic Settings** with environment variables
- **Structured logging** with structlog
- **Async/await** patterns throughout
- **Multi-environment** support (SKS-specific configurations)
- **Jupyter notebooks** in `src/notebooks/` for experimentation

### API Architecture

- **Anomaly Detection API** (`/tenants/{tenant_id}/anomaly-detectors/{ad_id}`) - Training, inference, and monitoring
- **Prophecy API** (`/tenants/{tenant_id}/prophecies`) - Time-series forecasting management
- **SKS API** (`/sks`) - SKS-specific site management and QoE analysis

### Job Processing System

- **ProcessPoolJobExecutor** - Multi-process job execution with configurable thread pool
- **JobScheduler** - PostgreSQL-backed job scheduling with retry logic
- **JobStore** - Persistent job state management with tenant isolation

### Data Storage

- **PostgreSQL** - Job scheduling, configuration, and metadata
- **GreptimeDB** - Time-series metrics and forecasting results
- **S3/MinIO** - Model artifacts, training data, and inference results
- **Loki** - Log aggregation and retrieval

### LLM Integration

- **Multi-provider support** - OpenAI, Gemini, OpenRouter, Groq
- **Contextual analysis** - Log anomaly explanation and root cause analysis
- **Translation services** - Multi-language support for SKS environment

### Configuration

- **Environment-based** - Uses .env files and environment variables
- **Consul integration** - Service discovery and dynamic configuration
- **Multi-tenant** - Supports tenant isolation and SKS-specific configurations
