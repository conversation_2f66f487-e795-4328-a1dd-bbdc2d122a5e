FROM python:3.11.11-slim-bullseye

ENV PYTHONUNBUFFERED=1 \
    POETRY_VIRTUALENVS_CREATE=false \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on

ARG PROJECT_NAME

RUN apt-get update && apt-get install zlib1g build-essential -y && apt-get clean

COPY ./tiktoken_cache /opt/tiktoken_cache
ENV TIKTOKEN_CACHE_DIR=/opt/tiktoken_cache

WORKDIR /app

COPY ./pyproject.toml ./poetry.lock ./

RUN pip install --no-cache-dir poetry && poetry install --only main --no-root --no-directory && rm -rf /root/.cache

COPY . /app

ENV PYTHONPATH=/app/src

CMD ["python", "src/main.py"]
