FROM pytorch/pytorch:2.5.1-cuda12.4-cudnn9-runtime

ENV POETRY_VIRTUALENVS_CREATE=false
ARG PROJECT_NAME
USER root

RUN apt-get update && apt-get install zlib1g -y && apt-get clean

COPY ./tiktoken_cache /opt/tiktoken_cache
ENV TIKTOKEN_CACHE_DIR=/opt/tiktoken_cache

WORKDIR /app

COPY ./crystal-ball/pyproject.toml ./crystal-ball/poetry.lock ./crystal-ball/
RUN pip install --no-cache-dir poetry && poetry -C ./crystal-ball install  --no-root --no-directory --compile --with gpu && rm -rf /root/.cache

COPY ./crystal-ball/resources ./crystal-ball/resources
COPY ./${PROJECT_NAME} ./app
COPY ./crystal-ball/src ./crystal-ball/src
COPY version.txt .

ENTRYPOINT ["./app"]
