﻿Training Phase
Log sequence ID insertion
Upon enabling a log group for Model training, vector is configured to insert log-sequence-id for every log ingested into Loki. This sequence id is a serial id, containing unique id referencing the anomaly_detector_id, time-stamp and an incremental numeric should there be multiple log lines arriving within the same microsecond.
Data Collection for training
* Log data is collected by performing time based query to Loki
* Golden Set data is collected over 7 consecutive days period (non public holiday)
* Data is collected over multiples of 1 hour timeslices
* By default FFWD skips collecting data from public holidays for training purposes
* Public holiday dates are inferred from registry db with country+state
* Country + state information is entered per tenant upon tenant creation process
* Public holiday dates are UTC+timezone of the local time, hence data collection is performed at 1hr time slice granularity
* Log data ingress volume and rate contain certain profile over the data collection period. Data ingestion profile is recorded in TDengine, where every cumulative 1GB is recorded against a timestamp.
* Mimir contains the record of total events over the collection period. Hence we can calculate the estimated average event size in Bytes by dividing the GB records from TDengine over the total events from Mimir. Let’s call this  bytes per event on average.
* By default FFWD collects 10GB of total data for training purposes over the data collection period (let’s call this  GB).
   * For log groups with more than 30GB per week of data, retrieve up to 10GB per week
   * For log groups with less than 30GB per week of data, retrieve 30% (up to 10GB) of week’s volume.
   * For log groups where 30% of total is less than 5GB, retrieve all logs from the week for training.
   * The previous week’s (day -14 to -7) volume density profile of the log group is used to determine the current week’s data collection profile.
   * 7 days consists of 168 x 1hr slices, by querying TDengine for total GB over the period, and for each 1hr time-slice, calculate the GB density percentage, let’s call this  , where 
   * For the target collection  GB over the collection period, for each 1hr time slice , FFWD collects actual  GB for the time slice. Total events to retrieve from Loki for the given 1hr time slice would be  


Skipping public holiday for data profile and data collection
if there are any public holidays in the previous week, it will use the profile the same day of the week before the previous week.
for instance, user enables it on 15/01, because 01/01 is a public holiday, it will try to use the same day of the week, which is 25/12, but 25/12 is also a public holiday, so it becomes 18/12. Which means the volume profile is based on 18/12, 02/01-07/01


For data collection around public holiday:
Just skip the public holiday, and move back more days. For instance, user enables it on 08/01, 25/12 and 26/12 are public holidays, so move back two days, collecting data for: 23/12 24/12 27/12-31/12


Above explains a fair and representative method to collect log data for model training purposes.
DRAIN
Log data collected are tokenised on a per log-line basis. DRAIN tokenises log lines for each “type” that it discovers. I.e. each similar type of log represented by the same token.
Sequence in which logs arrived is represented accordingly in the sequence of the tokens created by DRAIN. 
[CLS] token is appended to indicate the start of a sequence, 512 tokens represent one sequence batch, with [SEP] inserted, then another batch of [CLS] 512 and so on.
Log-sequence-id is recorded against each token of each batch, for the purpose of ability to trace a token flagged by BERT all the way back to the original log line.


Each log group has a DRAIN state file stored in MinIO. The output tokens from DRAIN are prepared for BERT training. The output tokens will be the vocabulary for BERT training. The size of the vocabulary is three times the number of DRAIN tokens with a minimum of 300 and a maximum of 1000. This allows the BERT model to be fine-tuned by replacing placeholders with new tokens when new types of logs emerge without needing to train the model from scratch.


Parquet files
Various ML models are used to compute data collected from Loki. Log data from Loki is transformed into schema form into Parquet files stored in MinIO object Storage.
Parquet file schema format looks like this:

{
  <field_name>: {
    key: <field_name>,
    types: [“str”] # there may be conflict types for a field
    enabled: bool # if it is a histogram or a numeric fields
    numeric: bool # if it is a numeric field. 
    histogram: bool # if it is a histogram field.
    is_ip: bool 
    is_datetime: bool
  }
}
The schema is used to ensure that fields with conflicting types can be stored correctly, as well as not to save fields that are not involved in the training and inference phase, such as high cardinality fields.
In the preprocessing phase, IP fields will be geo-looked up,  generating the country, state, and city fields which are considered discrete fields too.
Whenever possible, ML python programs use native data frame plugin PolaRS. Data frames are queried by Apache Arrow format.


Discrete Fields, new and missing values
FFWD discovers fields with discrete values. Conditions to qualify as discrete values:
      * Less than 500 unique values for the said field
      * Values appearing at least > 1% of the data collected for the said field


If a field value only appears rarely at less than 1% of the time, we disregard this value for training purposes. If a field has more than 500 unique values, we consider this as a high cardinality field and disregard this field for training purposes.


For all qualified values, we build a histogram for each qualified field. Histogram contain values as the bins, and frequency of occurrence as the Y axis.
The training process for Discrete fields creates a set of histograms, each representing each qualified field. 
Numeric Fields, gauge qualification
For each numeric field discovered, that has failed the discrete field test, FFWD collects up to 10,000 data points over the data collection period. Hopkins Statistics (2 out of 3 daily quadrant pass test)  is used to test if a numeric field can qualify to form cluster(s). E.g. gauge type numeric. Score close to 1 means strong tendency to form cluster. Set qualification score at 0.7. 
For numeric fields that fail Hopkins Statistics test, they are excluded from training process.
Numeric fields that passed Hopkins Statistics test, the data points are subjected to HDBSCAN to surface the cluster(s):
      * There maybe zero or more than 1 cluster for each qualified numeric field
      * Minimum cluster size setting is 15% of total data points collected
      * Minimum samples setting is 10% of total data points collected
      * Retrieve each cluster : upper and lower bound values + centroid value, cluster-id
      * There could be multiple numeric fields with multiple clusters each
Top Fields, new and missing fields
FFWD builds a histogram of fields (fields that appear at > 1% of total data collected), where bins are the field names, and occurrence frequency as Y axis.
The training process builds this histogram to represent the Top fields discovered and their distribution.


Log Structure
As a result of DRAIN process, each “type” of log has a corresponding unique token. “Type” could mean logs with different number of fields, different length, different value types within fields, arrangement of fields within a log. Hence each unique token is a good representation of a structure type for the given log.
Similar to Top Fields mentioned above, FFWD builds a histogram of unique tokens (with > 1% occurrence) during the training phase.


Log Rate and Volume with Prophet
FFWD uses Prophet to determine if the log rate (and hence volume) has drifted over time. Log incoming event count is converted to metrics, with metricised data points stored in Mimir. Log rate is hence count-over-time to produce a events per second representation.


Prophet imports time-series log rate data over the data collection period. Prophet outputs yhat, yhat_upper, yhat_lower , after a computation based on seasonality, trend and public holidays. 
In FFWD, we do not use the yhat_upper and yhat_lower produced by Prophet (since Prophet is a forecasting tool, with exaggerated confidence bands) , instead, we compute our own standard deviation based on historical data, and superimpose the standard deviation margins over the Prophet computed yhat value. In FFWD we also optionally ignore the trend component of Prophet, as trend component assumes an ever increasing or decreasing trend based on the trained data. 


During the training phase, we create an expected time-series log rate profile with confidence band in which future log rate should fall under. 


Log Sequence detection with LogBert
FFWD implemented LogBert [2103.04475] LogBERT: Log Anomaly Detection via BERT to detect sequence drift. 
Initially LogBert runs on AWS Sagemaker for both training and inference phases.
      * Training from scratch with the model BertForMaskedLM from huggingface library.
      * Untrained BERT image from Hugging Facetrain.py script
      * Batch transform job
      * S3 is used to stage DRAIN tokens imported
      * S3 is used to store the tokeniser and trained model
      * Spot instance GPU is used
      * Loss parameter of 0.08 to trigger training to stop or the improvement of metric_for_best_model is less than 0.002 after 10 consecutive times.
      * Alternatively, up to 100 epochs run to trigger training to stop


Each row in the dataset consists of up to 512 DRAIN tokens. To let the BERT model capture the relationship between logs, a rolling window is used when generating the dataset, with a window size of 512 and a step of 256.
________________


Inference Phase


Data Collection for inference
      * Up to 1GB of data is collected per day per log group for inference purposes
      * To create prediction results sensitive to each time slice, FFWD collects daily data at 10m time slices.
      * Same GB density % based collection mechanism is used as per training phase
      * There are up to 144 time slices per day. Hence should drift or divergence is detected, we are able to zoom into the 10 min time slice to identify the issue.
      * Data collected from 10 min time slices are used for 1) Discrete Value , 2) Numeric Value, 3) Top Fields, 4) Log Structure drift detections
      * For LogBert, DRAIN collects data and process tokens from the entire day (up to 144 lots) to produce a single day token sequence


DRAIN
Log data collected are tokenised on a per log-line basis. DRAIN tokenises log lines for each “type” that it discovers. I.e. each similar type of log represented by the same token.
Sequence in which logs arrived is represented accordingly in the sequence of the tokens created by DRAIN. 
[CLS] token is appended to indicate the start of a sequence, 512 tokens represent one sequence batch, with [SEP] inserted, then another batch of 512 and so on.
Log-sequence-id is recorded against each token of each batch, for the purpose of ability to trace a token flagged by BERT all the way back to the original log line.


Each log group has a DRAIN state file. The state file helps faster DRAIN processing on inference.


Discrete Fields, new and missing values
Inference set histogram is built on a 10 min time slice basis.
FFWD applies JSD (Jensen-Shannon Divergence) ML on inference set histogram and training set histogram to produce a JSD score. The top 50 values of the training set with the highest frequency will be taken, and the remaining values will be grouped into “others”.
      * JSD score greater than 0.2 is considered high drift and is the threshold for divergence
      * Each 10 min slice during the day has a corresponding JSD score, keeping the drift detection relevant and accurate to the time when it happens
      * JSD score as a metric is stored in Mimir for each given field with discrete values


On the user portal, we display the worst JSD score of the day for this feature (since there are up to 144 JSD scores for the day), to flag the importance of being highly sensitive to any divergence, even down to temporal local level.




Numeric Fields
FFWD attempts to discover numeric clusters and their centroid values at 10min time slice interval throughout the inference day data. Since Hopkins Stats and HDBSCAN requires sufficient data points for algorithm to work, the following process is used:
      * For each 10min time slice, we simply compare inference data points with training set cluster range, to determine the outlier points, and to compute % of outliers against training set cluster(s).
      * From above, we have a outlier score for every 10 min time slice
      * For the entire inference day data set, we run HDBSCAN (one per day per qualified numeric field) to compute Centroid value(s) for the cluster(s)
      * Outlier scores are stored in Mimir at every 10 min interval
      * There could be multiple numeric fields with multiple clusters each


On the user portal, we display the worst outlier score of the day for this feature (since there are up to 144 outlier scores for the day), to flag the importance of being highly sensitive to any divergence, even down to temporal local level.


Top Fields
Inference set histogram is built on a 10min time slice basis.
FFWD applies JSD (Jensen-Shannon Divergence) ML on inference set histogram and training set histogram to produce a JSD score. The top 50 fields of the training set with the highest frequency will be taken, and the remaining fields will be grouped into “others”.


      * JSD score greater than 0.2 is considered high drift and is the threshold for divergence
      * Each 10min slice during the day has a corresponding JSD score, keeping the drift detection relevant and accurate to the time when it happens
      * JSD score as a metric is stored in Mimir for each given field discovered


On the user portal, we display the worst JSD score of the day for this feature (since there are up to 144 JSD scores for the day), to flag the importance of being highly sensitive to any divergence, even down to temporal local level.


Log Structures
Inference set histogram is built on a 10min time slice basis.
FFWD applies JSD (Jensen-Shannon Divergence) ML on inference set histogram and training set histogram to produce a JSD score. The top 50 templates of the training set with the highest frequency will be taken, and the remaining templates will be grouped into “others”.


      * JSD score greater than 0.2 is considered high drift and is the threshold for divergence
      * Each 10min slice during the day has a corresponding JSD score, keeping the drift detection relevant and accurate to the time when it happens
      * JSD score as a metric is stored in Mimir for each given token discovered


On the user portal, we display the worst JSD score of the day as the feature score (since there are up to 144 JSD scores for the day), to flag the importance of being highly sensitive to any divergence, even down to temporal local level.




Log Rate and Volume
We compute the percentage of outliers against predicted Prophet band, the outlier percentage forms the daily score for this feature panel. 
User is able to visually see on the time-series graph in detail modal, when the issue occurs, down to closest 1 min. No temporal information is lost.




Log Sequence
LogBert surfaces the 512 sequence block with the worst prediction score, and within that the tokens with the worst prediction score. From the sequence block id and token position within the block, we are able to retrieve the log-sequence-id (originally inserted by vector). 
The prediction score for the worst 512 sequence block becomes the Sequence Score of the day.


We can retrieve the original log lines having the worst prediction scores by simply querying Loki using the matching log-sequence-id.