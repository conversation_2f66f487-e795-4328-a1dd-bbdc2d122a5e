# Anomaly service scripts

## Local Development

Install [Poetry](https://python-poetry.org/docs/)

```
$ poetry install
$ poetry shell
```

## Prophet

For training a model:

```
Usage: python3 src prophecy train [OPTIONS]

  Prophet train script

Options:
  --mimir-query TEXT         [required]
  --tenant-id TEXT           [required]
  --prophecy-id TEXT         [required]
  --start INTEGER            unix timestamp  [required]
  --end INTEGER              unix timestamp  [required]
  --tenant-subdivision TEXT
  --tenant-country TEXT
  --help                     Show this message and exit.
```

For forecasting with a trained model:

```
Usage: python3 src prophecy predict [OPTIONS]

  Prophet predict script

Options:
  --prophecy-id TEXT  [required]
  --start INTEGER     unix timestamp  [required]
  --end INTEGER       unix timestamp  [required]
  --help              Show this message and exit.
```

## Anomaly Detector

For training:

```
Usage: python3 src log-anomaly train [OPTIONS]

Options:
  --tenant-id TEXT                [required]
  --flow-id TEXT                  [required]
  --anomaly-detector-id TEXT      [required]
  --metric-name TEXT              Metric name for log rate in mimir
                                  [required]
  --start INTEGER                 unix timestamp  [required]
  --end INTEGER                   unix timestamp  [required]
  --skip-public-holiday           [default: no-skip-public-holiday]
  --tenant-subdivision TEXT
  --tenant-country TEXT
  --help                          Show this message and exit.
```

For inference:

```
Usage: python3 src log-anomaly predict [OPTIONS]

Options:
  --tenant-id TEXT                [required]
  --flow-id TEXT                  [required]
  --anomaly-detector-id TEXT      [required]
  --trained-date INTEGER          [required]
  --start INTEGER                 unix timestamp  [required]
  --end INTEGER                   unix timestamp  [required]
  --metric-name TEXT              Metric name for log rate in mimir
                                  [required]
  --prophecy-id TEXT              Prophecy ID for calculate log rate score in mimir
                                  [required]
  --skip-public-holiday           [default: false]
  --tenant-subdivision TEXT
  --tenant-country TEXT
  --help                          Show this message and exit.
```
