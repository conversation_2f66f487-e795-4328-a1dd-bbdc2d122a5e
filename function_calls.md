## LLM API

Base URL: http://172.16.10.210:13001/tenants/sks


### 创建completions

`POST :13001/tenants/sks/completions`

```json
{
  "provider": "openai",
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "provider-options": {...}
}
```

- `model`: 目前支持 `gpt-4o` 和 `gpt-4o-mini`，后续会支持更多模型。
- `messages`: 消息列表
  - `role`: 可选值为 `user`、`assistant`、`system`
  - `content`: 消息内容
    - 可以为纯字符串
    - 可以为object，比如 `{"text": {"type": "text", "content": "Hello"}}` 或者上传图片: 
    - `{ "type": "image_url", "image_url": { "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg" } }`
比如
```
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What is in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
            }
          }
        ]
      }
    ],
```
- `temperature`: 生成的随机性，范围是0-1，越高越随机
- `top-p`: 返回的数量
可以参考openai的参数。 https://platform.openai.com/docs/api-reference/chat/create

**返回值**: 针对发送的消息，返回的下一条回复。如果要进行连续对话，需要自己维护 `messages` 数组。
```json
{
    "choices": [
        {
            "finish_reason": "stop",
            "index": 0,
            "logprobs": null,
            "message": {
                "role": "assistant",
                "content": "......",
                "refusal": null,
                "annotations": []
            }
        }
    ],
    "usage": {
        "total-tokens": 19,
        "completion-tokens": 9,
        "completion-tokens-details": {
            "reasoning_tokens": 0,
            "accepted_prediction_tokens": 0,
            "audio_tokens": 0,
            "rejected_prediction_tokens": 0
        },
        "prompt-tokens": 10,
        "prompt-tokens-details": {
            "cached_tokens": 0,
            "audio_tokens": 0
        }
    }
}
```

如果要进行function call，则可以参考OpenAI的文档，
https://platform.openai.com/docs/guides/function-calling

```json
{
  "provider": "openai",
  "model": "gpt-4o",
  "messages": [{"content": [{"type":"text", "text": "whats the weather today in beijing?"}], "role": "user"}],
  "top-p": 1,
  "provider-options": {
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "get_weather",
          "description": "Get current temperature for a given location.",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "City and country e.g. Bogotá, Colombia"
              }
            },
            "required": [
              "location"
            ],
            "additionalProperties": false
          },
          "strict": true
        }
      }
    ]
  }
}
```

然后会返回

```json
{
  "choices": [
    {
      "finish_reason": "tool_calls",
      "index": 0,
      "logprobs": null,
      "message": {
        "role": "assistant",
        "content": null,
        "refusal": null,
        "tool_calls": [
          {
            "type": "function",
            "function": {
              "arguments": "{\"location\":\"Beijing, China\"}",
              "name": "get_weather"
            },
            "id": "call_OJzUqYha4eBS9mDJj3PFh9k7"
          }
        ],
        "annotations": []
      }
    }
  ],
  "usage": {
    "total-tokens": 83,
    "completion-tokens": 18,
    "completion-tokens-details": {
      "reasoning_tokens": 0,
      "accepted_prediction_tokens": 0,
      "audio_tokens": 0,
      "rejected_prediction_tokens": 0
    },
    "prompt-tokens": 65,
    "prompt-tokens-details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    }
  }
}
```

然后就可以在前端调用 `get_weather(location="Beijing, China")`  这个函数了
后续聊天，就继续调用 `/completion` 接口，不断的扩充 `messages` 这个字段。
