## LLM API

Base URL: http://172.16.10.210:13001/tenants/sks


### 创建completions

不使用Knowledge Base，进行最基本的聊天：

`POST :13001/tenants/sks/completions`

```json
{
  "provider": "openai",
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "provider-options": {...}
}
```

- `model`: 目前支持 `gpt-4o` 和 `gpt-4o-mini`，后续会支持更多模型。
- `messages`: 消息列表
  - `role`: 可选值为 `user`、`assistant`、`system`
  - `content`: 消息内容
    - 可以为纯字符串
    - 可以为object，比如 `{"text": {"type": "text", "content": "Hello"}}` 或者上传图片: 
    - `{ "type": "image_url", "image_url": { "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg" } }`
比如
```
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What is in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
            }
          }
        ]
      }
    ],
```
- `temperature`: 生成的随机性，范围是0-1，越高越随机
- `top-p`: 返回的数量
可以参考openai的参数。 https://platform.openai.com/docs/api-reference/chat/create

**返回值**: 针对发送的消息，返回的下一条回复。如果要进行连续对话，需要自己维护 `messages` 数组。
```json
{
    "choices": [
        {
            "finish_reason": "stop",
            "index": 0,
            "logprobs": null,
            "message": {
                "role": "assistant",
                "content": "......",
                "refusal": null,
                "annotations": []
            }
        }
    ],
    "usage": {
        "total-tokens": 19,
        "completion-tokens": 9,
        "completion-tokens-details": {
            "reasoning_tokens": 0,
            "accepted_prediction_tokens": 0,
            "audio_tokens": 0,
            "rejected_prediction_tokens": 0
        },
        "prompt-tokens": 10,
        "prompt-tokens-details": {
            "cached_tokens": 0,
            "audio_tokens": 0
        }
    }
}
```

### 知识库相关

`knowledge_base_id=dd0f252b-12c0-4599-b659-68116115096b`

由于当前知识库依赖OpenAI的特性，所以目前只能使用OpenAI的模型

#### 创建Thread并运行

`PUT :13001/tenants/sks/threads/runs`

参数为：

```json
{
 "knowledge-base-id": "dd0f252b-12c0-4599-b659-68116115096b",
 "model" : "gpt-4o-mini",
 "messages": [{"role": "user", "content": "Explain the aiops"}]
}
```
这里的messages格式和上面一样。

返回值为。
```json
{
    "thread-id": "thread_eCb3XTchpf2TkDpErf4cCp9S",
    "run-id": "run_ScAdvMbfhWJqJI5KtaUXkfZd",
    "status": "completed"
}
```

需要注意这个接口的超时时间，建议设置为1分钟。
当返回后，`thread-id` 就是这个对话的ID。如果希望有查询历史会话的能力，需要自己持久化这个ID。
可以向这个thread添加更多消息，以及查看这个thread里的所有消息

#### 向Thread添加消息

`PUT :13001/tenants/sks/threads/:thread-id/messages`

只有 `role` 和 `content` 两个字段。和上述一样。
返回值：
```json
{
    "message-id": "msg_9zwxLgv7z1WUWeCosUbROcGh",
    "thread-id": "thread_gvMxC3fbV8KaOcS63qYGYCmq",
    "run-id": "run_sB61GDDubC6VVps356oBcd3j",
    "status": "completed"
}
```

#### 列出Thread消息

`GET :13001/tenants/sks/threads/:thread-id/messages`

Params:
- `limit`: 返回的消息数量。默认200，取值范围1-100
- `order`: asc 或者 desc。默认为desc
- `after`: message的id。获取指定id之后的消息
- `before`: message的id。获取指定id之前的消息

返回值与OpenAI一致：https://platform.openai.com/docs/api-reference/messages/listMessages

#### Function call的方式

首先也是通过 `PUT :13001/tenants/sks/threads/runs` 的接口来创建一个thread，可以额外通过 `provider-options` 传OpenAI的参数 (https://platform.openai.com/docs/api-reference/runs/createThreadAndRun )

比如：

```bash
curl --location --request PUT 'http://127.0.0.1:3000/tenants/sks/threads/runs' \
--header 'Content-Type: application/json' \
--data '{
"knowledge-base-id": "dd0f252b-12c0-4599-b659-68116115096b",
 "model" : "gpt-4o-mini",
 "messages": [{"role": "user", "content": "What'\''s the weather in Beijing"}],
 "provider-options": {
   "tools":  [
    {
      "type": "function",
      "function": {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
    }
  ]
 }
}'
```

调用后可能会返回：

```json
{
    "thread-id": "thread_HbAkZPXx7KkyneUUmWrXZReq",
    "run-id": "run_elcIVCA5WXaSSbvyv5MHMJT4",
    "status": "requires_action",
    "required-action": {
        "submit_tool_outputs": {
            "tool_calls": [
                {
                    "type": "function",
                    "function": {
                        "arguments": "{\"location\":\"Beijing\"}",
                        "name": "get_current_weather"
                    },
                    "id": "call_6lozuU21Qsr7saGL9PMg1ruB"
                }
            ]
        },
        "type": "submit_tool_outputs"
    }
}
```

当 `status="requires_action"` 且 `required-action.type="submit_tool_outputs"` 时，需要调用 `submit_tool_outputs` 接口来给OpenAI上传这个function的返回值。

比如

```bash
curl --location '.../tenants/sks/threads/thread_HbAkZPXx7KkyneUUmWrXZReq/runs/run_elcIVCA5WXaSSbvyv5MHMJT4/submit-tool-outputs' \
--header 'Content-Type: application/json' \
--data '{
    "tool_outputs": [
      {
        "tool_call_id": "call_6lozuU21Qsr7saGL9PMg1ruB",
        "output": "20摄氏度"
      }
    ]
  }'
```

然后再调用 `/tenants/sks/threads/thread_HbAkZPXx7KkyneUUmWrXZReq/messages` 接口来获取这个Thread的聊天记录
