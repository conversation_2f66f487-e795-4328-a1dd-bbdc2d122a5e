[project]
name = "pythia"
description = ""
version = "2.0"
authors = []
requires-python = ">=3.11,<3.12"
dependencies = [
    "numpy (>=1.26,<2.0)",
    "pandas (>=2.2,<3.0)",
    "prophet (>=1.1.6,<2.0)",
    "boto3 (>=1.35,<2.0)",
    "requests (>=2.32,<3.0)",
    "protobuf (>=4.25,<5.0)",
    "holidays (>=0.62,<1.0)",
    "structlog (>=24.4.0,<25.0)",
    "orjson (>=3.10.12,<4.0)",
    "sagemaker (>=2.236.0,<3.0)",
    "datasets (>=3.1.0,<4.0)",
    "tokenizers (>=0.21.0,<1.0)",
    "transformers (>=4.47.0,<5.0)",
    "drain3 (>=0.9.11,<1.0)",
    "scikit-learn (>=1.6.0,<2.0)",
    "scipy (>=1.13.1,<1.15)",
    "pyarrow (>=18.1.0,<19.0)",
    "hdbscan (>=0.8.40,<1.0)",
    "polars[fsspec, numpy, pandas, pyarrow] (>=1.17.1,<2.0)",
    "pydantic-settings (>=2.6.1,<3.0)",
    "pydantic (>=2.10.3,<3.0)",
    "httpx (>=0.28.1,<1.0)",
    "maxminddb (>=2.6.2,<3.0)",
    "s3fs (>=2024.9.0,<2025.0)",
    "s3path (>=0.6.0,<1.0)",
    "python-snappy (>=0.7.3,<1.0)",
    "tenacity (>=8.2.3,<9)",
    "google-generativeai (>=0.8.3,<1.0)",
    "openai (>=1.57.1,<2.0)",
    "tiktoken (>=0.8.0,<1.0)",
    "urllib3 (>=1.26,<2.0)",
    "fastapi (>=0.115.8,<1.0)",
    "python-multipart (>=0.0.20,<1.0)",
    "uvicorn[standard] (>=0.34.0,<1.0)",
    "fastapi-cli (>=0.0.7,<1.0)",
    "pydantic-extra-types (>=2.10.2,<3.0)",
    "pypika (>=0.48.9,<1.0)",
    "asyncpg (>=0.30.0,<1.0)",
    "pendulum (>=3.0.0,<4.0)",
    "rich (>=13.9.4,<14.0)",
    "dotenv (>=0.9.9,<1.0)",
    "pre-commit (>=4.2.0,<5.0.0)",
    "anyio (>=4.9.0,<5.0.0)",
    "pandas-stubs (>=2.2.3.250527,<*******)",
    "google-genai (>=1.24.0,<2.0.0)",
    "qdrant-client (>=1.14.3,<2.0.0)",
    "asgi-correlation-id (>=4.3.4,<5.0.0)",
]

[tool.poetry]
requires-poetry = ">=2.0"
package-mode = false
packages = [{ include = "src" }]

[tool.poetry.group.gpu.dependencies]
accelerate = "0.34.2"

[build-system]
requires = ["poetry-core>=2.0"]
build-backend = "poetry.core.masonry.api"

[tool.pyright]
include = ["src"]
exclude = ["src/pb/**", "src/notebooks/**", "src/scripts/**"]
pythonVersion = "3.11"
