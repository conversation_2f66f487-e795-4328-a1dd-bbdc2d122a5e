import os

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, BertForMaskedLM

mlm_probability = 0.5
candidate_size = int(os.environ.get("CANDIDATE_SIZE", "15"))
device = "cuda"


def torch_mask_tokens(tokenizer, inputs, special_tokens_mask):
    """
    Prepare masked tokens inputs/labels for masked language modeling: 80% MASK, 10% random, 10% original.
    """

    labels = inputs.clone()
    # We sample a few tokens in each sequence for MLM training (with probability `mlm_probability`)
    probability_matrix = torch.full(labels.shape, mlm_probability)
    if special_tokens_mask is None:
        special_tokens_mask = [
            tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True) for val in labels.tolist()
        ]
        special_tokens_mask = torch.tensor(special_tokens_mask, dtype=torch.bool)
    else:
        special_tokens_mask = special_tokens_mask.bool()

    probability_matrix.masked_fill_(special_tokens_mask, value=0.0)
    masked_indices = torch.bernoulli(probability_matrix).bool()
    labels[~masked_indices] = -100  # We only compute loss on masked tokens
    inputs[masked_indices] = tokenizer.convert_tokens_to_ids(tokenizer.mask_token)
    return inputs, labels


# call order
# 1. model_fn
# 2. input_fn processed_data = self.preprocess(input_data, content_type)
# 3. predict_fn predictions = self.predict(processed_data, model)
# 4. output_fn response = self.postprocess(predictions, accept)


def model_fn(model_dir):
    # Load model from HuggingFace Hub
    tokenizer = AutoTokenizer.from_pretrained(model_dir)
    model = BertForMaskedLM.from_pretrained(model_dir)
    model.to(device)
    return model, tokenizer


def predict_fn(data, model_and_tokenizer):
    # destruct model and tokenizer
    model, tokenizer = model_and_tokenizer

    input_texts = data.pop("text")

    # Tokenize sentences
    tokenized_data = tokenizer(input_texts, padding=True, truncation=True, return_tensors="pt", max_length=512)
    original_input_ids = tokenized_data["input_ids"].clone().to(device)
    mask = (
        (tokenized_data["input_ids"] == tokenizer.cls_token_id)
        | (tokenized_data["input_ids"] == tokenizer.sep_token_id)
        | (tokenized_data["input_ids"] == tokenizer.pad_token_id)
    )
    tokenized_data["input_ids"], tokenized_data["labels"] = torch_mask_tokens(
        tokenizer, tokenized_data["input_ids"], special_tokens_mask=mask
    )

    # Compute token embeddings
    with torch.no_grad():
        model_output = model(**tokenized_data.to(device))

    mask_token_index = torch.where(tokenized_data["input_ids"] == tokenizer.mask_token_id)
    original_tokens = original_input_ids[mask_token_index]
    candidate_tokens = torch.topk(model_output["logits"][mask_token_index], candidate_size)
    predicted = (original_tokens.unsqueeze(1) == candidate_tokens.indices).any(dim=1)
    softmax_result = F.softmax(model_output["logits"], dim=2)
    probabilities = torch.full(original_input_ids.shape, -1.0, device=device)
    probabilities[mask_token_index] = softmax_result[
        mask_token_index[0], mask_token_index[1], original_input_ids[mask_token_index]
    ]

    # return dictionary, which will be json serializable
    return {"predicted": predicted.cpu().numpy().tolist(), "probabilities": probabilities.cpu().numpy().tolist()[0]}
