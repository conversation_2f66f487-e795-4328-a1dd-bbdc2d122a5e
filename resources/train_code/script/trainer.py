import logging
import sys
from typing import Callable, Dict, List, Optional, Tuple, Union

import numpy as np
import torch
from datasets import Dataset
from torch import nn
from torch.cuda.amp import autocast
from transformers import (
    DataCollator,
    EvalPrediction,
    PreTrainedModel,
    PreTrainedTokenizerBase,
    Trainer,
    TrainerCallback,
    TrainerControl,
    TrainerState,
    TrainingArguments,
)

logger = logging.getLogger(__name__)

logging.basicConfig(
    level=logging.getLevelName("INFO"),
    handlers=[logging.StreamHandler(sys.stdout)],
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

device = "cpu"
if torch.cuda.is_available():
    device = "cuda"
elif torch.mps.is_available():
    device = "mps"


class LogBertTrainer(Trainer):
    def __init__(
        self,
        model,
        args: TrainingArguments = None,
        data_collator: Optional[DataCollator] = None,
        train_dataset: Optional[Dataset] = None,
        eval_dataset: Optional[Union[Dataset, Dict[str, Dataset]]] = None,
        tokenizer: Optional[PreTrainedTokenizerBase] = None,
        model_init: Optional[Callable[[], PreTrainedModel]] = None,
        compute_metrics: Optional[Callable[[EvalPrediction], Dict]] = None,
        callbacks: Optional[List[TrainerCallback]] = None,
        optimizers: Tuple[torch.optim.Optimizer, torch.optim.lr_scheduler.LambdaLR] = (None, None),
        preprocess_logits_for_metrics: Optional[Callable[[torch.Tensor, torch.Tensor], torch.Tensor]] = None,
    ):
        super().__init__(
            model,
            args,
            data_collator,
            train_dataset,
            eval_dataset,
            tokenizer,
            model_init=model_init,
            compute_metrics=compute_metrics,
            callbacks=callbacks,
            optimizers=optimizers,
            preprocess_logits_for_metrics=preprocess_logits_for_metrics,
        )
        self.hyper_center = None
        self.hyper_criterion = nn.MSELoss()

    def _calculate_hyper_center(self):
        logger.info("calculate_hyper_center")
        outputs = 0
        total_samples = 0

        def calculate_center(batch: Dict[str, List]):
            nonlocal outputs, total_samples
            tokenized = self.tokenizer(
                batch["text"], padding=True, truncation=True, return_tensors="pt", max_length=512
            )
            result = self.model.forward(**tokenized.to(device), output_hidden_states=True)
            cls_output = result.hidden_states[-1][:, 0, :]
            outputs += torch.sum(cls_output.detach().clone(), dim=0)
            total_samples += cls_output.size(0)
            return batch

        with torch.no_grad():
            if self.train_dataset:
                self.train_dataset.map(calculate_center, batched=True, batch_size=32)
            if self.eval_dataset:
                self.eval_dataset.map(calculate_center, batched=True, batch_size=32)

        logger.info("calculate_hyper_center done")
        return outputs / total_samples

    def calculate_hyper_center(self):
        if torch.cuda.is_available():
            with autocast():
                return self._calculate_hyper_center()
        else:
            return self._calculate_hyper_center()

    def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
        outputs = model(**inputs, output_hidden_states=True)
        mask_loss = outputs["loss"]  # bert loss

        # [CLS]
        cls_output = outputs["hidden_states"][-1][:, 0, :]

        hyper_loss = 0
        # hypersphere_loss
        if self.hyper_center is None and self.state.epoch >= 10:
            # init hyper_center it resumes from checkpoint
            self.hyper_center = self.calculate_hyper_center()

        if self.hyper_center is not None:
            hyper_loss = self.hyper_criterion(
                cls_output.squeeze(), self.hyper_center.expand(inputs["input_ids"].shape[0], -1)
            )

        # version 2.0 https://github.com/lukasruff/Deep-SVDD-PyTorch/blob/master/src/optim/deepSVDD_trainer.py
        # dist = torch.sum((cls_output - self.hyper_center) ** 2, dim=1)
        loss = mask_loss + 0.1 * hyper_loss
        # loss = mask_loss

        return (loss, outputs) if return_outputs else loss


class EarlyStoppingCallback(TrainerCallback):
    def __init__(self, early_stopping_patience: int, early_stopping_threshold: float, early_stopping_target: float):
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_threshold = early_stopping_threshold
        self.early_stopping_target = early_stopping_target
        self.early_stopping_patience_counter = 0

    def check_metric_value(self, args, state, control, metric_value):
        # best_metric is set by code for load_best_model
        operator = np.greater if args.greater_is_better else np.less
        if state.best_metric is None or (
            operator(metric_value, state.best_metric)
            and abs(metric_value - state.best_metric) > self.early_stopping_threshold
        ):
            self.early_stopping_patience_counter = 0
        else:
            self.early_stopping_patience_counter += 1

    def on_evaluate(self, args, state, control, metrics, **kwargs):
        metric_to_check = args.metric_for_best_model
        if not metric_to_check.startswith("eval_"):
            metric_to_check = f"eval_{metric_to_check}"
        print("metrics", metrics, state, args, self.early_stopping_patience_counter)
        metric_value = metrics.get(metric_to_check)
        if state.best_metric is not None and state.best_metric < self.early_stopping_target:
            control.should_training_stop = True
        self.check_metric_value(args, state, control, metric_value)
        if self.early_stopping_patience_counter >= self.early_stopping_patience:
            control.should_training_stop = True


class OnEpochBeginCallback(TrainerCallback):
    def __init__(self, trainer: LogBertTrainer):
        self.trainer = trainer

    def on_epoch_begin(self, args: TrainingArguments, state: TrainerState, control: TrainerControl, **kwargs):
        logger.info(f"on_epoch_begin, state={state}")
        if state.epoch >= 10:
            self.trainer.hyper_center = self.trainer.calculate_hyper_center()
