target-version = "py310"
include = ["pyproject.toml", "src/**/*.py"]
exclude = [
    ".venv",
    ".git",
    "__pycache__",
    "build",
    "dist",
    "venv",
    "src/pb",
    "src/notebooks",
    "src/scripts",
]
line-length = 120

[lint]
select = ["ALL"]

ignore = [
    # Formatter conflicts
    "E501", # line too long (handled by formatter)
    "W191", # indentation contains tabs
    "E111", # indentation is not a multiple of 4
    "D206", # docstring should be indented with spaces
    "D300", # use triple double quotes

    # Documentation (enable gradually)
    "D100", # missing docstring in public module
    "D101", # missing docstring in public class
    "D102", # missing docstring in public method
    "D103", # missing docstring in public function
    "D104", # missing docstring in public package
    "D105", # missing docstring in magic method
    "D107",
    "D205",
    "D400",
    "D415",

    # Type annotations (enable when ready)
    "ANN001", # missing type annotation for function argument
    "ANN002", # missing type annotation for *args
    "ANN003", # missing type annotation for **kwargs
    "ANN201", # missing return type annotation
    "ANN202", # missing return type annotation for private function
    "ANN204", # missing return type annotation for special method
    "ANN205", # missing return type annotation for staticmethod
    "ANN206", # missing return type annotation for classmethod
    "ANN401",
    "TRY003", # raise-vanilla-args,
    "TRY400",

    "EM101",
    "EM102",

    "G004",

    "S311",
    "S501",

    "G002",

    "ERA001",

    "TD",
    "FIX",

    "PERF203", # try-except-in-loop (PERF203)
    "PERF401",
    "PGH003",

    # Project-specific allowances
    "B008",    # function calls in argument defaults (FastAPI)
    "S101",    # use of assert (common in tests)
    "T201",    # print statements (debugging)
    "BLE001",  # blind except (sometimes needed)
    "FBT001",  # boolean positional arg
    "FBT002",  # boolean default positional arg
    "PLR0913", # too many arguments
    "C901",    # too complex
    "PLR0915", # too many statements
    "TC006",
    "RUF002",
    "S301",
    "S324",
    "TRY300",

    # temporary ignores
    "PLR0912",
    "PLR2004",
    "PTH118",
    "NPY002",
    "G201",
    "S106",
    "S107",
    "S108",
    "S608",
    "LOG",
    'B019',
    "RUF012",
    "B023",
    "PD015",
    "PTH123",
    "S112",
    "S110",
    "E722",
]

[lint.isort]
known-first-party = ["src"]
force-single-line = false
combine-as-imports = true

[format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
