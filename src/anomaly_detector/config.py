import math
import pathlib

import holidays
from pendulum import DateTime
from pydantic_settings import BaseSettings

from llm.providers.base import BaseModel
from llm.providers.openai import OpenAIProvider


class NumericFieldSettings(BaseSettings):
    samples_per_cycle: int = 10000
    valid_hopkins_score: float = 0.7  # greater than
    hopkins_sample: float = 0.05
    hdbscan_min_cluster_size: float = 0.15  # percentage of total rows
    hdbscan_min_samples: float = 0.1
    bins: int = 100


class HistogramFieldSettings(BaseSettings):
    bins: int = 50
    max_cardinality: int = 500


class LogBERTSettings(BaseSettings):
    # sagemaker_bucket: str = ""
    # sagemaker_s3_prefix: str = "logbert"
    train_ratio: float = 6000
    mlm_probability: float = 0.5

    hidden_size: int = 256
    max_len: int = 512
    epochs: int = 100
    per_device_train_batch_size: int = 32

    predicted_percentage_threshold: float = 0.8

    @staticmethod
    def cardinality_size(clusters: int):
        return min(max(5, math.floor(clusters / 8)), 15)


class PreprocessSettings(BaseSettings):
    record_batch_size: int = 4 * 5000


class HolidaySetting(BaseSettings):
    skip_public_holidays: bool = False
    subdivision: str | None = None
    country: str = "AU"

    def is_public_holiday(self, date: DateTime):
        return date.naive() in holidays.country_holidays(self.country, subdiv=self.subdivision)


class LLMModelSettings(BaseSettings):
    on_premises_provider_model: str = "qwen3:14b"
    on_premises_model_thinking: bool = False
    on_premises_token_limit: int = 100_000

    sks_default_model: str = "qwen-turbo"

    lograte_model: str = "openrouter:gpt-4.1"
    contextual_ranking_model: str = "openrouter:gemini-2.5-flash"
    log_grouping_model: str = "openrouter:gpt-4.1"
    metric_model: str = "openrouter:gpt-4.1"
    advisory_model: str = "gpt-4.1"  # OpenAI Assistant
    translator_model: str = "openrouter:gemini-2.5-flash"
    past_suppression_model: str = "openrouter:qwen3:235b"

    sks_embedding_model: str = "aliyun-embedding-v4"
    on_premises_embedding_model: str = "bge-m3"
    ffwd_embedding_model: str = "gemini"


class RetrieverSetting(BaseSettings):
    sleep_time: float = 0.04
    line_limit: int = 5000


resources_path = pathlib.Path.cwd() / "resources"


class AnomalyDetectorSettings(BaseSettings):
    inference_retention_days: int = 90
    postprocess_timeout: int = 5 * 60
    min_percentage_to_keep: float = 0.01
    skip_drain_templates_threshold: int = 2000
    on_premises_gpu: bool = False
    is_sks_env: bool = False
    geo_database_path: pathlib.Path = resources_path / "GeoLite2-City.mmdb"
    drain_config_path: pathlib.Path = resources_path / "drain3.ini"
    train_source_dir: pathlib.Path = resources_path / "train_code/script"
    inference_source_dir: pathlib.Path = resources_path / "inference_code/code"

    # holiday: HolidaySetting = HolidaySetting()
    llm: LLMModelSettings = LLMModelSettings()
    numeric_field: NumericFieldSettings = NumericFieldSettings()
    histogram_field: HistogramFieldSettings = HistogramFieldSettings()
    logbert: LogBERTSettings = LogBERTSettings()
    preprocess: PreprocessSettings = PreprocessSettings()
    retriever: RetrieverSetting = RetrieverSetting()

    def llm_model(self, model_name: str) -> BaseModel:
        if self.on_premises_gpu:
            if self.llm.on_premises_provider_model is None:
                raise ValueError("on_premises_provider_model is not set for on-premises GPU.")
            return OpenAIProvider.get_model(
                model_name=self.llm.on_premises_provider_model,
                token_limit=self.llm.on_premises_token_limit,
                thinking_model=self.llm.on_premises_model_thinking,
            )
        if self.is_sks_env:
            return OpenAIProvider.get_model(model_name=self.llm.sks_default_model)
        return OpenAIProvider.get_model(model_name=model_name)

    def embedding_model(self) -> str:
        if self.on_premises_gpu:
            return self.llm.on_premises_embedding_model
        if self.is_sks_env:
            return self.llm.sks_embedding_model
        return self.llm.ffwd_embedding_model


def get_anomaly_detector_settings():
    return AnomalyDetectorSettings()
