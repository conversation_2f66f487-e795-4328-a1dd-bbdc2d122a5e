from typing import Annotated

import pendulum
from fastapi import Depends
from pydantic_extra_types.pendulum_dt import DateTime

from config import Settings, get_settings

from .s3_path_config import S3PathConfig


async def get_inference_path_config(
    ad_id: str,
    datetime: Annotated[pendulum.DateTime, DateTime],
    settings: Annotated[Settings, Depends(get_settings)],
    full: bool = True,
) -> S3PathConfig:
    return S3PathConfig(
        anomaly_detector_id=ad_id,
        hosted_s3_base_path=settings.s3.base_path,
        inference_date=datetime,
        full_inference=full,
    )
