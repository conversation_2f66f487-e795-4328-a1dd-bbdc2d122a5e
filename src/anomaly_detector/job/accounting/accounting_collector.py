from dataclasses import dataclass
from datetime import datetime, timezone

import httpx
import orjson
import structlog
from tenacity import retry, stop_after_attempt, wait_fixed

import utils


@dataclass
class AccountingMetrics:
    collected_lines: int = 0
    training_tokens: int = 0
    inference_tokens: int = 0
    sagemaker_seconds: int = 0
    numeric_fields: int = 0
    discrete_fields: int = 0
    file_size_bytes: int = 0

    def set(self, field_name: str, value):
        setattr(self, field_name, int(value))

    def add(self, field_name: str, value):
        setattr(self, field_name, getattr(self, field_name) + int(value))

    def dict(self):
        return {
            "collected_lines": self.collected_lines,
            "training_tokens": self.training_tokens,
            "inference_tokens": self.inference_tokens,
            "sagemaker_seconds": self.sagemaker_seconds,
            "numeric_fields": self.numeric_fields,
            "discrete_fields": self.discrete_fields,
            "file_size_bytes": self.file_size_bytes,
        }


class AccountingCollector:
    tenant_id: str
    anomaly_detector_id: str
    run_id: str
    job_start_time: int
    metrics: AccountingMetrics = AccountingMetrics()

    def __init__(
        self,
        *,
        tenant_id: str,
        anomaly_detector_id: str,
        run_id: str,
        job_start_time: int,
        path_config,
        metronome_url: str,
    ):
        self.logger = structlog.get_logger(
            "AccountingCollector",
            run_id=run_id,
            tenant_id=tenant_id,
            anomaly_detector_id=anomaly_detector_id,
            job_start_time=job_start_time,
        )
        self.metronome_client = httpx.Client(base_url=metronome_url)
        self.run_id = run_id
        self.tenant_id = tenant_id
        self.anomaly_detector_id = anomaly_detector_id
        self.job_start_time = job_start_time
        self.path_config = path_config

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def _save_to_metronome(self, data):
        self.metronome_client.put(
            f"/tenants/{self.tenant_id}/anomaly-detectors/{self.anomaly_detector_id}/runs/{self.run_id}",
            headers={"Content-Type": "application/json"},
            json=data,
        )

    def _save_to_hosted_s3(self, data):
        file = self.path_config.accounting_metrics(self.run_id)
        file.write_bytes(orjson.dumps(data))

    def save(self):
        self.logger.debug("Saving accounting metrics")
        ts = utils.iso_format(datetime.fromtimestamp(self.job_start_time, tz=timezone.utc))
        data = {**self.metrics.dict(), "ts": ts}
        try:
            self._save_to_hosted_s3(data)
            self._save_to_metronome(data)
            self.logger.info("Accounting metrics saved")
        except Exception:
            self.logger.exception("Failed to save accounting metrics")
