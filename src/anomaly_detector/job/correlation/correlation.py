from dataclasses import dataclass

import pandas as pd

from anomaly_detector.job.influxdb import InfluxPoint
from anomaly_detector.job.share import rising_edges_detection
from anomaly_detector.utils import column_tuple_to_str

from .correlation_marker import MetricMarkerCorrelation, ReportMarkersCorrelation


@dataclass
class RisingEdge:
    df: pd.DataFrame
    columns: set[tuple[str, str | None]]
    correlation: pd.DataFrame


@dataclass
class CorrelationResult:
    response: list[dict]
    rising_edges: list[RisingEdge]


def _min_max_normalize(df: pd.DataFrame):
    return (df - df.min()) / (df.max() - df.min())


CORRELATED_THRESHOLD = 0.5


def get_related_columns(corr_matrix: pd.DataFrame, col: str) -> list[str]:
    related_columns = corr_matrix[col][abs(corr_matrix[col]) > CORRELATED_THRESHOLD].index.to_list()
    if len(related_columns) <= 1:
        # omit self
        return []
    return related_columns


def corr_df_to_response(df: pd.DataFrame):
    response = []
    for col in df.columns:
        correlations = []
        for n, score in df[col].to_dict().items():
            if n != col and abs(score) > CORRELATED_THRESHOLD:
                correlations.append(
                    {
                        "marker_type": n[0],
                        "name": n[1],
                        "correlation_score": score,
                    },
                )
        if correlations:
            response.append(
                {
                    "marker_type": col[0],
                    "name": col[1],
                    "correlations": correlations,
                },
            )
    return response


def rising_edge_to_correlation_points(measurement: str, rising_edge: RisingEdge) -> list[InfluxPoint]:
    points = []
    columns = list(rising_edge.columns)
    for ts in rising_edge.df.index:
        for c1 in range(len(columns)):
            c1_tuple = columns[c1]
            for c2 in range(c1 + 1, len(columns)):
                c2_tuple = columns[c2]
                item = rising_edge.correlation[c1_tuple].loc[[c2_tuple]].iloc[0]  # type: ignore
                point = InfluxPoint(
                    measurement=measurement,
                    tags={"marker_1": column_tuple_to_str(c1_tuple), "marker_2": column_tuple_to_str(c2_tuple)},
                    fields={"correlation_score": float(item)},
                    timestamp=ts,
                )
                points.append(point)
    return points


def timestamps_detection(df: pd.DataFrame):
    corr_matrix = df.corr(method="spearman").fillna(0)
    rising_edges: list[RisingEdge] = []
    columns_pair_set: set[frozenset] = set()
    for col in corr_matrix.columns:
        related_columns = get_related_columns(corr_matrix, col)
        if not related_columns:
            continue
        min_max_df = _min_max_normalize(df[related_columns])
        for c in related_columns:
            if c != col and corr_matrix[col][c] < 0:
                min_max_df[c] = 1 - min_max_df[c]
        min_max_df["score"] = 1 - min_max_df.sum(axis=1) / len(related_columns)
        edges = rising_edges_detection(min_max_df, "score")
        edges = list(set(edges))
        columns_set = frozenset(related_columns)
        if columns_set not in columns_pair_set:
            rising_edges.append(
                RisingEdge(
                    df=min_max_df.iloc[edges],
                    columns=related_columns,  # type: ignore
                    correlation=corr_matrix[related_columns].loc[related_columns],
                ),
            )
            columns_pair_set.add(columns_set)
    return rising_edges


def correlate(
    report_markers_correlation: ReportMarkersCorrelation,
    metric_markers_correlation: MetricMarkerCorrelation,
):
    report_df = report_markers_correlation.retrieve_graphs()
    promql_df = metric_markers_correlation.retrieve_graphs()
    if report_df is None and promql_df is None:
        return CorrelationResult(response=[], rising_edges=[])
    merged_df = pd.concat([report_df, promql_df], axis=1)
    daily_correlation = merged_df.corr(method="spearman").fillna(0)
    rising_edges = timestamps_detection(merged_df)
    return CorrelationResult(
        response=corr_df_to_response(daily_correlation),
        rising_edges=rising_edges,
    )
