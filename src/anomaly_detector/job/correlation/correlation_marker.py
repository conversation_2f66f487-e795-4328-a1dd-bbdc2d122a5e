from collections.abc import Callable

import pandas as pd
from pendulum import DateTime
from pypika import Criterion, Table

import constants
from anomaly_detector.job.metric.metric import MetricTask
from anomaly_detector.job.share.panel import panel_bad_score_validation
from anomaly_detector.marker import PromMarker, metric_marker_name
from anomaly_detector.sks.marker import CustomMarker
from anomaly_detector.utils import prom_metric_name
from config import TIME<PERSON>ON<PERSON>
from custom_exceptions import TableNotFoundError
from greptime import SyncGreptimeClient


class ReportMarkersCorrelation:
    def __init__(
        self,
        *,
        anomaly_detector_id: str,
        report: dict,
        greptime_client: SyncGreptimeClient,
        start: DateTime,
        end: DateTime,
    ):
        self.daily_report = report
        self.correlated_threshold = 0.5
        self.anomaly_detector_id = anomaly_detector_id
        self.greptime_client = greptime_client
        self.start = start
        self.end = end

    def _10m_scores(self, panel: str, field: str | None = None) -> list[dict] | None:
        table = Table(prom_metric_name(self.anomaly_detector_id, panel))
        conditions = [
            table.greptime_timestamp >= int(self.start.timestamp() * 1000),
            table.greptime_timestamp < int(self.end.timestamp() * 1000),
            table.inference == "10min",
        ]
        if field:
            conditions.append(table.field_name == field)
        query = table.select(table.greptime_value, table.greptime_timestamp).where(Criterion.all(conditions))
        try:
            return self.greptime_client.execute(query)
        except TableNotFoundError:
            return None

    def _create_dataframe(
        self,
        response: list[dict] | None,
        column_name: tuple[str, str | None],
    ) -> pd.DataFrame | None:
        if not response:
            return None

        return (
            pd.DataFrame(response)
            .rename(
                columns={
                    "greptime_value": column_name,
                    "greptime_timestamp": constants.DF_TIMESTAMP_COL,
                },
            )
            .set_index(constants.DF_TIMESTAMP_COL)
        )

    def _process_field_panel(
        self,
        panel: str,
        metric_name: str,
        panel_report: dict,
        validate_fn: Callable[[dict], bool],
    ) -> list[pd.DataFrame]:
        """Process panels that have field-level data (numeric_fields, discrete_fields)."""
        dfs = []
        for field, result in panel_report.items():
            if validate_fn(result):
                response = self._10m_scores(metric_name, field)
                df = self._create_dataframe(response, (panel, field))
                if df is not None:
                    dfs.append(df)
        return dfs

    def _process_panel_level(self, panel: str, metric_name: str, _: dict) -> list[pd.DataFrame]:
        """Process panels that have panel-level data (notable_fields, log_structure)."""
        response = self._10m_scores(metric_name)
        df = self._create_dataframe(response, (panel, None))
        return [df] if df is not None else []

    def retrieve_graphs(self) -> pd.DataFrame | None:
        # Early return if no daily report
        if not self.daily_report:
            return None

        dfs = []

        # Define panel processing strategies
        field_level_panels = {"numeric_fields", "discrete_fields"}
        panel_level_panels = {"notable_fields", "log_structure"}
        unimplemented_panels = {"log_rate", "sequence_pattern"}

        for panel, validate_fn in panel_bad_score_validation.items():
            panel_report = self.daily_report.get(panel, {})

            # Skip if no report data
            if not panel_report:
                continue

            metric_name = constants.PANEL_METRIC_NAME_MAPPING[panel]

            if panel in field_level_panels:
                # Process field-level panels
                panel_dfs = self._process_field_panel(panel, metric_name, panel_report, validate_fn)
                dfs.extend(panel_dfs)

            elif panel in panel_level_panels and panel_report and validate_fn(panel_report):
                # Process panel-level panels
                panel_dfs = self._process_panel_level(panel, metric_name, panel_report)
                dfs.extend(panel_dfs)

            elif panel in unimplemented_panels:
                # TODO: implement these panels
                pass

        # Combine all DataFrames
        if not dfs:
            return None

        merged_df = pd.concat(dfs, axis=1).fillna(1)
        merged_df.index = pd.to_datetime(merged_df.index, unit="ms", utc=True).tz_convert(TIMEZONE)
        return merged_df.sort_index()


class MetricMarkerCorrelation:
    def __init__(
        self,
        metric_tasks: list[MetricTask],
    ):
        self.metric_tasks = metric_tasks

    def retrieve_graphs(self, resample_freq: str = "10min") -> pd.DataFrame | None:
        dfs = []
        for metric_task in self.metric_tasks:
            if isinstance(metric_task.marker, PromMarker):
                original_df = metric_task.get_value_prophecy_df()
                if original_df.empty:
                    continue
                labels = original_df.attrs.get("labels", [])
                value_column = metric_task.marker.name
                groups = [((), original_df)] if not labels else list(original_df.groupby(labels))
                for label_values, group in groups:
                    name = metric_marker_name(value_column, dict(zip(labels, label_values, strict=False)))
                    series_df = group.set_index(constants.DF_TIMESTAMP_COL)
                    scores = series_df["is_outlier"].resample(resample_freq).mean()
                    column_tuple = ("metric", name)
                    scores = pd.DataFrame({column_tuple: scores})
                    dfs.append(scores)
            elif isinstance(metric_task.marker, CustomMarker):
                if metric_task.marker.qoe_score:
                    qoe_df = metric_task.get_value_df()
                    if qoe_df is None or isinstance(qoe_df, list) or qoe_df.empty:
                        continue
                    qoe_df = qoe_df.resample(resample_freq).mean()
                    qoe_df = qoe_df.rename(columns=lambda col: ("qoe", col))
                    dfs.append(qoe_df)
        if dfs:
            return pd.concat(dfs, axis=1)
        return None
