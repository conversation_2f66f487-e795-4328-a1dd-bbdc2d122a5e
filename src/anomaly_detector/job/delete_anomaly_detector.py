from anomaly_detector.job.dependency import ServiceDependencies
from anomaly_detector.job.option import LADOptions
from anomaly_detector.job.share.utils import rmdir_recursive
from anomaly_detector.s3_path_config import S3PathConfig
from config import Settings
from custom_exceptions import ServiceError
from s3 import register_aws_s3, register_hosted_s3


def delete_anomaly_detector(
    option: LADOptions,
    settings: Settings,
    service_dependencies: ServiceDependencies,
):
    register_hosted_s3(settings, service_dependencies.s3)
    register_aws_s3(settings)
    path_config = S3PathConfig(
        anomaly_detector_id=option.anomaly_detector_id,
        hosted_s3_base_path=settings.s3.base_path,
        inference_date=option.end,
    )
    try:
        rmdir_recursive(path_config.anomaly_detector_base_path)
    except Exception as err:
        raise ServiceError("Failed to delete anomaly detector data") from err
