from dataclasses import dataclass

from config import S3Settings


@dataclass
class ServiceDependencies:
    greptime: str
    s3: str
    loki_frontend: str | None = None
    metronome: str | None = None
    mimir_frontend: str | None = None
    mimir_proxy: str | None = None
    llamington: str | None = None
    qdrant: str | None = None
    ollama: str | None = None


def get_hosted_s3_storage_options(s3_settings: S3Settings, s3_url: str):
    return {
        "aws_region": "us-east-1",
        "access_key_id": s3_settings.username,
        "secret_access_key": s3_settings.password,
        "endpoint_url": s3_url,
        "bucket": s3_settings.bucket,
        "aws_allow_http": "true",
    }
