import math
import random
from collections import defaultdict
from collections.abc import Mapping
from dataclasses import dataclass, field
from datetime import datetime, timezone

import orjson
import pandas as pd
import polars as pl
import polars.selectors as cs
import s3path
import structlog

from anomaly_detector.config import AnomalyDetectorSettings
from anomaly_detector.job.accounting import <PERSON><PERSON>ollector
from anomaly_detector.job.fields.histogram.jsd import histogram_jsd
from anomaly_detector.job.fields.numeric_fields import (
    HDBS<PERSON>NR<PERSON>ult,
    fit_hdbscan,
    hdbscan_outlier,
    hopkins_statistic_with_quadrant,
)
from anomaly_detector.job.preprocess.log_timeranges import LogTimeRangesSample
from anomaly_detector.job.preprocess.preprocessed_reader import PreprocessedReader
from anomaly_detector.job.share import (
    Histogram,
    LogSamplePersist,
    PrioritySamples,
    TaskType,
)
from anomaly_detector.job.share.log_sample import BaseSamples
from anomaly_detector.job.utils import flatten_dict_keys
from anomaly_detector.s3_path_config import S3PathConfig
from constants import SPECIAL_KEYS, TEMPLATE_KEY, TIMESTAMP_KEY
from llm.baseline_context import BaselineContext
from llm.contextual_ranking import ContextualRanking, ProblematicLLMLogGroup, ProblematicLogGroups
from vector_db.vector_search import VectorSearch

# Constants for magic values
TEMPLATE_SCORE_THRESHOLD = 0.2
HOPKINS_SCORE_THRESHOLD = 0.5
MIN_HISTOGRAM_ROWS = 10
JSD_THRESHOLD = 0.2

BAD_TEMPLATE_K = -4


def _calculate_score(train_mapping, inference_mapping, t):
    expected = train_mapping.get(t, {}).get("percentage", 0)
    actual = inference_mapping.get(t, {}).get("percentage", 0)
    if expected == 0:
        return 1
    # score = 1 - math.exp(BAD_TEMPLATE_K * abs(actual - expected) * expected)
    # return score
    return abs((actual - expected) / expected)


def _derive_drain_bad_template_samples(jsd_result: dict, template_samples: Mapping[str, BaseSamples[str]]):
    train_template_mapping = {
        v["value"]: v for v in jsd_result.get("top_k_with_others", {}).get("train", {}).get("values", [])
    }
    inference_template_mapping = {
        v["value"]: v for v in jsd_result.get("top_k_with_others", {}).get("inference", {}).get("values", [])
    }

    all_templates = set(train_template_mapping.keys()) | set(inference_template_mapping.keys())

    all_templates = list(filter(lambda x: x in template_samples, all_templates))
    bad_templates = sorted(
        filter(
            lambda x: _calculate_score(train_template_mapping, inference_template_mapping, x)
            > TEMPLATE_SCORE_THRESHOLD,
            all_templates,
        ),
        key=lambda x: _calculate_score(train_template_mapping, inference_template_mapping, x),
        reverse=True,
    )
    if not bad_templates:
        return []

    sample_per_template = max(math.ceil(500 / len(bad_templates)), 1)
    samples = []
    for i in range(sample_per_template):
        for tid in bad_templates:
            sorted_samples = template_samples[tid].items
            increased = train_template_mapping.get(tid, {}).get("percentage", 0) < inference_template_mapping.get(
                tid,
                {},
            ).get("percentage", 0)
            if i < len(sorted_samples):
                samples.append({"ref": str(tid), "log": sorted_samples[i], "increased": increased})
    return samples


@dataclass
class DrainInferenceSamples:
    problematic_log_groups: ProblematicLogGroups | None
    samples: list[str] = field(default_factory=list)
    template_samples: dict[str, list[str]] = field(default_factory=dict)


class FieldTask:
    def __init__(
        self,
        flow_id: str,
        anomaly_detector_id: str,
        task_type: TaskType,
        *,
        anomaly_detector_settings: AnomalyDetectorSettings,
        contextual_ranking: ContextualRanking,
        preprocessed_reader: PreprocessedReader,
        parquet_storage_options: dict | None,
        path_config: S3PathConfig,
        accounting_collector: AccountingCollector,
        vector_search: VectorSearch,
        date=datetime.now(timezone.utc),
        baseline_context: BaselineContext | None = None,
        time_ranges_sample: LogTimeRangesSample | None = None,
        full_inference: bool = False,
    ):
        self.flow_id = flow_id
        self.task_type = task_type
        self.preprocessed_reader = preprocessed_reader
        self.vector_search = vector_search
        self.hdbscan_result: dict[str, HDBSCANResult] = {}
        self.trained_histograms: dict[str, Histogram] = {}
        self.full_inference = full_inference
        self.drain_histograms: Histogram | None = None
        self.parquet_storage_options = parquet_storage_options
        self.baseline_context = baseline_context
        self.logger = structlog.get_logger(
            "field_task",
            flow_id=flow_id,
            anomaly_detector_id=anomaly_detector_id,
            full_inference=full_inference,
            task=self.task_type,
            date=date,
        )
        self.task_date = date
        self.time_ranges_sample = time_ranges_sample
        self.path_config = path_config
        self.anomaly_detector_settings = anomaly_detector_settings
        self.fields_hist = preprocessed_reader.field_occurrence_hist()
        self.contextual_ranking = contextual_ranking
        self.accounting_collector = accounting_collector
        self.original_df: pl.LazyFrame | None = self._load_parquet_file(preprocessed_reader.parquet_files())
        if self.original_df is None:
            self.total_rows = 0
            self.valid_columns = []
            self.df = pl.LazyFrame()
        else:
            self.total_rows = int(self.original_df.select(pl.len()).collect()[0, 0])
            self.valid_columns = list(
                filter(
                    lambda x: x in self.fields_hist.cardinalities
                    and self.fields_hist.cardinalities[x] / self.total_rows
                    > anomaly_detector_settings.min_percentage_to_keep,
                    self.original_df.collect_schema().names(),
                ),
            )
            self.df = self._select_valid_columns(self.original_df)
        self.accounting_collector.metrics.set("collected_lines", self.total_rows)

    def save_fields_occurrence(self):
        fields_occurrence_path = self.path_config.trained_fields_occurrence
        fields_occurrence_path.write_bytes(orjson.dumps(self.fields_hist))

    def save_drain_histogram(self):
        drain_histogram_path = self.path_config.trained_drain_histogram
        drain_histogram_path.write_bytes(orjson.dumps(self.drain_histograms))

    def _calculate_hdbscan_cluster_percentage(self, df: pl.DataFrame, clusters: list, total_rows: int):
        result: dict[str, float] = {}
        for cluster in clusters:
            label = cluster["label"]
            if label == -1:
                continue
            actual = (
                df.filter((pl.col(df.columns[0]) >= cluster["min"]) & (pl.col(df.columns[0]) <= cluster["max"]))
                .select(pl.count())
                .item()
            )
            result[str(label)] = actual / total_rows
        return result

    def train_hdbscan(self):
        self.logger.info("start train hdbscan")
        assert self.df is not None, "DataFrame is not loaded"
        numeric_df = self.df.select(cs.numeric() - cs.by_name(SPECIAL_KEYS))
        sample_size = self.anomaly_detector_settings.numeric_field.samples_per_cycle
        hdbscan_result: dict[str, HDBSCANResult] = {}
        for col in numeric_df.collect_schema().names():
            self.logger.debug("start hdbscan", column=col)
            df = numeric_df.filter(pl.col(col).is_not_null()).select(pl.col(col)).collect()
            sampled_df = df
            hopkins_df = df
            total_rows = df.shape[0]
            if total_rows > sample_size:
                sampled_df = df.sample(n=sample_size)
            if total_rows > sample_size * 10:
                hopkins_df = df.sample(n=sample_size * 10)
            hopkins_stats = hopkins_statistic_with_quadrant(
                hopkins_df,
                hopkins_sample=self.anomaly_detector_settings.numeric_field.hopkins_sample,
            )
            passed_count = sum(
                [s >= self.anomaly_detector_settings.numeric_field.valid_hopkins_score for s in hopkins_stats],
            )
            valid_hopkins_test = passed_count / len(hopkins_stats) >= HOPKINS_SCORE_THRESHOLD
            self.logger.debug("hopkins score", column=col, scores=hopkins_stats)

            if not valid_hopkins_test:
                continue

            self.logger.debug("start hdbscan", column=col, samples=sampled_df.shape[0], total=df.shape[0])
            hdbscan_clusters = fit_hdbscan(
                sampled_df,
                hdbscan_min_cluster_size=self.anomaly_detector_settings.numeric_field.hdbscan_min_cluster_size,
                hdbscan_min_samples=self.anomaly_detector_settings.numeric_field.hdbscan_min_samples,
            )
            cluster_percentages = self._calculate_hdbscan_cluster_percentage(df, hdbscan_clusters, total_rows)
            hdbscan_result[col] = HDBSCANResult(
                score=hopkins_stats,
                clusters=hdbscan_clusters,
                rows=total_rows,
                cluster_percentages=cluster_percentages,
            )
            self.logger.debug("hdbscan clusters", column=col, clusters=hdbscan_result[col].clusters)
        self.accounting_collector.metrics.set("numeric_fields", len(hdbscan_result))
        return hdbscan_result

    def save_hdbscan_result(self, hdbscan_result: dict[str, HDBSCANResult]):
        self.hdbscan_result = hdbscan_result
        hdbscan_path = self.path_config.trained_hdbscan
        r = {col: result.to_dict() for col, result in hdbscan_result.items() if col not in SPECIAL_KEYS}
        hdbscan_path.write_bytes(orjson.dumps(r))

    def load_hdbscan(self):
        hdbscan_path = self.path_config.trained_hdbscan
        self.hdbscan_result = {
            field: HDBSCANResult.load_from_dict(obj) for field, obj in orjson.loads(hdbscan_path.read_bytes()).items()
        }
        self.hdbscan_result = flatten_dict_keys(self.hdbscan_result)

    def load_trained_histograms(self):
        histogram_path = self.path_config.trained_histograms
        self.trained_histograms = {
            field: Histogram.from_object(obj) for field, obj in orjson.loads(histogram_path.read_bytes()).items()
        }
        self.trained_histograms = flatten_dict_keys(self.trained_histograms)

    def save_trained_histograms(self, trained_hist: dict[str, Histogram]):
        self.trained_histograms = trained_hist
        histogram_path = self.path_config.trained_histograms
        hist = {
            field: hist.to_object()
            for field, hist in self.trained_histograms.items()
            if field not in SPECIAL_KEYS and field in self.valid_columns
        }
        self.accounting_collector.metrics.set("discrete_fields", len(hist))
        histogram_path.write_bytes(orjson.dumps(hist))

    def inference_discrete_fields(self, inference_hist: dict[str, Histogram]):
        self.load_trained_histograms()
        jsd_results = {}
        for field_name in self.trained_histograms.keys() | inference_hist.keys():
            if field_name not in self.trained_histograms and field_name not in self.valid_columns:
                continue
            jsd = histogram_jsd(
                self.trained_histograms.get(field_name, None),
                inference_hist.get(field_name),
                self.anomaly_detector_settings.min_percentage_to_keep,
                bins=self.anomaly_detector_settings.histogram_field.bins,
            )
            self.logger.debug("jsd", field=field_name, jsd=jsd, method="jsd_histograms")
            jsd_results[field_name] = jsd
        self.accounting_collector.metrics.set("discrete_fields", len(jsd_results))
        self.logger.debug("jsd results", results=jsd_results)
        return jsd_results

    def inference_notable_fields(self):
        def remove_special_fields(hist):
            cardinalities = {
                k: v
                for k, v in hist.cardinalities.items()
                if k not in SPECIAL_KEYS
                and not k.endswith('@country"]')
                and not k.endswith('@city"]')
                and not k.endswith('@subdivisions"]')
            }
            return Histogram(total_rows=hist.total_rows, cardinalities=defaultdict(int, cardinalities))

        trained_occurrence_hist_path = self.path_config.trained_fields_occurrence
        trained_occurrence_hist = remove_special_fields(
            Histogram.from_object(orjson.loads(trained_occurrence_hist_path.read_bytes())),
        )
        inference_occurrence_hist = remove_special_fields(self.fields_hist)
        jsd = histogram_jsd(
            trained_occurrence_hist,
            inference_occurrence_hist,
            self.anomaly_detector_settings.min_percentage_to_keep,
            bins=self.anomaly_detector_settings.histogram_field.bins,
        )
        self.logger.info("success inference notable fields")
        return jsd

    def drain_timerange_samples(self):
        time_range_sample_size = 30
        trained_hist = Histogram.from_object(orjson.loads(self.path_config.trained_drain_histogram.read_bytes()))
        all_samples = []
        if not self.time_ranges_sample:
            return []
        for time_sample in self.time_ranges_sample.time_range_samples:
            if time_sample.histogram.total_rows < MIN_HISTOGRAM_ROWS:
                continue
            jsd = histogram_jsd(
                trained_hist,
                time_sample.histogram,
                self.anomaly_detector_settings.min_percentage_to_keep,
                bins=self.anomaly_detector_settings.histogram_field.bins,
            )
            if jsd["score"] < JSD_THRESHOLD:
                continue
            all_samples += _derive_drain_bad_template_samples(jsd, time_sample.template_samples)
        if len(all_samples) > time_range_sample_size:
            return random.sample(all_samples, time_range_sample_size)
        return all_samples

    def llm_drain_bad_templates_problematic_log_groups(self, log_samples: LogSamplePersist, jsd_result: dict):
        overall_sample_size = 50
        samples = _derive_drain_bad_template_samples(jsd_result, log_samples.log_samples)
        default_problematic_log_groups = ProblematicLogGroups(vector_search=self.vector_search, ranked_groups=[])
        if not samples:
            return default_problematic_log_groups

        timerange_samples = self.drain_timerange_samples()
        mixed_samples = samples + timerange_samples

        if mixed_samples:
            problematic_log_groups = self.contextual_ranking.invoke(
                total_lines=self.df.select(pl.count()).collect().item(),
                sample_size=overall_sample_size,
                logs=[f"{s['log']}. {'increased' if s['increased'] else 'decreased'}" for s in mixed_samples],
                baseline_context=self.baseline_context,
            )
            problematic_log_groups.set_samples_based_on_index(mixed_samples, lambda x: x["log"])
            return problematic_log_groups
        return default_problematic_log_groups

    def llm_drain_keywords_problematic_log_groups(self, log_samples: LogSamplePersist) -> ProblematicLogGroups:
        normal_sample_size = 40
        severity_sample_per_template = 2
        severity_sample_size = 80
        all_samples = []
        severity_logs = []
        if self.drain_histograms is None:
            raise ValueError("drain histograms not trained")
        for tpl, count in self.drain_histograms.cardinalities.items():
            per = count / self.drain_histograms.total_rows
            if per >= self.anomaly_detector_settings.min_percentage_to_keep:
                continue
            tpl_samples = log_samples.log_samples.get(str(tpl))
            if tpl_samples and isinstance(tpl_samples, PrioritySamples):
                samples, severity_samples = tpl_samples.sample(priority_threshold=1, sample_size=5, keep_severity=True)
                all_samples += samples
                if severity_samples:
                    if len(severity_samples) > severity_sample_per_template:
                        severity_samples = random.sample(severity_samples, severity_sample_per_template)
                    severity_logs += severity_samples
        if len(all_samples) > normal_sample_size:
            all_samples = random.sample(all_samples, normal_sample_size)
        if len(severity_logs) > severity_sample_size:
            severity_logs = random.sample(severity_logs, severity_sample_size)
        problematic_log_groups = self.contextual_ranking.invoke(
            total_lines=self.df.select(pl.count()).collect().item(),
            sample_size=normal_sample_size,
            logs=all_samples,
            baseline_context=self.baseline_context,
        )
        problematic_log_groups.set_samples_based_on_index(all_samples)
        if severity_logs:
            problematic_log_groups.ranked_groups.insert(
                0,
                ProblematicLLMLogGroup(
                    description="Logs have high severity",
                    title="Severity Logs",
                    rankings=[5] * len(severity_logs),
                    samples=severity_logs,
                    samples_index=list(range(len(severity_logs))),
                ),
            )
        return problematic_log_groups

    def inference_drain_samples(
        self,
        jsd_result: dict,
        *,
        problematic_log_groups: ProblematicLogGroups | None = None,
    ) -> DrainInferenceSamples:
        log_sample_persist = LogSamplePersist(self.path_config.inference_log_samples)
        log_sample_persist.load()
        template_samples = {tid: ps.items[:10] for tid, ps in log_sample_persist.log_samples.items()}
        samples = []
        if not problematic_log_groups:
            try:
                problematic_log_groups = self.llm_drain_bad_templates_problematic_log_groups(
                    log_sample_persist,
                    jsd_result,
                )
                if problematic_log_groups and len(problematic_log_groups.ranked_groups) > 0:
                    samples = problematic_log_groups.per_group_samples(
                        maximum=50,
                        per_group=max(50 // len(problematic_log_groups.ranked_groups), 5),
                    )
                else:
                    samples = []
            except Exception:
                self.logger.exception("failed to get problematic log groups")

        return DrainInferenceSamples(
            template_samples=template_samples,
            problematic_log_groups=problematic_log_groups,
            samples=samples,
        )

    def inference_drain_histogram(self, skip_drain: bool = False):  # noqa: ARG002
        trained_hist_path = self.path_config.trained_drain_histogram
        trained_hist = Histogram.from_object(orjson.loads(trained_hist_path.read_bytes()))
        jsd = histogram_jsd(
            trained_hist,
            self.drain_histograms,
            self.anomaly_detector_settings.min_percentage_to_keep,
            bins=self.anomaly_detector_settings.histogram_field.bins,
        )
        self.logger.info("success inference drain histogram")
        return jsd

    def _inference_numeric_field(
        self,
        field_df: pl.DataFrame | None = None,
        trained_hdbscan: HDBSCANResult | None = None,
        inference_hdbscan: HDBSCANResult | None = None,
    ):
        if not trained_hdbscan and inference_hdbscan:
            inference_result = inference_hdbscan.to_dict()
            inference_result["trained_noise"] = 1.0
            return {
                "inference": inference_result,
            }
        if trained_hdbscan and (field_df is None or field_df.shape[0] == 0):
            # column is empty during inference, but present in training
            outliers = []
            inference_result = {
                "trained_noise": 1,
                "trained_percentage": {
                    str(k): {"expected": v, "actual": 0.0} for k, v in trained_hdbscan.cluster_percentages.items()
                },
            }
        else:
            if field_df is None:
                raise ValueError("field_df is None")
            rows = field_df.shape[0]
            inference_result = inference_hdbscan.to_dict() if inference_hdbscan else {}
            if trained_hdbscan is None:
                inference_result["trained_noise"] = 1.0
                return {"inference": inference_result}
            outliers = (
                hdbscan_outlier(field_df, trained_hdbscan.clusters)[field_df.columns[0]].to_pandas().sort_values()
            )
            if not len(outliers):  # column has rows but no outliers
                inference_result["trained_noise"] = 0
                outliers = []
            else:
                min_value = min(outliers.min(), *(x["min"] for x in trained_hdbscan.clusters))
                max_value = max(outliers.max(), *(x["max"] for x in trained_hdbscan.clusters))
                step = (max_value - min_value) / self.anomaly_detector_settings.numeric_field.bins
                outlier_bins, bins = pd.cut(
                    outliers,
                    bins=[min_value + step * i for i in range(self.anomaly_detector_settings.numeric_field.bins + 1)],
                    ordered=True,
                    include_lowest=True,
                    retbins=True,
                )
                outliers_count = outlier_bins.value_counts().sort_index()
                outliers = [
                    {
                        "count": op,
                        "percentage": op / rows,
                        "value": bins[index].item(),
                    }
                    for index, op in enumerate(outliers_count.to_list())
                ]
                inference_result["trained_noise"] = len(outliers) / rows
            actual_percentage = self._calculate_hdbscan_cluster_percentage(field_df, trained_hdbscan.clusters, rows)
            trained_percentage = {
                label: {
                    "expected": expected,
                    "actual": actual_percentage.get(label, 0),
                }
                for label, expected in trained_hdbscan.cluster_percentages.items()
            }
            inference_result["trained_percentage"] = trained_percentage

        return {
            "trained": trained_hdbscan.to_dict() if trained_hdbscan else {},
            "outliers": outliers,
            "inference": inference_result,
        }

    def inference_numeric_fields(self, *, skip_hdbscan=False):
        assert self.original_df is not None, "DataFrame is not loaded"
        self.load_hdbscan()
        inference_hdbscan = self.train_hdbscan() if not skip_hdbscan else {}
        trained_hdbscan = self.hdbscan_result
        result = {}

        for field_name in trained_hdbscan.keys() | inference_hdbscan.keys():
            inference_df = (
                self.original_df.filter(pl.col(field_name).is_not_null()).select(pl.col(field_name)).collect()
                if field_name in self.original_df
                else None
            )
            result[field_name] = self._inference_numeric_field(
                inference_df,
                trained_hdbscan.get(field_name, None),
                inference_hdbscan.get(field_name, None),
            )

        self.logger.info("success inference numeric fields")
        return result

    def _select_valid_columns(self, df: pl.LazyFrame):
        self.drain_histograms = Histogram(
            total_rows=self.total_rows,
            cardinalities=defaultdict(
                int,
                df.group_by(TEMPLATE_KEY)
                .agg(occurrences=pl.col(TEMPLATE_KEY).count())
                .with_columns(pl.col("occurrences").cast(pl.Int32))
                .with_columns(pl.col(TEMPLATE_KEY).cast(pl.Utf8))
                .collect()
                .to_pandas()
                .set_index(TEMPLATE_KEY)
                .to_dict()["occurrences"],
            ),
        )
        self.logger.debug(
            "valid columns",
            columns=self.valid_columns,
            total_rows=self.total_rows,
            occurrence=self.fields_hist,
        )
        return df.select(self.valid_columns)

    def _load_parquet_file(self, parquet_files: list[s3path.S3Path]):
        files = list(filter(lambda f: f.exists(), parquet_files))
        if not files:
            return None
        ldf = [pl.scan_parquet(f.as_uri(), storage_options=self.parquet_storage_options) for f in files]
        return pl.concat(ldf, how="diagonal_relaxed", parallel=True).sort(TIMESTAMP_KEY)
