import dataclasses
from collections import defaultdict

import numpy as np
from scipy.spatial.distance import j<PERSON><PERSON><PERSON><PERSON>

from anomaly_detector.job.share import Histogram


def top_k_with_others_df(hist: Histogram, *, bins: int):
    percentage = hist.to_percentage_df()
    top_k = percentage.nlargest(bins, "percentage")
    others_percentage = percentage.loc[~percentage.index.isin(top_k.index)].sum()
    return top_k, others_percentage


def remove_hist_low_cardinality(
    hist: Histogram,
    percentage: float,
    keep_other_hist: Histogram | None = None,
):
    new_cardinalities = defaultdict(
        int,
        {
            k: v
            for k, v in hist.cardinalities.items()
            if (v / hist.total_rows) >= percentage or (keep_other_hist and k in keep_other_hist.cardinalities)
        },
    )
    return dataclasses.replace(hist, cardinalities=new_cardinalities)


def histogram_jsd(
    hist_trained: Histogram | None = None,
    hist_inference: Histogram | None = None,
    remove_low_cardinality_percentage: float | None = None,
    *,
    bins: int,
):
    """Calculate the Jensen-Shannon divergence between two histograms.

    e.g
    training: { A: 0.1, B: 0.5, C: 0.4, F: 0.01 },
    inference: { A: 0.1, B: 0.2, D: 0.5, E: 0.3, F:0.005, G:0.005 }

    D and E will go to the "others" for calculating the jsd.
    F will be kept, as it is present in training.
    G will be ignored, as it's less than 0.01

    {"score": jsd([0.1, 0.5, 0.4, 0.01, 0.0], [0.1, 0.2, 0.0, 0.005, 0.8]),  the last one is others.
     "top_k_with_others: the same as the input.}

    if it was present in training, but empty in inference, score 1, return trained histogram, and all zero in inference histogram.
    if it was not in training, but preset in inference, score 1, return inference histogram.
    """
    # Early return for both None case
    if hist_trained is None and hist_inference is None:
        return {"score": 1}

    top_k_with_others = {}

    # Process inference histogram if it exists
    def process_inference_histogram(hist_inf, keep_other_hist=None):
        if remove_low_cardinality_percentage is not None:
            hist_inf = remove_hist_low_cardinality(hist_inf, remove_low_cardinality_percentage, keep_other_hist)
        inference_top_k, inference_others = top_k_with_others_df(hist_inf, bins=bins)
        return {
            "values": inference_top_k.reset_index().to_dict(orient="records"),
            "others": inference_others.to_dict(),
        }

    # Case 1: Only inference data exists
    if hist_trained is None:
        jsd_score = 1
        top_k_with_others["inference"] = process_inference_histogram(hist_inference)

    # Case 2: Training data exists
    else:
        if remove_low_cardinality_percentage is not None:
            hist_trained = remove_hist_low_cardinality(hist_trained, remove_low_cardinality_percentage)
        trained_top_k, trained_others = top_k_with_others_df(hist_trained, bins=bins)
        top_k_with_others["train"] = {
            "values": trained_top_k.reset_index().to_dict(orient="records"),
            "others": trained_others.to_dict(),
        }

        # Case 2a: No inference data - create zero inference
        if hist_inference is None:
            jsd_score = 1
            top_k_with_others["inference"] = {
                "values": [
                    {"value": v, "count": 0, "percentage": 0} for v in trained_top_k.reset_index().value.to_list()
                ],
                "others": {"count": 0, "percentage": 0},
            }

        # Case 2b: Both training and inference data exist - calculate JSD
        else:
            top_k_with_others["inference"] = process_inference_histogram(hist_inference, hist_trained)

            percentage_inference = hist_inference.to_percentage_df()
            inference_df = (
                percentage_inference[percentage_inference.index.isin(trained_top_k.index)]
                .reindex(trained_top_k.index)
                .fillna(0)
            )
            inference_jsd_others_percentage = (
                percentage_inference.loc[~percentage_inference.index.isin(trained_top_k.index)]
                .sum()["percentage"]
                .item()
            )
            train_hist = [*trained_top_k["percentage"].to_list(), trained_others["percentage"]]
            inference_hist = [*inference_df["percentage"].to_list(), inference_jsd_others_percentage]

            jsd_score = jensenshannon(train_hist, inference_hist).item()
            if np.isnan(jsd_score):
                # if the distances are extremely close, it can be NaN,
                # it's a bug of scipy https://github.com/scipy/scipy/pull/19438
                jsd_score = 0
    return {"score": jsd_score, "top_k_with_others": top_k_with_others}
