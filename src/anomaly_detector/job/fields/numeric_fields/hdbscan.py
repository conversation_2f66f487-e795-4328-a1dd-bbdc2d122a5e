from random import sample

import numpy as np
import polars as pl
from numpy.random import uniform
from sklearn import cluster
from sklearn.neighbors import NearestNeighbors


def fit_hdbscan(df: pl.DataFrame, *, hdbscan_min_cluster_size: float, hdbscan_min_samples: float):
    value_name = df.columns[0]
    cluster_size = int(len(df) * hdbscan_min_cluster_size)
    min_samples = int(len(df) * hdbscan_min_samples)
    clusterer = cluster.HDBSCAN(
        min_cluster_size=cluster_size,
        min_samples=min_samples,
        allow_single_cluster=True,
    )
    clusterer.fit(df)
    result_df = (
        df.with_columns(
            pl.Series("hdbscan_labels", clusterer.labels_, strict=False),
        )
        .group_by("hdbscan_labels")
        .agg(
            pl.col(value_name).min().alias("min"),
            pl.col(value_name).max().alias("max"),
            pl.col(value_name).median().alias("median"),
            pl.col(value_name).mean().alias("mean"),
        )
    ).to_dict(as_series=False)

    return [
        {
            "label": label,
            "min": result_df["min"][i],
            "max": result_df["max"][i],
            "median": result_df["median"][i],
            "mean": result_df["mean"][i],
        }
        for i, label in enumerate(result_df["hdbscan_labels"])
    ]


def hdbscan_outlier(df: pl.DataFrame, clusters: list[dict]):
    column_name = df.columns[0]
    filter_selector = None
    for c in clusters:
        if c["label"] == -1:
            continue
        range_filter = (pl.col(column_name) <= c["max"]) & (pl.col(column_name) >= c["min"])
        filter_selector = range_filter if filter_selector is None else filter_selector | range_filter
    return df.filter(~filter_selector) if filter_selector is not None else df


def hopkins_statistic(series: pl.DataFrame, *, hopkins_sample: float):
    sample_size = int(series.shape[0] * hopkins_sample)  # 0.05 (5%) based on paper by Lawson and Jures
    if sample_size < 2:
        return 0

    # a uniform random sample in the original data space
    uniform_random_sample = uniform(series.min(), series.max(), (sample_size, 1))

    # a random sample of size sample_size from the original data X
    random_indices = sample(range(0, series.shape[0], 1), sample_size)
    samples = series[random_indices]

    # initialise unsupervised learner for implementing neighbor searches
    neigh = NearestNeighbors(n_neighbors=2)
    # np_series = series.to_numpy().reshape(-1, 1)
    nbrs = neigh.fit(series)

    # u_distances = nearest neighbour distances from uniform random sample
    u_distances, u_indices = nbrs.kneighbors(uniform_random_sample, n_neighbors=2)
    u_distances = u_distances[:, 0]  # distance to the first (nearest) neighbour

    # w_distances = nearest neighbour distances from a sample of points from original data X
    w_distances, w_indices = nbrs.kneighbors(samples, n_neighbors=2)
    # distance to the second nearest neighbour (as the first neighbour will be the point itself, with distance = 0)
    w_distances = w_distances[:, 1]

    u_sum = np.sum(u_distances)
    w_sum = np.sum(w_distances)

    # compute and return hopkins' statistic
    return (u_sum / (u_sum + w_sum)).item()


def hopkins_statistic_with_quadrant(df: pl.DataFrame, hopkins_sample: float, quadrant: int = 3):
    slice_size = len(df) // quadrant
    hopkins_stats = []
    for i in range(quadrant):
        hopkins_stats.append(hopkins_statistic(df.slice(i * slice_size, slice_size), hopkins_sample=hopkins_sample))
    return hopkins_stats
