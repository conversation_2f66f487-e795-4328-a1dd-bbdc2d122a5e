class HDBSCANResult:
    score: list[float]
    clusters: list[dict]
    rows: int
    cluster_percentages: dict[str, float]

    def __init__(
        self,
        score: list[float],
        clusters: list[dict],
        rows: int,
        cluster_percentages: dict[str, float] | None = None,
    ):
        self.score = score
        self.clusters = clusters
        self.rows = rows
        self.cluster_percentages = cluster_percentages or {}

    def to_dict(self):
        return {
            "score": self.score,
            "clusters": self.clusters,
            "rows": self.rows,
            "cluster_percentages": self.cluster_percentages,
        }

    @classmethod
    def load_from_dict(cls, obj):
        clusters = obj["clusters"] if "clusters" in obj else obj["hdbscan"]
        return cls(
            score=obj["score"],
            clusters=clusters,
            rows=obj["rows"],
            cluster_percentages=obj.get("cluster_percentages", {}),
        )
