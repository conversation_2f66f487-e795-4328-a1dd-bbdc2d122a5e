import contextlib
import pathlib
from typing import TYPE_CHECKING, cast

import orj<PERSON>
import structlog
from pendulum import DateTime
from pypika import Table
from qdrant_client import QdrantClient

from anomaly_detector.config import AnomalyDetectorSettings, get_anomaly_detector_settings
from anomaly_detector.job.accounting import AccountingCollector
from anomaly_detector.job.correlation import (
    MetricMarkerCorrelation,
    ReportMarkersCorrelation,
    correlate,
    rising_edge_to_correlation_points,
)
from anomaly_detector.job.dependency import ServiceDependencies, get_hosted_s3_storage_options
from anomaly_detector.job.fields import FieldTask
from anomaly_detector.job.influxdb import InfluxDBClient
from anomaly_detector.job.llm_analysis import generate_advisory, translate_advisory
from anomaly_detector.job.llm_analysis.llm_analysis import (
    PanelAnalysisContext,
    PastAdvisories,
    generate_summary_advisory,
)
from anomaly_detector.job.llm_analysis.markers_analysis import analysis_markers
from anomaly_detector.job.logbert import <PERSON><PERSON><PERSON><PERSON><PERSON>, LogBERTTask
from anomaly_detector.job.logbert.logbert import LLMLogSample, LogBertInferenceResult
from anomaly_detector.job.lograte import LogRateReport, inference_log_rate
from anomaly_detector.job.metric.metric import MetricTask
from anomaly_detector.job.option import LADOptions
from anomaly_detector.job.postprocess.s3_cleaner import S3Cleaner
from anomaly_detector.job.preprocess import LogPreProcessor, LogVolumeProfile, LokiLogRetriever, PreprocessedReader
from anomaly_detector.job.preprocess.log_retriever import GreptimeLogRetriever
from anomaly_detector.job.share import TaskType
from anomaly_detector.job.share.log_sample import LogSamplePersist
from anomaly_detector.job.utils import build_metric_points
from anomaly_detector.s3_path_config import S3PathConfig
from anomaly_detector.utils import measurement_name
from config import Settings
from custom_exceptions import RecoverableError, ServiceError
from greptime import SyncGreptimeClient
from llm.baseline_context import BaselineContext
from llm.contextual_ranking import ContextualRanking, ProblematicLogGroups
from llm.llamington import LLamingtonClient
from llm.log_rate_analysis import LogRateAnalysis
from llm.metric_analysis import MetricAnalysis
from llm.panel_analysis import PanelAnalysis
from llm.past_suppression import PastSuppression
from llm.providers import OpenAIProvider
from llm.qoe_analysis import QoEAnalysis
from llm.root_cause_analysis import RootCauseAnalysis
from llm.translator import Translator
from prom import PrometheusClient
from prom_writer import PrometheusRemoteWriter
from s3 import register_aws_s3, register_hosted_s3
from sks.llm_context.context_loader import get_qoe_calculation_context
from vector_db.vector_search import EMBEDDING_MODELS, VectorSearch, create_embedding_provider

if TYPE_CHECKING:
    from anomaly_detector.job.fields.field_task import DrainInferenceSamples


def save_and_cleanup(
    option: LADOptions,
    report: dict,
    full_inference: bool,
    *,
    path_config: S3PathConfig,
    anomaly_detector_settings: AnomalyDetectorSettings,
    accounting_collector: AccountingCollector,
    prometheus_writer: PrometheusRemoteWriter,
):
    logger = structlog.get_logger("save_and_cleanup")
    points_timestamp = int(option.end.timestamp())
    report_path = path_config.inference_report
    try:
        report_path.write_bytes(orjson.dumps(report))
        logger.warning("report generated successfully", report_path=report_path)
    except Exception as e:
        raise RecoverableError("failed to write report") from e

    # FIXME: if no logs but have metrics, should not skip
    if report.get("processed_logs", 0) == 0:
        logger.warning("no logs processed, skipping metric points")
        return

    metric_points = build_metric_points(
        option.anomaly_detector_id,
        points_timestamp,
        report,
        full_inference=full_inference,
    )
    prometheus_writer.write_metric_points(metric_points)
    logger.warning("metrics have been written to greptime", metrics=metric_points)
    if not anomaly_detector_settings.is_sks_env:
        accounting_collector.save()
    try:
        s3_cleaner = S3Cleaner(
            path_config,
            retention_days=anomaly_detector_settings.inference_retention_days,
            timeout=anomaly_detector_settings.postprocess_timeout,
        )
        s3_cleaner.cleanup()
    except Exception as e:
        logger.warning("failed to cleanup s3", error=e)


def inference(
    option: LADOptions,
    settings: Settings,
    service_dependencies: ServiceDependencies,
    staging_base_path: pathlib.Path,
    trained_date: DateTime,
    full_inference: bool,
):
    log_flow = option.log_flows[0]  # Support multiple log flows
    logger = structlog.get_logger(
        "inference",
        flow_id=log_flow.flow_id,
        anomaly_detector_id=option.anomaly_detector_id,
        full_inference=full_inference,
        start_time=option.start,
        end_time=option.end,
    )
    anomaly_detector_settings = get_anomaly_detector_settings()
    register_hosted_s3(settings, service_dependencies.s3)
    register_aws_s3(settings)
    path_config = S3PathConfig(
        option.anomaly_detector_id,
        trained_date=trained_date,
        inference_date=option.end,
        full_inference=full_inference,
        staging_base_path=staging_base_path,
        aws_s3_base_path=settings.sagemaker.aws_path,
        hosted_s3_base_path=settings.s3.base_path,
    )
    accounting_collector = AccountingCollector(
        tenant_id=option.tenant_id,
        anomaly_detector_id=option.anomaly_detector_id,
        run_id=option.run_id,
        job_start_time=option.job_start_time,
        path_config=path_config,
        metronome_url=service_dependencies.metronome or "",
    )
    prometheus_client = PrometheusClient(
        mimir_proxy_url=service_dependencies.mimir_proxy,
        mimir_frontend_url=service_dependencies.mimir_frontend,
        mimir_enabled=option.mimir_enabled,
        headers={"X-Scope-OrgID": option.tenant_id},
    )

    hosted_s3_storage_options = get_hosted_s3_storage_options(settings.s3, service_dependencies.s3)

    base_report: dict = {
        "tenant_id": option.tenant_id,
        "flow_id": log_flow.flow_id,
        "anomaly_detector_id": option.anomaly_detector_id,
        "inference_time": {
            "start": option.start.timestamp(),
            "end": option.end.timestamp(),
        },
    }
    report = base_report.copy()
    report["processed_logs"] = 0

    llm_advisory_report = base_report.copy()
    log_rate_report = LogRateReport()
    openai_base_url = (
        service_dependencies.ollama
        if anomaly_detector_settings.on_premises_gpu
        else (settings.llm.openai_base_url or None)
    )

    lograte_analysis = LogRateAnalysis(
        task_start=option.start,
        provider=OpenAIProvider(
            openai_api_key=settings.llm.openai_api_key,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.lograte_model),
            openai_url=openai_base_url,
        ),
    )

    if full_inference:
        report.update(
            {
                "reasoning": {"score": 0},
                "notable_fields": {"score": 0},
                "log_structure": {"score": 0},
                "log_rate": {"score": 0},
                "sequence_pattern": {"score": 0},
            },
        )
        if log_flow.log_rate_prophecy_id and log_flow.log_rate_metric_name:
            log_rate_report = inference_log_rate(
                option=option,
                path_config=path_config,
                prometheus_client=prometheus_client,
                metric_name=log_flow.log_rate_metric_name,
                prophecy_id=log_flow.log_rate_prophecy_id,
                lograte_analysis=lograte_analysis,
                use_vision=not (anomaly_detector_settings.on_premises_gpu or settings.use_thread_executor),
            )
    try:
        log_volume_profile = LogVolumeProfile.load_from_dict(
            orjson.loads(path_config.trained_log_profile.read_bytes()),
            prometheus_client=prometheus_client,
            metronome_url=service_dependencies.metronome or "",
        )
        if log_volume_profile.should_retrieve_all and not option.skip_log_volume_profile:
            log_volume_profile.adjust_daily_profile(option.start, option.end)
    except ServiceError:
        raise
    except Exception as e:
        logger.exception("failed to load log volume profile", error=e)
        raise RecoverableError("failed to load log volume profile") from e

    drain_ignored_keys = option.drain_ignored_keys if option.drain_ignored_keys else []
    # Create greptime_client for both cases
    greptime_client = SyncGreptimeClient(
        url=service_dependencies.greptime,
        db=option.greptime_log_db or "pythia",
    )

    greptime_logs_enabled = option.greptime_log_db is not None and option.greptime_table is not None

    if greptime_logs_enabled:
        filters: list = []
        table = Table(cast(str, option.greptime_table))
        if option.sks_options and option.sks_options.site_id:
            filters.append(table.site_id == option.sks_options.site_id)
        else:
            filters.extend(
                [
                    table.flow_id == log_flow.flow_id,
                    table.type != "metric",
                ],
            )

        log_retriever = GreptimeLogRetriever(
            greptime_client,
            table=cast(str, option.greptime_table),
            timestamp_field="ts" if not settings.is_sks_env else "timestamp",
            log_field="line" if not settings.is_sks_env else "message",
            log_volume_profile=log_volume_profile,
            retriever_setting=anomaly_detector_settings.retriever,
            filters=filters,
        )
    else:
        assert service_dependencies.loki_frontend, "Loki frontend URL must be provided for log retrieval"
        log_retriever = LokiLogRetriever(
            tenant_id=option.tenant_id,
            flow_id=log_flow.flow_id,
            log_volume_profile=log_volume_profile,
            loki_url=service_dependencies.loki_frontend,
            retriever_setting=anomaly_detector_settings.retriever,
        )

    drain_parser = DrainParser(
        config_path=anomaly_detector_settings.drain_config_path,
        task_type=TaskType.INFERENCE,
        persist_path=path_config.trained_drain_state,
        sample_path=path_config.inference_log_samples if full_inference else None,
        skip_drain_templates_threshold=anomaly_detector_settings.skip_drain_templates_threshold,
    )

    target_parquet_file = (
        path_config.inference_daily_parquet_file(option.start, option.end)
        if full_inference
        else path_config.inference_10m_parquet_file(option.start, option.end)
    )

    preprocessor = LogPreProcessor(
        log_retriever=log_retriever,
        path_config=path_config,
        target_hosted_s3_path=target_parquet_file,
        drain_parser=drain_parser,
        task_start=option.start,
        task_date=option.end,
        task_type=TaskType.INFERENCE,
        accounting_collector=accounting_collector,
        log_timeranges_sample=log_rate_report.log_timeranges_sample,
        anomaly_detector_setting=anomaly_detector_settings,
        s3_settings=settings.s3,
        s3_url=service_dependencies.s3,
        drain_ignored_keys=drain_ignored_keys,
        excluded_fields=option.excluded_fields,  # type: ignore FIXME
    )

    preprocessor.load_model_schema()
    has_logs = False

    if (
        target_parquet_file.exists()
        and path_config.inference_field_hist.exists()
        and path_config.inference_schema.exists()
        and path_config.inference_histograms.exists()
    ):
        logger.info("skip preprocess")
        has_logs = True
    else:
        try:
            preprocessor.retrieve_logs_to_arrow_files()
            if preprocessor.field_occurrence_hist.total_rows:
                preprocessor.current_schema.save_schema(path_config.inference_schema)
                preprocessor.current_schema.save_histograms(path_config.inference_histograms)
                path_config.inference_field_hist.write_bytes(
                    orjson.dumps(preprocessor.field_occurrence_hist.to_object()),
                )
                preprocessor.convert_to_parquet()
                has_logs = True
            else:
                has_logs = False
        except Exception as e:
            raise ServiceError("failed to do preprocess") from e

        if not has_logs:
            if not full_inference:
                return
            if not option.markers:
                logger.warning("no logs or markers for inference")
                report["processed_logs"] = 0
                path_config.inference_report.write_bytes(orjson.dumps(report))
                return

    preprocessed_reader = PreprocessedReader(option, path_config)

    qdrant_client = QdrantClient(url=service_dependencies.qdrant)
    vector_search = VectorSearch(
        greptime_client=greptime_client,
        greptime_table=option.greptime_table if greptime_logs_enabled else None,
        qdrant_client=qdrant_client,
        embedding_provider=create_embedding_provider(
            settings.llm,
            model=EMBEDDING_MODELS[anomaly_detector_settings.embedding_model()],
        ),
        tenant_id=option.tenant_id,
        flow_id=log_flow.flow_id,
        greptime_logs_enabled=greptime_logs_enabled,
    )

    baseline_context = None

    contextual_ranking_task = ContextualRanking(
        vector_search=vector_search,
        provider=OpenAIProvider(
            openai_api_key=settings.llm.openrouter_api_key,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.contextual_ranking_model),
            openai_url=settings.llm.openrouter_base_url,
        ),
    )

    if full_inference and path_config.trained_baseline_context.exists():
        context_file = orjson.loads(path_config.trained_baseline_context.read_bytes())
        baseline_context = BaselineContext.load(vector_search, context_file)
        if not baseline_context.log_groups and not baseline_context.samples:
            baseline_context = None

    field_task = FieldTask(
        path_config=path_config,
        flow_id=log_flow.flow_id,
        contextual_ranking=contextual_ranking_task,
        anomaly_detector_id=option.anomaly_detector_id,
        preprocessed_reader=preprocessed_reader,
        vector_search=vector_search,
        accounting_collector=accounting_collector,
        task_type=TaskType.INFERENCE,
        date=option.end,
        full_inference=full_inference,
        time_ranges_sample=log_rate_report.log_timeranges_sample,
        anomaly_detector_settings=anomaly_detector_settings,
        parquet_storage_options=hosted_s3_storage_options,
        baseline_context=baseline_context,
    )

    log_structure_result: dict | None = None

    if has_logs:
        logger.warning("start fields inference")
        log_structure_result = field_task.inference_drain_histogram(skip_drain=drain_parser.should_skip)

        report.update(
            {
                "discrete_fields": field_task.inference_discrete_fields(
                    preprocessed_reader.discrete_fields_histograms(),
                ),
                "numeric_fields": field_task.inference_numeric_fields(skip_hdbscan=not full_inference),
                "notable_fields": field_task.inference_notable_fields(),
                "log_structure": log_structure_result,
                "processed_logs": field_task.total_rows,
            },
        )

        logger.warning("success fields inference")

    if not full_inference:
        save_and_cleanup(
            option,
            report,
            full_inference=False,
            path_config=path_config,
            anomaly_detector_settings=anomaly_detector_settings,
            accounting_collector=accounting_collector,
            prometheus_writer=PrometheusRemoteWriter(
                service_dependencies.greptime,
                option.tenant_id,
                params={"db": settings.greptime.db},
            ),
        )
        return

    drain_inference_samples: DrainInferenceSamples | None = None
    use_cached_keyword_problematic_log_groups = False
    keyword_problematic_log_groups: ProblematicLogGroups | None = None

    use_cached_drain_problematic_log_groups = False
    drain_problematic_log_groups: ProblematicLogGroups | None = None

    use_cached_bert_problematic_log_groups = False
    bert_problematic_log_groups = None
    bert_inference_result: LogBertInferenceResult = LogBertInferenceResult(score=0, sequences=[])
    past_suppression = PastSuppression(
        vector_search=vector_search,
        provider=OpenAIProvider(
            openai_api_key=settings.llm.openrouter_api_key,
            model=anomaly_detector_settings.llm_model(
                anomaly_detector_settings.llm.past_suppression_model,
            ),
            openai_url=settings.llm.openai_base_url
            if anomaly_detector_settings.on_premises_gpu
            else settings.llm.openrouter_base_url,
        ),
        date=path_config.inference_date_str,
    )

    if has_logs:
        if not drain_parser.should_skip:
            log_sample_persist = LogSamplePersist(path_config.inference_log_samples)
            log_sample_persist.load()
            if path_config.inference_keyword_problematic_log_groups.exists():
                logger.warning("keyword problematic log groups loaded from cache")
                keyword_problematic_log_groups = ProblematicLogGroups.load(
                    orjson.loads(path_config.inference_keyword_problematic_log_groups.read_bytes()),
                    vector_search=vector_search,
                )
                use_cached_keyword_problematic_log_groups = True
            else:
                logger.warning("keyword problematic log groups not found, generating new ones")
                keyword_problematic_log_groups = field_task.llm_drain_keywords_problematic_log_groups(
                    log_samples=log_sample_persist,
                )
                keyword_problematic_log_groups.shrink_group_samples(maximum=80)
                logger.warning("start llm filtering keyword problematic log groups")
                keyword_problematic_log_groups.llm_filter_out_existed_logs(past_suppression)
                path_config.inference_keyword_problematic_log_groups.write_bytes(
                    orjson.dumps(keyword_problematic_log_groups.dump()),
                )

            if path_config.inference_drain_problematic_log_groups.exists():
                logger.warning("drain problematic log groups loaded from cache")
                drain_problematic_log_groups = ProblematicLogGroups.load(
                    orjson.loads(path_config.inference_drain_problematic_log_groups.read_bytes()),
                    sample_to_prompt=lambda x: x["log"],
                    vector_search=vector_search,
                )
                use_cached_drain_problematic_log_groups = True

            if log_structure_result is not None:
                drain_inference_samples = field_task.inference_drain_samples(
                    log_structure_result,
                    problematic_log_groups=drain_problematic_log_groups,
                )
                log_structure_result["samples"] = drain_inference_samples.samples
                log_structure_result["template_samples"] = drain_inference_samples.template_samples

            if (
                not use_cached_drain_problematic_log_groups
                and drain_inference_samples is not None
                and drain_inference_samples.problematic_log_groups
            ):
                logger.warning("drain problematic log groups not found, generating new ones")
                drain_inference_samples.problematic_log_groups.shrink_group_samples(maximum=80)
                logger.warning("start llm filtering drain problematic log groups")
                drain_inference_samples.problematic_log_groups.llm_filter_out_existed_logs(past_suppression)
                path_config.inference_drain_problematic_log_groups.write_bytes(
                    orjson.dumps(drain_inference_samples.problematic_log_groups.dump()),
                )

        logger.warning("success fields inference")
        report["problematic_logs"] = {
            "samples": [s.get("sample") for s in keyword_problematic_log_groups.sorted_samples(with_index=False)[:50]]
            if keyword_problematic_log_groups
            else [],
        }

        log_sequence_report = base_report.copy()

        if not drain_parser.should_skip:
            bert_task = LogBERTTask(
                tenant_id=option.tenant_id,
                flow_id=log_flow.flow_id,
                contextual_ranking=contextual_ranking_task,
                task_type=TaskType.INFERENCE,
                anomaly_detector_id=option.anomaly_detector_id,
                date=option.end,
                parquet_files=preprocessed_reader.parquet_files(),
                trained_date=trained_date,
                accounting_collector=accounting_collector,
                path_config=path_config,
                logbert_settings=anomaly_detector_settings.logbert,
                sagemaker_settings=settings.sagemaker,
                parquet_storage_options=hosted_s3_storage_options,
                train_source_dir=anomaly_detector_settings.train_source_dir,
                inference_source_dir=anomaly_detector_settings.inference_source_dir,
                loki_url=service_dependencies.loki_frontend,
                baseline_context=baseline_context,
            )
            try:
                bert_task.load_tokenizer()
            except Exception as e:
                raise ServiceError("failed to load tokenizer") from e

            inference_results = None
            if anomaly_detector_settings.on_premises_gpu:
                inference_results = bert_task.inference_on_hosted_gpu()
            else:
                with contextlib.suppress(Exception):
                    inference_results = bert_task.retrieve_results_from_s3()
                if not inference_results:
                    try:
                        bert_task.generate_jsonl_file_on_s3()
                    except Exception as e:
                        raise ServiceError("failed to inference on sagemaker") from e
                    try:
                        bert_task.inference_on_sagemaker()
                        inference_results = bert_task.retrieve_results_from_s3()
                    except Exception as e:
                        raise ServiceError("failed to inference on sagemaker") from e
                    logger.warning("inference on sagemaker completed")
            if not inference_results:
                raise ServiceError("failed to logbert inference")

            if path_config.inference_log_sequences_problematic_log_groups.exists():
                logger.warning("bert problematic log groups loaded from cache")
                bert_problematic_log_groups = ProblematicLogGroups.load(
                    orjson.loads(path_config.inference_log_sequences_problematic_log_groups.read_bytes()),
                    sample_deserializer=lambda x: LLMLogSample(**x),
                    sample_to_prompt=lambda x: x.log,
                    sample_serializer=lambda x: {
                        "log": x.log,
                        "timestamp": x.timestamp,
                        "original": x.original,
                    },
                    vector_search=vector_search,
                )
                use_cached_bert_problematic_log_groups = True

            bert_inference_result = bert_task.derive_log_report(
                inference_results,
                greptime_client=greptime_client if greptime_logs_enabled else None,
                greptime_table=option.greptime_table if greptime_logs_enabled else None,
                problematic_log_groups=bert_problematic_log_groups,
            )

            if not use_cached_bert_problematic_log_groups and bert_inference_result.problematic_log_groups:
                try:
                    logger.warning("bert problematic log groups not found, generating new ones")
                    bert_inference_result.problematic_log_groups.shrink_group_samples(maximum=30)
                    logger.warning("start llm filtering bert problematic log groups")
                    bert_inference_result.problematic_log_groups.llm_filter_out_existed_logs(past_suppression)
                    path_config.inference_log_sequences_problematic_log_groups.write_bytes(
                        orjson.dumps(bert_inference_result.problematic_log_groups.dump()),
                    )
                except Exception as e:
                    logger.warning("failed to write log sequence problematic log groups", error=e)

            log_sequence_report["sequences"] = bert_inference_result.sequences
            log_sequence_report["samples"] = bert_inference_result.samples()
            log_sequence_report_path = path_config.inference_logs_report
            try:
                log_sequence_report_path.write_bytes(orjson.dumps(log_sequence_report))
                logger.warning(
                    "log sequence report generated successfully",
                    log_sequence_report_path=log_sequence_report_path,
                )
                report["sequence_pattern"] = {"score": bert_inference_result.score}
            except Exception as e:
                raise ServiceError("failed to write log sequence report") from e
        else:
            report["sequence_pattern"] = {"score": 0}

    try:
        report_markers_correlation = ReportMarkersCorrelation(
            anomaly_detector_id=option.anomaly_detector_id,
            report=report,
            greptime_client=SyncGreptimeClient(
                url=service_dependencies.greptime,
                db=option.greptime_db or "pythia",
            ),
            start=option.start,
            end=option.end,
        )
        if settings.is_sks_env and option.sks_options:
            metric_prometheus_client = PrometheusClient(
                mimir_proxy_url=f"{service_dependencies.greptime}/v1/prometheus/api/v1",
                mimir_enabled=False,
                uri="/query_range",
                headers={"x-greptime-db-name": option.sks_options.prometheus_db},
            )
            prophecy_prometheus_client = PrometheusClient(
                mimir_proxy_url=f"{service_dependencies.greptime}/v1/prometheus/api/v1",
                mimir_enabled=False,
                uri="/query_range",
                headers={"x-greptime-db-name": option.greptime_db},
            )
        else:
            metric_prometheus_client = PrometheusClient(
                mimir_proxy_url=service_dependencies.mimir_proxy,
                mimir_frontend_url=service_dependencies.mimir_frontend,
                mimir_enabled=option.mimir_enabled,
                headers={"X-Scope-OrgID": option.tenant_id},
            )
            prophecy_prometheus_client = prometheus_client
        metric_tasks = [
            MetricTask(
                marker=marker,
                start=option.start,
                end=option.end,
                greptime_client=SyncGreptimeClient(
                    url=service_dependencies.greptime,
                    db=(option.sks_options.greptime_db if option.sks_options else option.greptime_db) or "pythia",
                ),
                prophecy_prometheus_client=prophecy_prometheus_client,
                prometheus_client=metric_prometheus_client,
                prophecy_id=marker.name,
                tenant_id=option.tenant_id,
                extra_filters={"site_id": option.sks_options.site_id} if option.sks_options else None,
            )
            for marker in option.markers
        ]
        provider = OpenAIProvider(
            openai_api_key=settings.llm.openrouter_api_key,
            openai_url=settings.llm.openrouter_base_url,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.metric_model),
        )

        if path_config.inference_metrics_report.exists():
            content = orjson.loads(path_config.inference_metrics_report.read_bytes())
            metric_reports = content["metric_reports"]
            metric_advisories = content["metric_advisories"]
        else:
            extra_context = get_qoe_calculation_context() if settings.is_sks_env else ""
            metric_analyser = MetricAnalysis(provider, extra_context=extra_context)
            qoe_analyser = QoEAnalysis(provider, qoe_context=extra_context)
            metric_reports, metric_advisories = analysis_markers(metric_tasks, metric_analyser, qoe_analyser)
            if len(metric_reports):
                path_config.inference_metrics_report.write_bytes(
                    orjson.dumps({"metric_reports": metric_reports, "metric_advisories": metric_advisories}),
                )

        metric_marker_correlation = MetricMarkerCorrelation(metric_tasks)
        correlation_result = correlate(
            report_markers_correlation=report_markers_correlation,
            metric_markers_correlation=metric_marker_correlation,
        )
        influxdb_client = InfluxDBClient(
            db=option.greptime_db,
            url=f"{service_dependencies.greptime}/v1/influxdb",
        )
        report["metrics"] = metric_reports
        report["correlated_markers"] = correlation_result.response
        measurement = measurement_name(option.anomaly_detector_id)
        for edges in correlation_result.rising_edges:
            points = rising_edge_to_correlation_points(measurement, edges)
            influxdb_client.write(points)

    except Exception:
        logger.error("failed to get report markers correlation", exc_info=True)
        metric_advisories = {}
        report["correlated_markers"] = []

    try:
        logger.warning("start generating llm advisory")
        past_advisories = PastAdvisories()
        past_advisories.load_advisories(path_config.inference_past_advisory_report())

        llamington_client = LLamingtonClient(
            llamington_url=service_dependencies.llamington or "",
            tenant_id=option.tenant_id,
            flow_id="sks" if settings.is_sks_env else log_flow.flow_id,
        )

        context = PanelAnalysisContext(
            lad_option=option,
            past_advisories=past_advisories,
            baseline_context=baseline_context,
            current_date_str=path_config.inference_date_str,
            service_dependencies=service_dependencies,
            log_rate_advisory=log_rate_report.advisory,
            keyword_problematic_groups=keyword_problematic_log_groups,
            log_structure_problematic_groups=drain_inference_samples.problematic_log_groups
            if drain_inference_samples
            else None,
            log_sequence_problematic_groups=bert_inference_result.problematic_log_groups,
        )
        advisory_provider = OpenAIProvider(
            openai_api_key=settings.llm.openai_api_key,
            openai_url=settings.llm.openai_base_url,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.advisory_model),
        )
        translate_provider = OpenAIProvider(
            openai_api_key=settings.llm.openrouter_api_key,
            openai_url=settings.llm.openrouter_base_url,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.translator_model),
        )
        if anomaly_detector_settings.on_premises_gpu:
            advisory_model = anomaly_detector_settings.llm.on_premises_provider_model
        elif settings.is_sks_env:
            advisory_model = anomaly_detector_settings.llm.sks_default_model
        else:
            advisory_model = anomaly_detector_settings.llm.advisory_model

        panel_analysis = PanelAnalysis(
            advisory_model,
            llamington_client,
            provider=advisory_provider if anomaly_detector_settings.on_premises_gpu or settings.is_sks_env else None,
        )
        advisory_result = generate_advisory(
            panel_analysis,
            report,
            context=context,
        )
        logger.warning("llm advisory generated, start root cause analysis")
        advisory_result.update(metric_advisories)
        root_cause_analysis = RootCauseAnalysis(
            model=advisory_model,
            llamington_client=llamington_client,
            provider=advisory_provider if anomaly_detector_settings.on_premises_gpu or settings.is_sks_env else None,
        )
        reasoning_score, root_cause_advisory = generate_summary_advisory(
            root_cause_analysis,
            past_suppression,
            vector_search,
            report,
            advisory_result,
            context=context,
        )
        logger.warning("root cause analysis completed", reasoning_score=reasoning_score)
        advisory_result["summary"] = {"content": root_cause_advisory}
        report["reasoning"] = {"score": reasoning_score}

        llm_advisory_report.update(advisory_result)
        path_config.inference_advisory_report.write_bytes(orjson.dumps(llm_advisory_report))
        translator = Translator(provider=translate_provider)
        translates = []
        if settings.is_sks_env:
            translates.append({"lang": "zh-cn", "name": "Simplified Chinese"})
        else:
            translates.append({"lang": "jp", "name": "Japanese"})
        for translate in translates:
            translated_advisory_result = translate_advisory(
                translator,
                advisory_result=advisory_result,
                target_lang=translate["name"],
            )
            translated_llm_advisory_report = base_report.copy()
            translated_llm_advisory_report.update(translated_advisory_result)
            path_config.inference_advisory_report_lang(translate["lang"]).write_bytes(
                orjson.dumps(translated_llm_advisory_report),
            )

        logger.warning(
            "llm advisory generated successfully",
            advisory_report_path=path_config.inference_advisory_report,
        )
        if keyword_problematic_log_groups and not use_cached_keyword_problematic_log_groups:
            logger.warning("saving keyword problematic log groups to vector db")
            keyword_problematic_log_groups.save_to_vectordb(path_config.inference_date_str)
        if bert_inference_result.problematic_log_groups and not use_cached_bert_problematic_log_groups:
            logger.warning("saving bert problematic log groups to vector db")
            bert_inference_result.problematic_log_groups.save_to_vectordb(path_config.inference_date_str)
        if (
            drain_inference_samples
            and drain_inference_samples.problematic_log_groups
            and not use_cached_drain_problematic_log_groups
        ):
            logger.warning("saving drain problematic log groups to vector db")
            drain_inference_samples.problematic_log_groups.save_to_vectordb(path_config.inference_date_str)
    except ServiceError:
        raise
    except Exception as e:
        logger.exception("failed to generate llm advisory", error=e)

    if "reasoning" not in report:
        report["reasoning"] = {"score": 0}

    save_and_cleanup(
        option,
        report,
        full_inference=full_inference,
        path_config=path_config,
        anomaly_detector_settings=anomaly_detector_settings,
        accounting_collector=accounting_collector,
        prometheus_writer=PrometheusRemoteWriter(
            service_dependencies.greptime,
            option.tenant_id,
            params={"db": settings.greptime.db},
        ),
    )
