from dataclasses import dataclass

import httpx
from pendulum import DateTime


@dataclass
class InfluxPoint:
    measurement: str
    tags: dict
    fields: dict
    timestamp: DateTime


def _escape_comma(s: str) -> str:
    return s.replace(",", r"\,")


def _encode_line(point: InfluxPoint) -> str:
    ts = int(point.timestamp.timestamp() * 1e9)
    tags = ",".join(f"{k}={_escape_comma(v)}" for k, v in point.tags.items())
    fields = ",".join(f"{k}={v}" for k, v in point.fields.items())

    return f"{point.measurement},{tags} {fields} {ts}"


class InfluxDBClient:
    def __init__(self, url: str, db: str):
        self.url = url
        self.db = db
        self.client = httpx.Client(
            base_url=url,
        )

    def write(self, points: list[InfluxPoint]):
        if not points:
            return
        content = "\n".join(_encode_line(point) for point in points)
        self.client.post(
            "/api/v2/write",
            content=content,
            params={"db": self.db, "precision": "ns"},
        )
