import pathlib
import shutil
import uuid
from collections.abc import Callable
from dataclasses import fields
from datetime import timed<PERSON>ta
from typing import cast

import pendulum
import structlog
from asyncpg import Pool
from pendulum import duration

from anomaly_detector.job.delete_anomaly_detector import delete_anomaly_detector
from anomaly_detector.job.dependency import ServiceDependencies
from anomaly_detector.job.option import LADOptions, SKSOptions
from anomaly_detector.sks.qoe import sks_markers
from config import Settings
from job.model import (
    AnomalyDetectorConfig,
    AnomalyDetectorLogFlow,
    BaseAnomalyDetectorConfig,
    GreptimeAnomalyDetectorConfig,
    JobModel,
    JobType,
)
from job.scheduler import BaseJob
from services import ServicesRegistry
from utils import get_module


def _create_log_flows_from_config(job_config: BaseAnomalyDetectorConfig) -> list[AnomalyDetectorLogFlow]:
    """Create log flows list from job config, using flow_id as fallback if log_flows is empty."""
    log_flows = job_config.log_flows
    if not log_flows and job_config.flow_id:
        log_flows = [
            AnomalyDetectorLogFlow(
                flow_id=job_config.flow_id,
                log_rate_metric_name=job_config.log_rate_metric,
                log_rate_prophecy_id=job_config.prophecy_id,
            ),
        ]
    return log_flows


class BaseAnomalyDetectorJob(BaseJob):
    def __init__(self, job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
        self.job_record = job_record
        self.pool = pool
        self.settings = settings
        self.services_registry = services_registry
        self.logger = structlog.get_logger("BaseAnomalyDetectorJob", id=self.job_record.id)

    def _staging_base_path(self):
        return pathlib.Path("/tmp/staging/") / str(self.job_record.job_source_id)

    def _resolve_services(self):
        service_urls = {}
        for svc in fields(ServiceDependencies):
            try:
                service_urls[svc.name] = self.services_registry.service(svc.name).url()
            except Exception:
                if svc.default is None:
                    pass
                else:
                    self.logger.exception("Failed to resolve service", service=svc.name)
                    raise
        return ServiceDependencies(**service_urls)

    def invalidate_cache(self):
        for svc in fields(ServiceDependencies):
            if svc.default is None:
                self.services_registry.service(svc.name).invalidate_cache()

    async def on_done(self):
        shutil.rmtree(self._staging_base_path(), ignore_errors=True)


class AnomalyDetectorTrainingJob(BaseAnomalyDetectorJob):
    def __init__(self, job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
        super().__init__(job_record, settings, services_registry, pool)
        self.logger = structlog.get_logger("AnomalyDetectorTrainingJob", id=self.job_record.id)
        self.train_func = cast(Callable, get_module("anomaly_detector.job.train", "train"))
        if isinstance(self.job_record.config, BaseAnomalyDetectorConfig):
            self.job_config = self.job_record.config
        else:
            raise TypeError(f"Invalid config type: {self.job_record.config}")

    def _is_preprocess(self):
        return (not self.job_config.express_training) and (
            self.job_record.next_run != self.job_config.scheduled_train_date
        )

    def _is_initial_train(self):
        return not self.job_config.trained_date

    def _staging_base_path(self):
        return super()._staging_base_path() / "train"

    def maximum_duration(self):
        return duration(hours=4)

    async def prepare_args(self) -> dict:
        if self.job_record.next_run is None:
            raise ValueError("next_run is None")
        settings = self.settings
        log_flows = _create_log_flows_from_config(self.job_config)
        lad_option = LADOptions(
            start=self.job_record.next_run.add(
                days=-1 if (self._is_initial_train() and not self.job_config.express_training) else -7,
            ),
            end=self.job_record.next_run,
            anomaly_detector_id=str(self.job_record.job_source_id),
            tenant_id=str(self.job_record.tenant_id),
            log_flows=log_flows,
            metric_flows=self.job_config.metric_flows,
            run_id=str(uuid.uuid4()),
            skip_public_holidays=self.job_config.skip_public_holidays,
            tenant_subdivision="",
            tenant_country="JP",
            job_start_time=pendulum.now().int_timestamp,
            skip_log_volume_profile=self.job_config.skip_log_volume_profile,
            express_training=self.job_config.express_training,
            excluded_fields=self.job_config.excluded_fields,
            greptime_db=self.settings.greptime.db,
            mimir_enabled=settings.mimir_enabled,
        )

        if settings.greptime_logs_enabled:
            lad_option.greptime_log_db = self.settings.greptime.log_db
            lad_option.greptime_table = f"t_{self.job_record.tenant_id.replace('-', '_')}"

        if settings.is_sks_env:
            lad_option.drain_ignored_keys = ["message"]
            lad_option.markers = sks_markers
            lad_option.sks_options = SKSOptions(
                greptime_db=settings.greptime.sks_db,
                prometheus_db=settings.greptime.sks_db,
                site_id=self.job_record.tenant_id if self.job_record.tenant_id != "sks" else None,
            )
            if isinstance(self.job_config, GreptimeAnomalyDetectorConfig):
                lad_option.greptime_log_db = self.job_config.db or self.settings.greptime.log_db
                lad_option.greptime_table = log_flows[0].flow_id  # TODO: Support multiple log flows

        return {
            "option": lad_option,
            "settings": settings,
            "service_dependencies": self._resolve_services(),
            "staging_base_path": self._staging_base_path(),
            "metric_name": self.job_config.log_rate_metric,
            "last_trained_date": self.job_config.trained_date,
            "is_preprocess": self._is_preprocess(),
        }

    def run(self):
        return self.train_func

    async def on_success(self):
        self.logger.info("Training completed")
        async with (
            self.pool.acquire() as conn,
            conn.transaction(),
        ):
            if self._is_preprocess():
                await conn.execute(
                    "UPDATE jobs SET status = 'pending', attempts = 0, run_started = NULL, errors = '[]', next_run = next_run + interval '1 day' WHERE id = $1",
                    self.job_record.id,
                )
                return
            new_config = self.job_config.model_copy(
                update={
                    "scheduled_train_date": None,
                    "trained": True,
                    "trained_date": self.job_record.next_run,
                }
                | {"initial_trained_date": self.job_record.next_run}
                if self._is_initial_train()
                else {},
            )
            if self._is_initial_train():
                inference_full_config = new_config.model_copy(
                    update={"inference_interval_minutes": 1440, "full_inference": True},
                )
                inference_10min_config = new_config.model_copy(
                    update={"inference_interval_minutes": 10, "full_inference": False},
                )
                if self.job_record.next_run is None:
                    raise ValueError("next_run is None")
                base_values = (
                    self.job_record.tenant_id,
                    self.job_record.job_source_id,
                    (self.job_record.next_run + duration(days=1)).naive(),
                    JobType.log_anomaly,
                )
                await conn.executemany(
                    """
                    INSERT INTO jobs (tenant_id, job_source_id, next_run, type, cost, config, created)
                    VALUES ($1, $2, $3, $4, $5, $6, now())""",
                    [
                        (*base_values, 30, inference_full_config.kebab_dump()),
                        (*base_values, 30, inference_10min_config.kebab_dump()),
                    ],
                )
            else:
                if self.job_record.next_run is None:
                    raise ValueError("next_run is None")
                await conn.execute(
                    """
                    UPDATE jobs
                    SET config = config || $2
                    WHERE job_source_id = $1
                    """,
                    self.job_record.job_source_id,
                    {"retrained-date": self.job_record.next_run.naive()},
                )
            await conn.execute(
                """
                UPDATE jobs
                SET status = 'completed', attempts = 0, errors = '[]', run_started = NULL, config = config || $2, next_run = $3
                WHERE id = $1
                """,
                self.job_record.id,
                new_config.kebab_dump(),
                None,
            )


class AnomalyDetectorInferenceJob(BaseAnomalyDetectorJob):
    def __init__(self, job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
        super().__init__(job_record, settings, services_registry, pool)
        self.logger = structlog.get_logger("AnomalyDetectorInferenceJob", id=self.job_record.id)
        self.predict_func = cast(Callable, get_module("anomaly_detector.job.inference", "inference"))
        if isinstance(self.job_record.config, BaseAnomalyDetectorConfig):
            self.job_config = self.job_record.config
        else:
            raise TypeError(f"Invalid config type: {self.job_record.config}")

    def _use_retrained_date(self):
        if self.job_config.retrained_date is None:
            return False
        if self.job_record.next_run is None or self.job_config.inference_interval_minutes is None:
            raise ValueError("next_run is None")
        return self.job_record.next_run.add(
            minutes=self.job_config.inference_interval_minutes,
        ) - self.job_config.retrained_date >= duration(days=1)

    def _staging_base_path(self):
        return super()._staging_base_path() / "inference"

    def maximum_duration(self):
        if self.job_config.full_inference:
            return duration(hours=3)
        return duration(hours=1)

    async def prepare_args(self):
        if self.job_record.next_run is None or self.job_config.inference_interval_minutes is None:
            raise ValueError("next_run is None")
        settings = self.settings
        log_flows = _create_log_flows_from_config(self.job_config)
        lad_option = LADOptions(
            start=self.job_record.next_run.add(minutes=self.job_config.inference_interval_minutes * -1),
            end=self.job_record.next_run,
            anomaly_detector_id=str(self.job_record.job_source_id),
            tenant_id=str(self.job_record.tenant_id),
            log_flows=log_flows,
            metric_flows=self.job_config.metric_flows,
            run_id=str(uuid.uuid4()),
            skip_public_holidays=self.job_config.skip_public_holidays,
            tenant_subdivision="",
            tenant_country="JP",
            job_start_time=pendulum.now().int_timestamp,
            skip_log_volume_profile=self.job_config.skip_log_volume_profile,
            excluded_fields=self.job_config.excluded_fields,
            greptime_db=self.settings.greptime.db,
            mimir_enabled=settings.mimir_enabled,
        )
        if settings.greptime_logs_enabled:
            lad_option.greptime_log_db = self.settings.greptime.log_db
            lad_option.greptime_table = f"t_{self.job_record.tenant_id.replace('-', '_')}"

        if settings.is_sks_env:
            lad_option.drain_ignored_keys = ["message"]
            lad_option.markers = sks_markers
            lad_option.sks_options = SKSOptions(
                greptime_db=settings.greptime.sks_db,
                prometheus_db=settings.greptime.sks_db,
                site_id=self.job_record.tenant_id if self.job_record.tenant_id != "sks" else None,
            )
            if isinstance(self.job_config, GreptimeAnomalyDetectorConfig):
                lad_option.greptime_log_db = self.job_config.db or self.settings.greptime.log_db
                lad_option.greptime_table = log_flows[0].flow_id  # TODO: Support multiple log flows

        return {
            "option": lad_option,
            "settings": settings,
            "service_dependencies": self._resolve_services(),
            "staging_base_path": self._staging_base_path(),
            "trained_date": self.job_config.retrained_date
            if self._use_retrained_date()
            else self.job_config.trained_date,
            "full_inference": bool(self.job_config.full_inference),
        }

    def run(self):
        return self.predict_func

    async def on_success(self):
        if self.job_record.next_run is None or self.job_config.inference_interval_minutes is None:
            raise ValueError("next_run is None")
        async with (
            self.pool.acquire() as conn,
            conn.transaction(),
        ):
            next_run_duration = timedelta(minutes=self.job_config.inference_interval_minutes)
            if self._use_retrained_date():
                await conn.execute(
                    """
                    UPDATE jobs
                    SET status = 'pending', attempts = 0, errors = '[]', run_started = NULL, config = config || $3, next_run = next_run + $2
                    WHERE id = $1
                    """,
                    self.job_record.id,
                    next_run_duration,
                    {"trained-date": self.job_record.next_run.to_iso8601_string(), "retrained-date": None},
                )
            else:
                await conn.execute(
                    """
                    UPDATE jobs
                    SET status = 'pending', attempts = 0, errors = '[]', run_started = NULL, next_run = next_run + $2
                    WHERE id = $1""",
                    self.job_record.id,
                    next_run_duration,
                )


class AnomalyDetectorDeleteJob(BaseAnomalyDetectorJob):
    def __init__(self, job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
        super().__init__(job_record, settings, services_registry, pool)
        self.logger = structlog.get_logger("AnomalyDetectorDeleteJob", id=self.job_record.id)
        if isinstance(self.job_record.config, BaseAnomalyDetectorConfig):
            self.job_config = self.job_record.config
        else:
            raise TypeError(f"Invalid config type: {self.job_record.config}")

    def maximum_duration(self):
        return duration(hours=1)

    async def prepare_args(self):
        if self.job_record.next_run is None:
            raise ValueError("next_run is None")
        settings = self.settings
        log_flows = _create_log_flows_from_config(self.job_config)
        option = LADOptions(
            anomaly_detector_id=str(self.job_record.job_source_id),
            tenant_id=str(self.job_record.tenant_id),
            log_flows=log_flows,
            run_id=str(uuid.uuid4()),
            end=self.job_record.next_run,
            start=self.job_record.next_run,
            mimir_enabled=settings.mimir_enabled,
        )
        service_dependencies = self._resolve_services()
        return {
            "option": option,
            "settings": settings,
            "service_dependencies": service_dependencies,
        }

    def run(self):
        return delete_anomaly_detector

    async def on_success(self):
        self.logger.info("Deletion completed")
        async with (
            self.pool.acquire() as conn,
            conn.transaction(),
        ):
            await conn.execute(
                "DELETE FROM jobs WHERE id = $1",
                self.job_record.id,
            )


def anomaly_detector_job_factory(
    job_record: JobModel,
    settings: Settings,
    services_registry: ServicesRegistry,
    pool: Pool,
) -> BaseAnomalyDetectorJob:
    if not isinstance(job_record.config, (GreptimeAnomalyDetectorConfig, AnomalyDetectorConfig)):
        raise TypeError(f"Invalid config type: {job_record.config}")
    if job_record.config.deleted:
        return AnomalyDetectorDeleteJob(job_record, settings, services_registry, pool)
    if job_record.config.trained:
        return AnomalyDetectorInferenceJob(job_record, settings, services_registry, pool)
    return AnomalyDetectorTrainingJob(job_record, settings, services_registry, pool)
