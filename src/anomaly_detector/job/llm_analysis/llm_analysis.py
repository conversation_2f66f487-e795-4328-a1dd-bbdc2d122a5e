from collections.abc import Callable
from dataclasses import dataclass

import orjson
import pendulum
import s3path
import structlog
from tenacity import retry, stop_after_attempt, wait_random

from anomaly_detector.job.dependency import ServiceDependencies
from anomaly_detector.job.option import LADOptions
from anomaly_detector.job.share.score_calculation import regular_overall_score
from anomaly_detector.job.utils import flattened_key_to_dot
from custom_exceptions import ServiceError
from llm.baseline_context import BaselineContext
from llm.contextual_ranking import ProblematicLogGroups
from llm.panel_analysis import PanelAnalysis
from llm.past_suppression import PastSuppression
from llm.root_cause_analysis import RootCauseAnalysis
from llm.utils import cleanup_past_advisories
from loki.loki import query_logs_from_loki
from vector_db.vector_search import SymptomPayload, VectorSearch

logger = structlog.get_logger("llm_analysis")


class PastAdvisories:
    def __init__(self):
        self._advisories = []

    def load_advisories(self, file_paths: list[s3path.S3Path]):
        for file_path in file_paths:
            if file_path.exists():
                self._advisories.append(orjson.loads(file_path.read_bytes()))

    def _advisory_prompt(self, advisory: dict, panel: str, field: str | None = None):
        start_ts = advisory["inference_time"].get("start")
        end_ts = advisory["inference_time"].get("end")
        start = pendulum.from_timestamp(start_ts)
        end = pendulum.from_timestamp(end_ts)
        content_res = advisory.get(panel, {})
        if field:
            content_res = content_res.get(field, {})
        return start, end, content_res.get("content", "")

    def to_prompt(self, panel_name: str, field_name: str | None = None) -> str | None:
        prompt = ""
        for advisory in self._advisories:
            start, end, content = self._advisory_prompt(advisory, panel_name, field_name)
            content = cleanup_past_advisories(content)
            if content:
                prompt += f'Advisory from {start.to_iso8601_string()} to {end.to_iso8601_string()}: "{content}"\n\n'
        return prompt.strip() if prompt else None


@dataclass
class PanelAnalysisContext:
    lad_option: LADOptions
    past_advisories: PastAdvisories
    service_dependencies: ServiceDependencies
    log_rate_advisory: str = ""
    current_date_str: str = ""
    baseline_context: BaselineContext | None = None
    keyword_problematic_groups: ProblematicLogGroups | None = None
    log_sequence_problematic_groups: ProblematicLogGroups | None = None
    log_structure_problematic_groups: ProblematicLogGroups | None = None


def validate_score(res: dict) -> bool:
    return res.get("score", 0) > 0.2


def validate_numeric_fields(field_res: dict) -> bool:
    return field_res.get("inference", {}).get("trained_noise", 0) > 0.2


def histogram_to_string(hist: dict) -> str:
    fields = []
    for item in hist.get("values", {}):
        fields.append(f"{flattened_key_to_dot(item['value'])}: {item['percentage'] * 100:.2f}%")
    fields.append(f"others: {hist.get('others', {}).get('percentage', 0) * 100:.2f}%")
    return ", ".join(fields)


def analysis_notable_fields(analysis: PanelAnalysis, res: dict, context: PanelAnalysisContext) -> dict:
    panel_name = "notable_fields"
    top_k_with_others = res.get("top_k_with_others", {})
    expected = histogram_to_string(top_k_with_others.get("train", {}))
    actual = histogram_to_string(top_k_with_others.get("inference", {}))
    advisory_content = analysis.invoke(
        panel=panel_name,
        args={
            "expected": expected,
            "actual": actual,
            "current_date": context.current_date_str,
            "past_7_day_advisories": context.past_advisories.to_prompt(panel_name),
            "training_groups": context.baseline_context.to_prompt() if context.baseline_context else "",
        },
    )
    return {"content": advisory_content}


def analysis_log_structure(analysis: PanelAnalysis, _: dict, context: PanelAnalysisContext) -> dict:
    panel_name = "log_structure"
    tpl_name = panel_name
    structure_groups = context.log_structure_problematic_groups
    baseline = context.baseline_context

    samples_prompt = structure_groups.to_prompt(per_group=4, use_original_groups=True) if structure_groups else ""
    if not samples_prompt:
        return {"content": "No new problematic logs found."}

    baseline_prompt = baseline.to_prompt() if baseline else ""
    if not baseline_prompt:
        tpl_name = "legacy_" + panel_name

    advisory_content = analysis.invoke(
        panel=tpl_name,
        args={
            "log_lines": samples_prompt,
            "training_groups": baseline_prompt if baseline else "",
            "current_date": context.current_date_str,
            "past_7_day_advisories": context.past_advisories.to_prompt(panel_name),
        },
    )
    return {"content": advisory_content}


def analysis_sequence_pattern(analysis: PanelAnalysis, _: dict, context: PanelAnalysisContext) -> dict:
    panel_name = "sequence_pattern"
    tpl_name = panel_name
    log_sequence_groups = context.log_sequence_problematic_groups
    samples_prompt = log_sequence_groups.to_prompt(per_group=4, use_original_groups=True) if log_sequence_groups else ""
    if not samples_prompt:
        return {"content": "No new problematic log sequences found."}
    baseline_prompt = context.baseline_context.to_prompt() if context.baseline_context else ""
    if not baseline_prompt:
        tpl_name = "legacy_" + panel_name
    advisory_content = analysis.invoke(
        panel=tpl_name,
        args={
            "log_lines": samples_prompt,
            "training_groups": baseline_prompt,
            "current_date": context.current_date_str,
            "past_7_day_advisories": context.past_advisories.to_prompt(panel_name),
        },
    )
    return {"content": advisory_content}


def analysis_discrete_field(
    analysis: PanelAnalysis,
    res: dict,
    field_name: str,
    context: PanelAnalysisContext,
) -> dict:
    panel_name = "discrete_fields"
    top_k_with_others = res.get("top_k_with_others", {})
    actual = histogram_to_string(top_k_with_others.get("inference", {}))
    past_advisories = context.past_advisories.to_prompt(panel_name, field_name)
    baseline_prompt = context.baseline_context.to_prompt() if context.baseline_context else ""
    if not top_k_with_others.get("train"):
        advisory_content = analysis.invoke(
            panel="new_discrete_fields",
            args={
                "actual": actual,
                "field_name": flattened_key_to_dot(field_name),
                "training_groups": baseline_prompt,
                "current_date": context.current_date_str,
                "past_7_day_advisories": past_advisories,
            },
        )
        return {"content": advisory_content}
    expected = histogram_to_string(top_k_with_others.get("train", {}))
    advisory_content = analysis.invoke(
        panel=panel_name,
        args={
            "expected": expected,
            "actual": actual,
            "field_name": flattened_key_to_dot(field_name),
            "training_groups": baseline_prompt,
            "current_date": context.current_date_str,
            "past_7_day_advisories": past_advisories,
        },
    )
    return {"content": advisory_content}


def _build_numeric_loki_query(flow_id: str, field_name: str, sorted_clusters: list[dict]):
    escaped_field_name = field_name.replace('"', r"\"")
    conditions = [f"(l0 < {sorted_clusters[0]['min']})"]
    for i in range(len(sorted_clusters) - 1):
        conditions.append(f"(l0 > {sorted_clusters[i]['max']} and l0 < {sorted_clusters[i + 1]['min']})")
    conditions.append(f"(l0 > {sorted_clusters[-1]['max']})")
    return f'{{_reserved_flow_id="{flow_id}"}} | json l0="{escaped_field_name}" | {" or ".join(conditions)}'


def _expected_numeric_range(sorted_clusters: list[dict]):
    ranges = []
    for c in sorted_clusters:
        ranges.append(f"from {c['min']} to {c['max']}")
    return ", ".join(ranges)


def analysis_numeric_fields(
    analysis: PanelAnalysis,
    res: dict,
    field_name: str,
    context: PanelAnalysisContext,
) -> dict | None:
    panel_name = "numeric_fields"
    trained = res.get("trained")
    if not trained:
        return None
    clusters = trained.get("clusters", [])
    sorted_clusters = sorted(filter(lambda x: x["label"] != -1, clusters), key=lambda x: x["min"])
    if not sorted_clusters:
        return None
    log_flow = context.lad_option.log_flows[0]  # Support multiple log flows
    q = _build_numeric_loki_query(log_flow.flow_id, field_name, sorted_clusters)
    expected = _expected_numeric_range(sorted_clusters)
    if not q:
        return None
    if not context.lad_option.greptime_table and context.service_dependencies.loki_frontend:
        res = query_logs_from_loki(
            loki_url=context.service_dependencies.loki_frontend,
            tenant_id=context.lad_option.tenant_id,
            start=context.lad_option.start,
            end=context.lad_option.end,
            query=q,
            limit=5,
        )
        log_lines = "\n".join([r["values"][0][1] for r in res["result"]])
    else:
        log_lines = ""
    advisory_content = analysis.invoke(
        panel=panel_name,
        args={
            "log_lines": log_lines,
            "field_name": flattened_key_to_dot(field_name),
            "current_date": context.current_date_str,
            "expected": expected,
            "training_groups": context.baseline_context.to_prompt() if context.baseline_context else "",
            "past_7_day_advisories": context.past_advisories.to_prompt(panel_name, field_name),
        },
    )
    return {"content": advisory_content}


@dataclass
class PanelAnalysisFunction:
    validate_fn: Callable[[dict], bool]
    analysis_fn: (
        Callable[[PanelAnalysis, dict, PanelAnalysisContext], dict | None]
        | Callable[[PanelAnalysis, dict, str, PanelAnalysisContext], dict | None]
        | Callable[[PanelAnalysis], PanelAnalysis]
    )


FIELDS_ANALYSIS = {
    "log_structure": PanelAnalysisFunction(
        validate_fn=validate_score,
        analysis_fn=analysis_log_structure,
    ),
    "sequence_pattern": PanelAnalysisFunction(
        validate_fn=validate_score,
        analysis_fn=analysis_sequence_pattern,
    ),
    "numeric_fields": PanelAnalysisFunction(
        validate_fn=validate_numeric_fields,
        analysis_fn=analysis_numeric_fields,
    ),
    "discrete_fields": PanelAnalysisFunction(
        validate_fn=validate_score,
        analysis_fn=analysis_discrete_field,
    ),
    "notable_fields": PanelAnalysisFunction(
        validate_fn=validate_score,
        analysis_fn=analysis_notable_fields,
    ),
    "log_rate": PanelAnalysisFunction(
        validate_fn=validate_score,
        analysis_fn=lambda x: x,
    ),
}


@retry(wait=wait_random(min=180, max=600), stop=stop_after_attempt(3))
def generate_summary_advisory(
    llm_analysis: RootCauseAnalysis,
    past_suppression: PastSuppression,
    vector_search: VectorSearch,
    report: dict,
    advisory_result: dict,
    context: PanelAnalysisContext,
):
    overall_score = regular_overall_score(report)
    template_name = "root_cause_summary" if overall_score > 0.2 else "advise_summary"
    symptoms = []
    pending_save_symptoms = []
    new_symptoms = 0
    for panel, advisory in advisory_result.items():
        if panel in ["numeric_fields", "discrete_fields", "metrics", "qoes"]:
            for field, result in advisory.items():
                if not result:
                    continue
                symptom_content = result if isinstance(result, str) else result.get("content")
                cleaned_symptom = cleanup_past_advisories(symptom_content)
                symptom_res, existed = past_suppression.existed_symptom(
                    cleaned_symptom,
                    current_date=context.current_date_str,
                )
                if not existed:
                    pending_save_symptoms.append(
                        SymptomPayload(
                            content=cleaned_symptom,
                            panel=panel,
                            field=field,
                            date=context.current_date_str,
                        ),
                    )
                    new_symptoms += 1
                symptoms.append(symptom_content)
        else:
            symptom_content = advisory if isinstance(advisory, str) else advisory.get("content")
            cleaned_symptom = cleanup_past_advisories(symptom_content)
            symptom_res, existed = past_suppression.existed_symptom(
                cleaned_symptom,
                current_date=context.current_date_str,
            )
            if not existed:
                pending_save_symptoms.append(
                    SymptomPayload(
                        content=cleaned_symptom,
                        panel=panel,
                        date=context.current_date_str,
                    ),
                )
                new_symptoms += 1
            symptoms.append(symptom_content)
    if not new_symptoms and (
        not context.keyword_problematic_groups or not context.keyword_problematic_groups.sample_size()
    ):
        return 0, "Everything looks good, no anomalies detected."
    baseline_prompt = context.baseline_context.to_prompt() if context.baseline_context else ""
    original_log_lines = []
    log_lines_prompt = ""
    if context.keyword_problematic_groups:
        keyword_prompt, keywords_samples = context.keyword_problematic_groups.to_prompt(
            per_group=3,
            return_logs=True,
            sample_index_start_from=len(original_log_lines) + 1,
        )
        original_log_lines += keywords_samples
        log_lines_prompt += keyword_prompt
    else:
        keyword_prompt = ""
    if context.log_structure_problematic_groups:
        log_structure_prompt, log_structure_samples = context.log_structure_problematic_groups.to_prompt(
            per_group=3,
            return_logs=True,
            sample_index_start_from=len(original_log_lines) + 1,
        )
        original_log_lines += log_structure_samples
        log_lines_prompt += log_structure_prompt
    if context.log_sequence_problematic_groups:
        log_sequence_prompt, log_sequence_samples = context.log_sequence_problematic_groups.to_prompt(
            per_group=3,
            return_logs=True,
            sample_index_start_from=len(original_log_lines) + 1,
        )
        original_log_lines += log_sequence_samples
        log_lines_prompt += log_sequence_prompt

    start_dt = pendulum.from_timestamp(report["inference_time"].get("start"), tz="UTC").astimezone(
        pendulum.timezone("Asia/Tokyo"),
    )
    current_date_str = f"{start_dt.to_date_string()}"

    reasoning_score, advisory_content = llm_analysis.invoke(
        template_name=template_name,
        args={
            "current_date": current_date_str,
            "keywords_log_lines": keyword_prompt,
            "symptoms": symptoms,
            "log_lines": log_lines_prompt,
            "training_groups": baseline_prompt,
            "original_log_lines": original_log_lines,
            "past_7_day_advisories": context.past_advisories.to_prompt("summary"),  # TODO: replace with symptom search
        },
    )
    cleaned_advisory_content = cleanup_past_advisories(advisory_content)
    symptom_res, existed = past_suppression.existed_symptom(
        cleaned_advisory_content,
        current_date=context.current_date_str,
    )
    if not existed:
        pending_save_symptoms.append(
            SymptomPayload(
                content=cleaned_advisory_content,
                panel="summary",
                date=context.current_date_str,
            ),
        )
    for symptom in pending_save_symptoms:
        vector_search.save_symptom(symptom)
    return reasoning_score, advisory_content


def generate_advisory(
    llm_analysis: PanelAnalysis,
    report: dict,
    *,
    context: PanelAnalysisContext,
):
    results = {}
    for panel, fns in FIELDS_ANALYSIS.items():
        try:
            panel_report = report.get(panel, {})
            if panel in ["numeric_fields", "discrete_fields"]:
                results[panel] = {}
                for field, result in panel_report.items():
                    if fns.validate_fn(result):
                        # These functions take 4 parameters
                        advisory = fns.analysis_fn(llm_analysis, result, field, context)
                        results[panel][field] = advisory
            elif panel == "log_rate":
                if context.log_rate_advisory and fns.validate_fn(panel_report):
                    results[panel] = {"content": context.log_rate_advisory}
            elif panel in ["log_structure", "sequence_pattern"]:
                if panel_report and fns.validate_fn(panel_report):
                    # These functions take 3 parameters
                    advisory = fns.analysis_fn(llm_analysis, panel_report, context)
                    if advisory:
                        results[panel] = advisory
            elif panel_report and fns.validate_fn(panel_report):
                # Default case - 3 parameters
                advisory = fns.analysis_fn(llm_analysis, panel_report, context)
                if advisory:
                    results[panel] = advisory
        except ServiceError as e:
            logger.exception("failed to generate advisory", panel=panel, error=e)
            raise
        except Exception as e:
            logger.exception("failed to generate advisory", panel=panel, error=e)

    return results
