from __future__ import annotations

import base64
import io
from datetime import timezone
from typing import TYPE_CHECKING

import pandas as pd
from matplotlib import pyplot as plt
from matplotlib.dates import DateFormatter

from anomaly_detector.job.share.peak_detection import peak_detection

if TYPE_CHECKING:
    from anomaly_detector.job.lograte import LogRateTask
    from llm.log_rate_analysis import LogRateAnalysis


def analysis_log_rate(
    analysis: LogRateAnalysis,
    score: float,
    log_rate_task: LogRateTask,
    use_vision=False,
) -> tuple[list, str]:
    df = log_rate_task.df
    if df.empty:
        return [], ""
    above_percentage = float((df["log_rate"] > df["upper"]).sum() / len(df) * 100)
    below_percentage = float((df["log_rate"] < df["lower"]).sum() / len(df) * 100)
    if use_vision:
        fig, ax = plt.subplots(figsize=(20, 12), tight_layout=True)
        ax.plot(df["timestamp"], df["log_rate"], color="tab:orange", label="Actual Log Rate")
        ax.plot(df["timestamp"], df["upper"], color="tab:green", label="Predicted Upper Band")
        ax.plot(df["timestamp"], df["lower"], color="tab:blue", label="Predicted Lower Band")
        ax.grid(visible=True)
        ax.set_xticks(pd.date_range(start=df["timestamp"].min(), end=df["timestamp"].max(), freq="1h"))
        date_form = DateFormatter("%H", tz="Asia/Tokyo")
        ax.xaxis.set_major_formatter(date_form)
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        plt.close()
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        parsed_result, res = analysis.invoke(
            image=f"data:image/png;base64,{image_base64}",
        )
    else:
        _, markers, properties = peak_detection(
            df,
            "log_rate",
            prominence=0.5,
            distance=30,
        )
        top_5_ts = df.iloc[markers[(-properties["prominences"]).argsort()[:5]]]["timestamp"]
        parsed_result = [
            (t, "spike") for t in sorted([ts.astimezone(timezone.utc).to_pydatetime() for ts in list(top_5_ts)])
        ]
        if len(top_5_ts):
            spikes_str = ", ".join([ts.strftime("%H:%M") for ts in top_5_ts])
            if len(top_5_ts) > 1:
                res = "There are significant spikes in log rate around "
            else:
                res = "There is a significant spike in log rate at "
            res += spikes_str
        else:
            res = ""

    above_statement = (
        f"{above_percentage:.2f}% of outliers are above the normal range. " if above_percentage > 1 else ""
    )
    below_statement = (
        f"{below_percentage:.2f}% of outliers are below the normal range. " if below_percentage > 1 else ""
    )

    def get_adverb(s):
        s = abs(s)
        if s > 0.5:
            return " significantly"
        if s > 0.25:
            return ""
        return " marginally"

    adverb = get_adverb(score)
    statement = f"Log rate has deviated{adverb} from the normal range. {above_statement}{below_statement}"
    return parsed_result, statement + res
