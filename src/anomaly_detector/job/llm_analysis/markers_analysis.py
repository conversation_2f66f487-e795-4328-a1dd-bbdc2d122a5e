from anomaly_detector.job.metric.metric import MetricTask
from anomaly_detector.marker import Prom<PERSON><PERSON><PERSON>, metric_marker_name
from anomaly_detector.sks.marker import CustomMarker
from anomaly_detector.sks.marker_descriptions import get_marker_description
from llm.metric_analysis import MetricAnalysis
from llm.qoe_analysis import QoEAnalysis


def analysis_markers(metric_tasks: list[MetricTask], metric_analyser: MetricAnalysis, qoe_analyser: QoEAnalysis):
    prom_metric_tasks = [metric_task for metric_task in metric_tasks if isinstance(metric_task.marker, PromMarker)]
    qoe_metric_tasks = [
        metric_task
        for metric_task in metric_tasks
        if isinstance(metric_task.marker, CustomMarker) and metric_task.marker.qoe_score
    ]
    metric_reports = {}
    metric_advisories = {}
    qoe_advisories = {}
    for task in prom_metric_tasks:
        try:
            results = task.inference_daily()
            for result in results:
                marker_name = metric_marker_name(task.marker.name, result["labels"])
                metric_reports[marker_name] = {
                    "name": marker_name,
                    "marker_type": task.marker.type,
                    "tag": task.marker.tag,
                    "description": get_marker_description(task.marker.name, lang="zh-cn"),
                    "score": result["score"],
                }
                if result["score"] > 0.2:
                    try:
                        advisory = metric_analyser.invoke(
                            marker=task.marker,
                            values=result["group"][["timestamp", task.marker.name, "upper", "lower"]],
                        )
                        metric_advisories[marker_name] = {"content": advisory}
                    except Exception as e:
                        print(e)
        except Exception as e:
            print(e)
            continue
    for task in qoe_metric_tasks:
        try:
            df = task.get_value_df()
            if isinstance(df, list) or df is None or df.empty:
                continue
            related_metrics = [
                m.marker for m in metric_tasks if isinstance(m.marker, PromMarker) and m.marker.tag == task.marker.tag
            ]
            if isinstance(task.marker, CustomMarker):
                try:
                    advisories = qoe_analyser.invoke(
                        qoe_marker=task.marker,
                        qoe_scores=df,
                        related_metrics=related_metrics,
                        metric_advisories=metric_advisories,
                    )
                    qoe_advisories.update(advisories)
                except Exception as e:
                    print(e)
        except Exception as e:
            print(e)
            continue
    advisories = {
        "metrics": metric_advisories,
    }
    if qoe_advisories:
        advisories["qoes"] = qoe_advisories
    return metric_reports, advisories
