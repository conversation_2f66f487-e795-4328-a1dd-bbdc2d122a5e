import copy

from llm.translator import Translator


def _translate(translator: Translator, target: dict, target_lang: str):
    for key, value in target.items():
        if isinstance(value, str):
            target[key] = translator.invoke(target_lang, value)
        elif isinstance(value, dict):
            _translate(translator, value, target_lang)


def translate_advisory(translator: Translator, advisory_result: dict, target_lang: str):
    translated_advisory = copy.deepcopy(advisory_result)
    _translate(translator, translated_advisory, target_lang)
    return translated_advisory
