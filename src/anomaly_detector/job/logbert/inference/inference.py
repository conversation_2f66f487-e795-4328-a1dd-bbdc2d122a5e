from datetime import <PERSON><PERSON><PERSON>

import numpy as np
import orjson
import pandas as pd
import polars as pl
import structlog
from pendulum import DateTime
from pypika import Table

import constants
from greptime import SyncGreptimeClient, build_greptime_log_sql, retrieve_original_logs_from_greptime
from loki.loki import retrieve_original_logs_from_loki


def worst_logs_query_sql(tenant_id: str, flow_id: str, log_seq_ids: list[str], start: DateTime, end: DateTime):
    table = Table(f"t_{tenant_id.replace('-', '_')}")
    conditions = [
        table.flow_id == flow_id,
        table.seq_id.isin(log_seq_ids),
    ]
    return build_greptime_log_sql(table, start, end, conditions)


class InferenceResult:
    def __init__(
        self,
        probabilities: list[float],
        predicted: list[float],
        start_position_of_sequences: int = 0,
    ):
        self.start_position_of_sequences = start_position_of_sequences
        self.predicted = predicted
        self.logger = structlog.get_logger("InferenceResult")
        self.result_df = pd.DataFrame(
            {
                "probabilities": probabilities,
            },
        )

    def predicted_percentage(self):
        return sum(self.predicted) / len(self.predicted)

    def merge_sequence_ids(self, df: pl.LazyFrame):
        columns = df.collect_schema().names()
        if constants.LOG_SEQ_ID_KEY not in columns:
            return
        ts_seq_df = (
            df.select([constants.TIMESTAMP_KEY, constants.LOG_SEQ_ID_KEY])
            .slice(self.start_position_of_sequences, len(self.result_df))
            .collect()
            .to_pandas()
        )
        self.result_df = pd.concat([self.result_df, ts_seq_df], axis=1)
        self.result_df.probabilities = self.result_df.probabilities.replace(-1, np.nan)

    def _original_logs_by_loki(
        self,
        worst: pd.DataFrame,
        start: DateTime,
        end: DateTime,
        tenant_id: str,
        flow_id: str,
        loki_url: str,
    ):
        res = retrieve_original_logs_from_loki(
            loki_url=loki_url,
            tenant_id=tenant_id,
            flow_id=flow_id,
            start=start,
            end=end,
            log_seq_ids=worst[constants.LOG_SEQ_ID_KEY].to_list(),
        )
        try:
            parsed_logs = pd.DataFrame(
                (
                    {
                        constants.LOG_SEQ_ID_KEY: orjson.loads(line).get(constants.ORIGINAL_LOG_SEQ_ID_KEY, ""),
                        "log": (int(t) // 1000_000_000, line),
                    }
                    for t, line in res[0]["values"]
                ),
            )
            return pd.merge(worst, parsed_logs, on=constants.LOG_SEQ_ID_KEY, how="left")
        except Exception as e:
            self.logger.error("Failed to retrieve worst logs", e=e)
            return None

    def _original_logs_by_greptime(
        self,
        worst: pd.DataFrame,
        start: DateTime,
        end: DateTime,
        flow_id: str,
        greptime_client: SyncGreptimeClient,
        greptime_table: str,
    ):
        log_seq_ids = worst[constants.LOG_SEQ_ID_KEY].to_list()
        try:
            res = retrieve_original_logs_from_greptime(
                greptime_client=greptime_client,
                flow_id=flow_id,
                start=start,
                end=end,
                log_seq_ids=log_seq_ids,
                greptime_table=greptime_table,
            )
            parsed_logs = pd.DataFrame(
                {
                    constants.LOG_SEQ_ID_KEY: log["seq_id"],
                    "log": (int(log["ts"]) // 1000_000_000, log["line"]),
                }
                for log in res
            )
            return pd.merge(worst, parsed_logs, on=constants.LOG_SEQ_ID_KEY, how="left")
        except Exception as e:
            self.logger.error("Failed to retrieve worst logs", e=e)
            return None

    def worst_df_with_logs(
        self,
        worst_n=10,
        *,
        tenant_id: str,
        flow_id: str,
        loki_url: str | None,
        greptime_client: SyncGreptimeClient | None,
        greptime_table: str | None = None,
    ):
        worst = self.result_df.nsmallest(worst_n, "probabilities")
        if constants.LOG_SEQ_ID_KEY not in worst.columns:
            return None
        worst[constants.TIMESTAMP_KEY] = worst[constants.TIMESTAMP_KEY].dt.tz_localize("UTC")
        start = worst[constants.TIMESTAMP_KEY].min() - timedelta(seconds=0.1)
        end = worst[constants.TIMESTAMP_KEY].max() + timedelta(seconds=0.1)
        if greptime_client and greptime_table:
            return self._original_logs_by_greptime(
                worst=worst,
                start=start,
                end=end,
                flow_id=flow_id,
                greptime_client=greptime_client,
                greptime_table=greptime_table,
            )
        if loki_url:
            return self._original_logs_by_loki(
                worst=worst,
                start=start,
                end=end,
                tenant_id=tenant_id,
                flow_id=flow_id,
                loki_url=loki_url,
            )
        return None

    def to_report(self, logs):
        self.result_df.probabilities = self.result_df.probabilities.replace(np.nan, -1)
        return {
            "predicted_percentage": self.predicted_percentage(),
            "probabilities": self.result_df.probabilities.to_list(),
            "logs": logs,
            "log_seq_ids": self.result_df[constants.LOG_SEQ_ID_KEY].to_list(),
        }
