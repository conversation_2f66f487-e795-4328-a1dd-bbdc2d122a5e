import copy
import io
import pathlib
import random
import re
import shutil
import string
import tempfile
from dataclasses import dataclass
from datetime import datetime, timezone
from functools import cached_property
from typing import TYPE_CHECKING, NamedTuple, cast

import boto3
import orjson
import pendulum
import polars as pl
import s3fs
import s3path
import structlog
from datasets import Dataset, DatasetDict

import constants
from anomaly_detector.config import LogBERTSettings
from anomaly_detector.job.accounting import AccountingCollector
from anomaly_detector.job.share import PrioritySamples, TaskType, count_keyword, utils
from anomaly_detector.s3_path_config import S3PathConfig
from config import SagemakerSettings
from greptime import SyncGreptimeClient
from llm.baseline_context import BaselineContext
from llm.contextual_ranking import ContextualRanking, ProblematicLogGroups
from utils import get_module

from .inference import InferenceResult
from .preprocess import DrainParser, tokenizer

ORIGINAL_LOG_SEQ_ID_KEY = constants.ORIGINAL_LOG_SEQ_ID_KEY
LOG_SEQ_ID_KEY = constants.LOG_SEQ_ID_KEY
overlapping_window = 256
MAX_DATASET_SIZE = 12000

if TYPE_CHECKING:
    from transformers.tokenization_utils_fast import PreTrainedTokenizerFast


def torch_mask_tokens(
    trained_tokenizer: "PreTrainedTokenizerFast",
    inputs,
    special_tokens_mask,
    *,
    mlm_probability: float,
):
    """Prepare masked tokens inputs/labels for masked language modeling: 80% MASK, 10% random, 10% original."""
    torch = get_module("torch")

    labels = inputs.clone()
    # We sample a few tokens in each sequence for MLM training (with probability `mlm_probability`)
    probability_matrix = torch.full(labels.shape, mlm_probability)
    if special_tokens_mask is None:
        special_tokens_mask = [
            trained_tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True) for val in labels.tolist()
        ]
        special_tokens_mask = torch.tensor(special_tokens_mask, dtype=torch.bool)
    else:
        special_tokens_mask = special_tokens_mask.bool()

    probability_matrix.masked_fill_(special_tokens_mask, value=0.0)
    masked_indices = torch.bernoulli(probability_matrix).bool()
    labels[~masked_indices] = -100  # We only compute loss on masked tokens
    mask_token = trained_tokenizer.mask_token
    if mask_token is None:
        raise ValueError(
            "The tokenizer does not have a mask token. Please make sure to use a tokenizer that has a mask token.",
        )
    inputs[masked_indices] = trained_tokenizer.convert_tokens_to_ids(mask_token)
    return inputs, labels


class LLMLogSample(NamedTuple):  # it has to be NamedTuple because of heappush
    timestamp: str
    log: str
    original: str


@dataclass
class LogBertInferenceResult:
    score: float
    sequences: list[dict]
    problematic_log_groups: ProblematicLogGroups | None = None

    def samples(self):
        if not self.problematic_log_groups:
            return []
        return [
            (sample["sample"].timestamp, sample["sample"].original)
            for sample in self.problematic_log_groups.sorted_samples(
                with_index=False,
            )
            if sample and "sample" in sample and sample["sample"] is not None
        ]


class LogBERTTask:
    last_trained_path_config: S3PathConfig | None = None
    path_config: S3PathConfig

    def __init__(
        self,
        tenant_id: str,
        flow_id: str,
        anomaly_detector_id: str,
        *,
        accounting_collector: AccountingCollector,
        contextual_ranking: ContextualRanking,
        path_config: S3PathConfig,
        logbert_settings: LogBERTSettings,
        sagemaker_settings: SagemakerSettings,
        parquet_storage_options: dict,
        train_source_dir: pathlib.Path,
        inference_source_dir: pathlib.Path,
        loki_url: str | None = None,
        task_type=TaskType.TRAIN,
        date=datetime.now(tz=timezone.utc),
        parquet_files: list[s3path.S3Path] | None = None,
        trained_date: datetime | None = None,
        last_trained_date: datetime | None = None,
        baseline_context: BaselineContext | None = None,
    ):
        self.logger = structlog.get_logger(
            "LogBERTTask",
            flow_id=flow_id,
            task_type=task_type,
            date=date,
            anomaly_detector_id=anomaly_detector_id,
        )
        self.contextual_ranking = contextual_ranking
        self.baseline_context = baseline_context
        self.parquet_storage_options = parquet_storage_options
        self.train_source_dir = train_source_dir
        self.inference_source_dir = inference_source_dir
        self.logbert_settings = logbert_settings
        self.sagemaker_settings = sagemaker_settings
        self.tenant_id = tenant_id
        self.flow_id = flow_id
        self.loki_url = loki_url
        self.trained_date = trained_date if task_type == TaskType.INFERENCE else date
        self.last_trained_date = last_trained_date
        self.task_type = task_type
        self.task_date: datetime = date
        self.path_config = path_config
        self.accounting_collector = accounting_collector
        if self.last_trained_date:
            self.last_trained_path_config = copy.copy(self.path_config)
            self.last_trained_path_config.trained_date = pendulum.instance(self.last_trained_date)
        self.tokenizer_builder = tokenizer.TokenizerBuilder(is_first_train=not last_trained_date)
        self.tokenizer: PreTrainedTokenizerFast | None = None
        self.df = self._load_parquet_file(parquet_files=parquet_files or [])

    @cached_property
    def _aws_s3_storage_options(self):
        if not self.sagemaker_settings.access_key or not self.sagemaker_settings.secret_key:
            # use default S3 credentials
            return {}
        return {
            "key": self.sagemaker_settings.access_key,
            "secret": self.sagemaker_settings.secret_key,
        }

    @property
    def _device(self):
        torch = get_module("torch")
        if torch.cuda.is_available():
            return "cuda"
        if torch.mps.is_available():
            return "mps"
        return "cpu"

    def build_tokenizer(self, drain_parser: DrainParser):
        self.logger.info("start build tokenizer")
        # load last trained tokenizer if exists, and train based on it
        self.tokenizer_builder.load(
            self.last_trained_path_config.tokenizer_dir
            if self.last_trained_date and self.last_trained_path_config
            else self.path_config.tokenizer_dir,
        )
        self.tokenizer_builder.train_from_drain_state(drain_parser)
        self.tokenizer_builder.save(self.path_config.tokenizer_dir)

    def upload_tokenizer_to_s3(self, target_dir=None):
        self.logger.info("upload tokenizer to s3", s3_path=self.path_config.aws_s3_trained_tokenizer or target_dir)
        self.tokenizer_builder.save(self.path_config.aws_s3_trained_tokenizer)

    def load_tokenizer(self):
        # download tokenizer from s3 to temporary directory
        # since AutoTokenizer.from_pretrained() only accept local path
        tmp_dir = tempfile.mkdtemp()
        for file in self.path_config.tokenizer_dir.iterdir():
            pathlib.Path(tmp_dir, file.name).write_bytes(file.read_bytes())
        PreTrainedTokenizerFastCls = get_module(  # noqa: N806
            "transformers.tokenization_utils_fast",
            "PreTrainedTokenizerFast",
        )
        self.tokenizer = PreTrainedTokenizerFastCls.from_pretrained(tmp_dir)

    def _load_hosted_gpu_model(self):
        BertForMaskedLM = get_module("transformers").BertForMaskedLM  # noqa: N806
        # download model from s3 to temporary directory
        tmp_dir = tempfile.mkdtemp()
        for file in self.path_config.hosted_gpu_model_dir.iterdir():
            pathlib.Path(tmp_dir, file.name).write_bytes(file.read_bytes())
        return BertForMaskedLM.from_pretrained(tmp_dir)

    def build_dataset(self):
        def window_generator():
            for win in pandas_df[constants.TEMPLATE_KEY].rolling(window=512, step=overlapping_window, center=True):
                yield {"text": " ".join(win.to_list())}

        def tokenize_dataset(ds):
            if self.tokenizer is None:
                raise ValueError("Tokenizer is not loaded")
            return self.tokenizer(
                ds["text"],
                padding=True,
                truncation=True,
                max_length=self.logbert_settings.max_len,
            )

        self.load_tokenizer()
        pandas_df = self.df.sort(constants.TIMESTAMP_KEY).select(constants.TEMPLATE_KEY).collect().to_pandas()
        pandas_df[constants.TEMPLATE_KEY] = pandas_df[constants.TEMPLATE_KEY].apply(lambda x: str(x).zfill(5))
        dataset = cast(Dataset, Dataset.from_generator(window_generator)).shuffle()
        if len(dataset) > MAX_DATASET_SIZE:
            dataset = dataset.select(range(MAX_DATASET_SIZE))
        train_set = cast(DatasetDict, dataset.map(tokenize_dataset, batched=True).train_test_split(test_size=0.1))
        self.accounting_collector.metrics.set("training_tokens", dataset.num_rows * 512)
        return train_set

    def save_dataset(self, dataset: Dataset | DatasetDict, hosted_gpu=False):
        if hosted_gpu:
            dataset.save_to_disk(self.path_config.hosted_gpu_staging_dir / "dataset")
        else:
            self.logger.info("upload dataset to s3", s3_path=self.path_config.aws_s3_trained_dataset)
            dataset.save_to_disk(
                self.path_config.aws_s3_trained_dataset.as_uri(),
                storage_options=self._aws_s3_storage_options,
            )

    def _start_hosted_gpu_training(self):
        from .train.train import LogBertTrainingArguments, train  # noqa: PLC0415

        for d in ["output", "pretrained_model", "dataset", "checkpoint"]:
            (self.path_config.hosted_gpu_staging_dir / d).mkdir(parents=True, exist_ok=True)
        args = LogBertTrainingArguments(
            training_dir=str(self.path_config.hosted_gpu_staging_dir.absolute()),
            output_dir=str(self.path_config.hosted_gpu_staging_dir.absolute()),
            model_dir=str((self.path_config.hosted_gpu_staging_dir / "output").absolute()),
            from_scratch=not self.last_trained_date,
        )
        train(args)

    def train_on_hosted_gpu(self):
        self.logger.info("start training on hosted gpu")
        if self.last_trained_date:
            self._prepare_fine_tune_model_hosted_gpu()
        self._start_hosted_gpu_training()
        self.logger.info("finish training on hosted gpu")

    def upload_hosted_gpu_model(self):
        model_dir = self.path_config.hosted_gpu_staging_dir / "output"
        for file in model_dir.iterdir():
            (self.path_config.hosted_gpu_model_dir / file.name).write_bytes(file.read_bytes())

    def cleanup_hosted_gpu(self):
        shutil.rmtree(self.path_config.hosted_gpu_staging_dir)

    def train_on_sagemaker(self):
        self.logger.info("start training on sagemaker")
        if self.last_trained_date:
            self._prepare_fine_tune_model_s3()
        sess, role = self._aws_sagemaker_session()
        source_dir = self.train_source_dir

        metric_definitions = [
            {"Name": "train_runtime", "Regex": r"train_runtime.*=\D*(.*?)$"},
            {"Name": "eval_accuracy", "Regex": r"eval_accuracy.*=\D*(.*?)$"},
            {"Name": "eval_loss", "Regex": r"eval_loss.*=\D*(.*?)$"},
        ]
        HuggingFaceCls = get_module(  # noqa: N806
            "sagemaker.huggingface",
            "HuggingFace",
        )

        huggingface_estimator = HuggingFaceCls(
            environment={"FFWD_FLOW_ID": self.flow_id},
            entry_point="train.py",
            source_dir=str(source_dir),
            base_job_name=f"lad-{self.flow_id}",
            instance_type="ml.g4dn.xlarge",
            instance_count=1,
            pytorch_version="1.13.1",
            transformers_version="4.26.0",
            py_version="py39",
            sagemaker_session=sess,
            role=role,
            hyperparameters={
                "epochs": self.logbert_settings.epochs,
                "output_dir": "/opt/ml/checkpoints",
                "from_scratch": not self.last_trained_date,
            },
            use_spot_instances=True,  # whether to use spot instances or not
            max_wait=72000,  # max time including spot start + training time
            max_run=40000,  # max expected training time
            metric_definitions=metric_definitions,
            # s3 uri where our checkpoints will be uploaded during training
            checkpoint_s3_uri=self.path_config.aws_s3_trained_checkpoint.as_uri(),
            # model_uri=
            output_path=self.path_config.aws_s3_trained_output.as_uri(),
        )  # type: ignore
        huggingface_estimator.fit(self.path_config.aws_s3_train_base.as_uri())
        job_detail = sess.describe_training_job(huggingface_estimator._current_job_name)  # noqa: SLF001
        self.accounting_collector.metrics.add("sagemaker_seconds", job_detail["BillableTimeInSeconds"])
        self._append_inference_code_to_model(huggingface_estimator.model_data, sess)

    def _append_inference_code_to_model(self, original_model_s3_url: str, session=None):
        S3Downloader = get_module(  # noqa: N806
            "sagemaker.s3",
            "S3Downloader",
        )
        S3Uploader = get_module(  # noqa: N806
            "sagemaker.s3",
            "S3Uploader",
        )
        sess = session if session else self._aws_sagemaker_session()[0]
        with (
            io.BytesIO(S3Downloader.read_bytes(original_model_s3_url, sagemaker_session=sess)) as trained_model_tar,
            io.BytesIO() as model_with_code_tar,
        ):
            inference_source_dir = self.inference_source_dir
            utils.append_tar_file(
                trained_model_tar,
                model_with_code_tar,
                list(inference_source_dir.iterdir()),
                prefix="code",
            )
            S3Uploader.upload_bytes(
                model_with_code_tar.getvalue(),
                (self.path_config.aws_s3_trained_output / "model.tar.gz").as_uri(),
                sagemaker_session=sess,
            )

    def generate_inference_dataset(self):
        pandas_df = self.df.sort(constants.TIMESTAMP_KEY).select(constants.TEMPLATE_KEY).collect().to_pandas()
        pandas_df[constants.TEMPLATE_KEY] = pandas_df[constants.TEMPLATE_KEY].apply(lambda x: str(x).zfill(5))
        rows = [
            {"text": " ".join(win.to_list())}
            for win in pandas_df[constants.TEMPLATE_KEY].rolling(window=512, step=512, center=True)
        ]
        self.accounting_collector.metrics.set("inference_tokens", len(rows) * 512)
        return rows

    def generate_jsonl_file_on_s3(self):
        self.logger.info("start generate jsonl file on s3", s3_path=self.path_config.aws_s3_inference_dataset)
        lines = self.generate_inference_dataset()
        s3 = s3fs.S3FileSystem(**self._aws_s3_storage_options)

        with s3.open(self.path_config.aws_s3_inference_dataset.as_uri(), "w") as f:
            f.writelines([orjson.dumps(line).decode("utf-8") + "\n" for line in lines])

    def inference_on_hosted_gpu(self):
        torch = get_module("torch")
        F = torch.nn.functional  # noqa: N806
        model = self._load_hosted_gpu_model()
        device = self._device
        self.logger.warning("hosted gpu device", device=device)
        model.to(self._device)
        batch_size = 32
        rows = self.generate_inference_dataset()
        if self.tokenizer is None:
            raise ValueError("Tokenizer is not initialized")
        candidate_size = self.logbert_settings.cardinality_size(self.tokenizer.vocab_size)
        input_texts = [row["text"] for row in rows]
        batches = utils.batch(input_texts, batch_size)
        results = []
        start_position = 0
        for batch in batches:
            tokenized_data = self.tokenizer(batch, padding=True, truncation=True, return_tensors="pt", max_length=512)
            original_input_ids = tokenized_data["input_ids"].clone().to(self._device)
            mask = (
                (tokenized_data["input_ids"] == self.tokenizer.cls_token_id)
                | (tokenized_data["input_ids"] == self.tokenizer.sep_token_id)
                | (tokenized_data["input_ids"] == self.tokenizer.pad_token_id)
            )
            tokenized_data["input_ids"], tokenized_data["labels"] = torch_mask_tokens(
                inputs=tokenized_data["input_ids"],
                trained_tokenizer=self.tokenizer,
                special_tokens_mask=mask,
                mlm_probability=self.logbert_settings.mlm_probability,
            )
            with torch.no_grad():
                model_output = model(**tokenized_data.to(self._device))

            for i in range(len(batch)):
                input_ids = tokenized_data["input_ids"][i]
                logits = model_output["logits"][i]
                mask_token_index = torch.where(input_ids == self.tokenizer.mask_token_id)[0]
                original_tokens = original_input_ids[i][mask_token_index]
                candidate_tokens = torch.topk(logits[mask_token_index], candidate_size)
                predicted = (original_tokens.unsqueeze(1) == candidate_tokens.indices).any(dim=1)
                softmax_result = F.softmax(logits, dim=1)
                probabilities = torch.full(input_ids.shape, -1.0, device=self._device)
                probabilities[mask_token_index] = softmax_result[
                    mask_token_index,
                    original_input_ids[i][mask_token_index],
                ]
                inference_result = InferenceResult(
                    probabilities=probabilities.cpu().numpy().tolist(),
                    predicted=predicted.cpu().numpy().tolist(),
                    start_position_of_sequences=start_position,
                )
                start_position += len(probabilities) - overlapping_window
                results.append(inference_result)
        return results

    def inference_on_sagemaker(self):
        self.logger.info("start inference on sagemaker")
        sess, role = self._aws_sagemaker_session()

        HuggingFaceModelCls = get_module(  # noqa: N806
            "sagemaker.huggingface",
            "HuggingFaceModel",
        )

        huggingface_model = HuggingFaceModelCls(
            model_data=(self.path_config.aws_s3_trained_output / "model.tar.gz").as_uri(),
            sagemaker_session=sess,
            role=role,
            transformers_version="4.26.0",  # transformers version used
            pytorch_version="1.13.1",  # pytorch version used
            py_version="py39",  # python version used
        )  # type: ignore

        batch_job = huggingface_model.transformer(
            instance_count=1,
            instance_type="ml.g4dn.xlarge",
            output_path=self.path_config.aws_s3_inference_output.as_uri(),
            strategy="SingleRecord",
            env={
                "FFWD_FLOW_ID": self.flow_id,
                "CANDIDATE_SIZE": str(self.logbert_settings.cardinality_size(self.tokenizer.vocab_size)),
            },
        )

        letters = string.ascii_lowercase
        random_suffix = "".join(random.choice(letters) for _ in range(4))

        batch_job.transform(
            self.path_config.aws_s3_inference_dataset.as_uri(),
            job_name=f"{self.flow_id}-{self.task_date.strftime(constants.DATETIME_FORMAT)}-{random_suffix}",
            content_type="application/json",
            split_type="Line",
        )
        new_sess, _ = self._aws_sagemaker_session()
        job_detail = new_sess.describe_transform_job(batch_job._current_job_name)  # noqa: SLF001
        self.accounting_collector.metrics.add(
            "sagemaker_seconds",
            (job_detail["TransformEndTime"] - job_detail["TransformStartTime"]).seconds,
        )

    def _prepare_fine_tune_model_hosted_gpu(self):
        if self.last_trained_path_config is None:
            raise ValueError("last_trained_path_config is None")
        target_dir = self.path_config.hosted_gpu_staging_dir / "pretrained_model"
        target_dir.mkdir(parents=True, exist_ok=True)
        for file in self.last_trained_path_config.hosted_gpu_model_dir.iterdir():
            (target_dir / file.name).write_bytes(file.read_bytes())

    def _prepare_fine_tune_model_s3(self):
        if self.last_trained_path_config is None:
            raise ValueError("last_trained_path_config is None")
        last_trained_model_tar_path = self.last_trained_path_config.aws_s3_trained_output / "model.tar.gz"
        with io.BytesIO(last_trained_model_tar_path.read_bytes()) as last_trained_model_tar:
            utils.uncompress_tar_file_and_upload(last_trained_model_tar, self.path_config.aws_s3_pretrained_model)

    def retrieve_results_from_s3(self):
        s3 = s3fs.S3FileSystem(**self._aws_s3_storage_options)
        result = s3.read_text((self.path_config.aws_s3_inference_output / "inference_dataset.jsonl.out").as_uri())
        predicted_results = [orjson.loads(s) for s in re.findall(r"(\{.+?\})", result)]
        start_position = 0
        inference_results = []
        for res in predicted_results:
            inference_result = InferenceResult(
                probabilities=res["probabilities"],
                predicted=res["predicted"],
                start_position_of_sequences=start_position,
            )
            inference_results.append(inference_result)
            start_position += len(res["probabilities"]) - overlapping_window
        return inference_results

    def contextual_ranking_samples(
        self,
        keywords_logs: list[LLMLogSample],
        bad_logs: list[LLMLogSample],
    ) -> ProblematicLogGroups:
        total_lines = self.df.select(pl.count()).collect().item()
        llm_response = self.contextual_ranking.invoke(
            total_lines=total_lines,
            sample_size=50,
            logs=[log.log for log in bad_logs],
        )
        llm_response.set_samples_based_on_index(bad_logs, lambda x: x.log)
        contextual_ranking_logs = [log.get("sample", None) for log in llm_response.sorted_samples(with_index=False)]
        combined_logs = keywords_logs + contextual_ranking_logs
        final_llm_response = self.contextual_ranking.invoke(
            total_lines=total_lines,
            sample_size=50,
            logs=[log.log for log in combined_logs if log is not None],
            baseline_context=self.baseline_context,
        )
        final_llm_response.set_samples_based_on_index(combined_logs, lambda x: x.log)

        def serializer(x: LLMLogSample):
            return {"log": x.log, "timestamp": x.timestamp, "original": x.original}

        final_llm_response.sample_serializer = serializer
        return final_llm_response

    def derive_log_report(
        self,
        inference_results: list[InferenceResult],
        greptime_client: SyncGreptimeClient | None,
        greptime_table: str | None,
        problematic_log_groups: ProblematicLogGroups | None = None,
    ):
        score = sum(
            1
            for d in inference_results
            if d.predicted_percentage() <= self.logbert_settings.predicted_percentage_threshold
        ) / len(inference_results)

        worst_10_inference_results = sorted(inference_results, key=lambda x: x.predicted_percentage())[:10]
        sequences_result = []
        llm_samples: PrioritySamples[LLMLogSample] = PrioritySamples(sample_size=500)
        keyword_samples: PrioritySamples[LLMLogSample] = PrioritySamples(sample_size=50)

        for ir in worst_10_inference_results:
            ir.merge_sequence_ids(self.df)
            worst_df = ir.worst_df_with_logs(
                worst_n=50,
                tenant_id=self.tenant_id,
                flow_id=self.flow_id,
                greptime_client=greptime_client,
                greptime_table=greptime_table,
                loki_url=self.loki_url,
            )
            if worst_df is None:
                continue
            worst_df = worst_df.dropna()
            if ir.predicted_percentage() <= self.logbert_settings.predicted_percentage_threshold:
                # bad sequence
                for row in worst_df.itertuples(index=False):
                    ts, log = row.log
                    parsed_log = orjson.loads(log)
                    if LOG_SEQ_ID_KEY in parsed_log:
                        del parsed_log[ORIGINAL_LOG_SEQ_ID_KEY]
                    if ORIGINAL_LOG_SEQ_ID_KEY in parsed_log:
                        del parsed_log[ORIGINAL_LOG_SEQ_ID_KEY]
                    content = LLMLogSample(timestamp=ts, log=orjson.dumps(parsed_log).decode("UTF-8"), original=log)
                    if row.probabilities < 0.5:
                        llm_samples.push(1 - row.probabilities, content)
                    keyword_samples.push(count_keyword(content.log), content)
            report = ir.to_report(worst_df.log.to_list()[:10])
            sequences_result.append(report)
        if len(llm_samples.samples):
            if not problematic_log_groups:
                try:
                    problematic_log_groups = self.contextual_ranking_samples(keyword_samples.items, llm_samples.items)
                except Exception:
                    self.logger.exception("Failed to contextual ranking")
                    raise
        else:
            problematic_log_groups = None
        return LogBertInferenceResult(
            score=score,
            sequences=sequences_result,
            problematic_log_groups=problematic_log_groups,
        )

    def _load_parquet_file(self, parquet_files: list[s3path.S3Path]):
        if not parquet_files:
            return pl.LazyFrame()
        files = filter(lambda f: f.exists(), parquet_files)
        ldf = [pl.scan_parquet(f.as_uri(), storage_options=self.parquet_storage_options) for f in files]
        return pl.concat(ldf, how="diagonal_relaxed", parallel=True).sort(constants.TIMESTAMP_KEY)

    def _aws_sagemaker_session(self):
        sagemaker_session_bucket = self.sagemaker_settings.bucket
        role = self.sagemaker_settings.execution_role  # anywhere else

        Session = get_module(  # noqa: N806
            "sagemaker.session",
            "Session",
        )

        return (
            Session(
                boto_session=boto3.session.Session(  # type: ignore
                    aws_access_key_id=self.sagemaker_settings.access_key,
                    aws_secret_access_key=self.sagemaker_settings.secret_key,
                    region_name=self.sagemaker_settings.aws_region,
                ),
                default_bucket=sagemaker_session_bucket,
            ),
            role,
        )
