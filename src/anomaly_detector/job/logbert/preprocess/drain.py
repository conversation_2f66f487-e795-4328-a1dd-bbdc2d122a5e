from __future__ import annotations

from typing import TYPE_CHECKING, cast

import orj<PERSON>
import structlog
from drain3 import Template<PERSON><PERSON>
from drain3.persistence_handler import Per<PERSON><PERSON>andler
from drain3.template_miner_config import TemplateMinerConfig

import constants
from anomaly_detector.job.share import LogSamplePersist, TaskType

if TYPE_CHECKING:
    from pathlib import Path

    import s3path


class S3PersistHandler(PersistenceHandler):
    def __init__(self, file_path: s3path.S3Path):
        self.file_path = file_path

    def save_state(self, state):
        self.file_path.write_bytes(state)

    def load_state(self):  # type: ignore
        if not self.file_path.exists():
            return None
        return self.file_path.read_bytes()


class DrainParser:
    def __init__(
        self,
        *,
        config_path: Path,
        skip_drain_templates_threshold: int,
        task_type=TaskType.TRAIN,
        persist_path: s3path.S3Path | None = None,
        sample_path: Path | None = None,
        is_priority_sample: bool = True,
    ):
        self.logger: structlog.BoundLogger = structlog.get_logger(scope="DrainParser").bind()
        self.task_type = task_type
        self.config_path = config_path
        self.persist_path = persist_path
        self.skip_drain_templates_threshold = skip_drain_templates_threshold
        self.persistence: S3PersistHandler | None = None
        self.log_sample_persist: LogSamplePersist | None = None
        if self.persist_path:
            self.persistence = S3PersistHandler(self.persist_path)
        self.drain_config = TemplateMinerConfig()
        self.drain_config.load(str(config_path))

        # the drain state has been loaded in constructor
        self.drain_miner = TemplateMiner(persistence_handler=self.persistence, config=self.drain_config)  # type: ignore
        # remove persistence handler, keep the changes of state in memory
        self.drain_miner.persistence_handler = None  # type: ignore

        if sample_path:
            self.log_sample_persist = LogSamplePersist(sample_path, is_priority_sample)
        self.logger.info("drain miner initialized", config_path=self.config_path)

    def parse_log(self, log: dict, additional_ignore_keys: list[str] | None = None):
        if self.should_skip:
            return 0
        copied_log = log.copy()
        if additional_ignore_keys:
            for key in additional_ignore_keys:
                if isinstance(key, str):
                    del copied_log[key]
        for special_key in constants.ORIGINAL_SPECIAL_KEYS:
            if special_key in copied_log:
                del copied_log[special_key]
        log_line = self._build_log_line(copied_log)
        copied_log[constants.ORIGINAL_TIMESTAMP_KEY] = log[constants.ORIGINAL_TIMESTAMP_KEY]
        res = self.drain_miner.add_log_message(log_line)
        if res["change_type"] != "none":
            self.logger.debug(res)
        if self.log_sample_persist:
            self.log_sample_persist.add_sample(str(res["cluster_id"]), orjson.dumps(copied_log).decode("utf-8"))
        return cast(int, res["cluster_id"])

    def force_save_state(self, reason):
        if self.drain_miner.persistence_handler is None:
            self.drain_miner.persistence_handler = self.persistence  # type: ignore
        self.drain_miner.save_state(reason)

    @property
    def should_skip(self):
        return self._template_count() >= self.skip_drain_templates_threshold

    def _template_count(self):
        return len(self.drain_miner.drain.clusters)

    def _build_log_line(self, log: dict):
        sorted_keys = sorted(log.keys())
        return " ".join([f"{k}={log[k]}" for k in sorted_keys])
