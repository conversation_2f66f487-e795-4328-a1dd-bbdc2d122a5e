import json
from pathlib import Path
from typing import Any

import s3path

from .drain import DrainParser

DEFAULT_SPECIAL_TOKEN_MAP = {
    "cls_token": "[CLS]",
    "mask_token": "[MASK]",
    "pad_token": "[PAD]",
    "sep_token": "[SEP]",
    "unk_token": "[UNK]",
}

DEFAULT_TOKENIZER_CONFIG = {
    "clean_up_tokenization_spaces": True,
    "cls_token": "[CLS]",
    "mask_token": "[MASK]",
    "model_max_length": 1000000000000000019884624838656,
    "pad_token": "[PAD]",
    "sep_token": "[SEP]",
    "tokenizer_class": "PreTrainedTokenizerFast",
    "unk_token": "[UNK]",
}

DEFAULT_TOKENIZER = {
    "version": "1.0",
    "truncation": {"direction": "Right", "max_length": 512, "strategy": "LongestFirst", "stride": 0},
    "padding": {
        "strategy": "BatchLongest",
        "direction": "Right",
        "pad_to_multiple_of": None,
        "pad_id": 0,
        "pad_type_id": 0,
        "pad_token": "[PAD]",
    },
    "added_tokens": [
        {
            "id": 0,
            "content": "[PAD]",
            "single_word": False,
            "lstrip": False,
            "rstrip": False,
            "normalized": False,
            "special": True,
        },
        {
            "id": 1,
            "content": "[UNK]",
            "single_word": False,
            "lstrip": False,
            "rstrip": False,
            "normalized": False,
            "special": True,
        },
        {
            "id": 2,
            "content": "[CLS]",
            "single_word": False,
            "lstrip": False,
            "rstrip": False,
            "normalized": False,
            "special": True,
        },
        {
            "id": 3,
            "content": "[SEP]",
            "single_word": False,
            "lstrip": False,
            "rstrip": False,
            "normalized": False,
            "special": True,
        },
        {
            "id": 4,
            "content": "[MASK]",
            "single_word": False,
            "lstrip": False,
            "rstrip": False,
            "normalized": False,
            "special": True,
        },
    ],
    "normalizer": None,
    "pre_tokenizer": {"type": "Whitespace"},
    "post_processor": {
        "type": "TemplateProcessing",
        "single": [
            {"SpecialToken": {"id": "[CLS]", "type_id": 0}},
            {"Sequence": {"id": "A", "type_id": 0}},
            {"SpecialToken": {"id": "[SEP]", "type_id": 0}},
        ],
        "pair": [
            {"SpecialToken": {"id": "[CLS]", "type_id": 0}},
            {"Sequence": {"id": "A", "type_id": 0}},
            {"SpecialToken": {"id": "[SEP]", "type_id": 0}},
            {"Sequence": {"id": "B", "type_id": 1}},
            {"SpecialToken": {"id": "[SEP]", "type_id": 1}},
        ],
        "special_tokens": {
            "[CLS]": {"id": "[CLS]", "ids": [2], "tokens": ["[CLS]"]},
            "[SEP]": {"id": "[SEP]", "ids": [3], "tokens": ["[SEP]"]},
        },
    },
    "decoder": None,
    "model": {
        "type": "WordLevel",
        "vocab": {"[PAD]": 0, "[UNK]": 1, "[CLS]": 2, "[SEP]": 3, "[MASK]": 4},
        "unk_token": "[UNK]",
    },
}


class TokenizerBuilder:
    def __init__(self, *, is_first_train: bool = True):
        self.tokenizer_config: dict = {}
        self.special_token_map: dict = {}
        self.tokenizer: dict = {}
        self.is_first_train = is_first_train

    def train_from_drain_state(self, drain_parser: DrainParser):
        current_tokens: dict[str, int] = self.tokenizer["model"]["vocab"]
        new_tokens: set[str] = set()
        for cluster in drain_parser.drain_miner.drain.clusters:
            token = str(cluster.cluster_id).zfill(5)
            if token not in current_tokens:
                new_tokens.add(token)
        if self.is_first_train:
            token_count = len(new_tokens)
            vocab_size = max(300, min(1000, token_count * 3))
            current_tokens.update({token: i for i, token in enumerate(new_tokens, start=5)})
            current_tokens.update(
                {"unused" + str(i): (i + len(current_tokens)) for i in range(vocab_size - len(current_tokens))},
            )
            self.tokenizer["model"]["vocab"] = current_tokens
            return

        token_list: list[dict[str, Any]] = [
            {"token": token, "id": token_id} for token, token_id in current_tokens.items()
        ]
        token_list = sorted(token_list, key=lambda x: x["id"])
        for token_dict in token_list:
            if token_dict["token"].startswith("unused") and new_tokens:
                next_token = new_tokens.pop()
                current_tokens[next_token] = token_dict["id"]
                del current_tokens[token_dict["token"]]
                if len(new_tokens) == 0:
                    break
        self.tokenizer["model"]["vocab"] = current_tokens

    def save(self, target_dir: s3path.S3Path | Path):
        (target_dir / "tokenizer.json").write_text(json.dumps(self.tokenizer))
        (target_dir / "tokenizer_config.json").write_text(json.dumps(self.tokenizer_config))
        (target_dir / "special_tokens_map.json").write_text(json.dumps(self.special_token_map))

    def load(self, tokenizer_dir: s3path.S3Path | Path):
        special_token_map_path = tokenizer_dir / "special_tokens_map.json"
        tokenizer_path = tokenizer_dir / "tokenizer.json"
        tokenizer_config_path = tokenizer_dir / "tokenizer_config.json"
        if special_token_map_path.exists():
            self.special_token_map = json.loads(special_token_map_path.read_text("utf-8"))
        else:
            self.special_token_map = DEFAULT_SPECIAL_TOKEN_MAP
            special_token_map_path.write_text(json.dumps(DEFAULT_SPECIAL_TOKEN_MAP))

        if tokenizer_config_path.exists():
            self.tokenizer_config = json.loads(tokenizer_config_path.read_text("utf-8"))
        else:
            self.tokenizer_config = DEFAULT_TOKENIZER_CONFIG
            tokenizer_config_path.write_text(json.dumps(DEFAULT_TOKENIZER_CONFIG))

        if tokenizer_path.exists():
            self.tokenizer = json.loads(tokenizer_path.read_text("utf-8"))
        else:
            self.is_first_train = True
            self.tokenizer = DEFAULT_TOKENIZER
