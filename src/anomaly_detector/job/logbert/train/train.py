import logging
import os
import sys
from dataclasses import dataclass

import datasets
from transformers import AutoModelForMaskedLM, AutoTokenizer, BertConfig, BertForMaskedLM
from transformers.data.data_collator import DataCollatorForLanguageModeling
from transformers.trainer_utils import get_last_checkpoint
from transformers.training_args import TrainingArguments

from anomaly_detector.job.logbert.train.trainer import EarlyStoppingCallback, LogBertTrainer, OnEpochBeginCallback
from utils import get_module

max_len = 512
hidden_size = 256

logger = logging.getLogger(__name__)

logging.basicConfig(
    level=logging.getLevelName("INFO"),
    handlers=[logging.StreamHandler(sys.stdout)],
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)


def print_files_in_dir(directory):
    for root, _dirs, files in os.walk(directory):
        for file in files:
            print(os.path.join(root, file))


@dataclass
class LogBertTrainingArguments:
    training_dir: str
    output_dir: str
    model_dir: str
    epochs: int = 100
    train_batch_size: int = 16
    eval_batch_size: int = 32
    warmup_steps: int = 500
    from_scratch: bool = True
    early_stopping_threshold: float = 0.002
    early_stopping_target: float = 0.08
    early_stopping_patience: int = 10


def train(args: LogBertTrainingArguments):
    torch = get_module("torch")
    logger.info(f"args: {args}")
    logger.info(f"training_dir {args.training_dir}")
    tokenizer = AutoTokenizer.from_pretrained(args.training_dir + "/tokenizer")
    logging.info("tokenizer initialized")
    # load datasets
    data = datasets.load_from_disk(args.training_dir + "/dataset")
    logging.info("dataset loaded")

    # compute metrics function for binary classification
    def compute_metrics(pred):
        # preds = pred.predictions.argmax(-1)
        # precision, recall, f1, _ = precision_recall_fscore_support(labels, preds, average="binary")
        # acc = accuracy_score(labels, preds)
        print("labels:", pred)
        return {"test": 0}

    if args.from_scratch:
        config = BertConfig(
            vocab_size=tokenizer.vocab_size,
            type_vocab_size=2,
            hidden_size=hidden_size,
            num_hidden_layers=4,
            num_attention_heads=4,
            intermediate_size=hidden_size * 2,  # logbert use 2, but bert use 4
        )
        model = BertForMaskedLM(config)
    else:
        model = AutoModelForMaskedLM.from_pretrained(
            args.training_dir + "/pretrained_model",
            ignore_mismatched_sizes=True,
        )

    training_args = TrainingArguments(
        output_dir=args.output_dir,
        overwrite_output_dir=get_last_checkpoint(args.output_dir) is not None,
        num_train_epochs=args.epochs,
        learning_rate=1e-3,
        logging_steps=200,
        adam_beta1=0.9,
        adam_beta2=0.999,
        weight_decay=0,
        fp16=torch.cuda.is_available(),
        # half_precision_backend="cuda_amp",
        per_device_train_batch_size=args.train_batch_size,
        per_device_eval_batch_size=args.eval_batch_size,
        save_total_limit=2,
        save_strategy="epoch",
        warmup_steps=args.warmup_steps,
        logging_first_step=True,
        load_best_model_at_end=True,
        evaluation_strategy="epoch",
    )

    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm_probability=0.5,
        pad_to_multiple_of=8,
    )

    trainer = LogBertTrainer(
        model=model,
        args=training_args,
        compute_metrics=compute_metrics,
        train_dataset=data["train"],
        eval_dataset=data["test"],
        data_collator=data_collator,
        tokenizer=tokenizer,
    )

    trainer.add_callback(OnEpochBeginCallback(trainer))
    trainer.add_callback(
        EarlyStoppingCallback(
            args.early_stopping_patience,
            args.early_stopping_threshold,
            args.early_stopping_threshold,
        ),
    )

    trainer.train()

    # evaluate model
    eval_result = trainer.evaluate(eval_dataset=data["test"])

    # writes eval result to file which can be accessed later in s3 ouput
    with open(os.path.join(args.output_dir, "eval_results.txt"), "w") as writer:
        print("***** Eval results *****")
        writer.writelines(f"{key} = {value}\n" for key, value in sorted(eval_result.items()))

    trainer.save_state()
    # Saves the model to s3
    trainer.save_model(args.model_dir)
