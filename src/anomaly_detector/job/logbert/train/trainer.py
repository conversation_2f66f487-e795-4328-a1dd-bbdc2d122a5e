import logging
import sys
from collections.abc import Callable

import numpy as np
from datasets import Dataset
from transformers.data.data_collator import DataCollator
from transformers.modeling_utils import PreTrainedModel
from transformers.tokenization_utils_base import PreTrainedTokenizerBase
from transformers.trainer import Trainer
from transformers.trainer_callback import TrainerCallback, TrainerControl, TrainerState
from transformers.trainer_utils import EvalPrediction
from transformers.training_args import TrainingArguments

from utils import get_module

# Constants for magic values
EPOCH_THRESHOLD_FOR_HYPER_CENTER = 10

logger = logging.getLogger(__name__)

logging.basicConfig(
    level=logging.getLevelName("INFO"),
    handlers=[logging.StreamHandler(sys.stdout)],
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)


class LogBertTrainer(Trainer):
    def __init__(
        self,
        model,
        args: TrainingArguments | None = None,
        data_collator: DataCollator | None = None,
        train_dataset: Dataset | None = None,
        eval_dataset: Dataset | dict[str, Dataset] | None = None,
        tokenizer: PreTrainedTokenizerBase | None = None,
        model_init: Callable[[], PreTrainedModel] | None = None,
        compute_metrics: Callable[[EvalPrediction], dict] | None = None,
        callbacks: list[TrainerCallback] | None = None,
        optimizers=(None, None),
        preprocess_logits_for_metrics=None,
    ):
        if args is None:
            raise ValueError("args cannot be None")
        super().__init__(
            model,
            args,
            data_collator,
            train_dataset,
            eval_dataset,
            tokenizer,
            model_init=model_init,
            compute_metrics=compute_metrics,
            callbacks=callbacks,
            optimizers=optimizers,
            preprocess_logits_for_metrics=preprocess_logits_for_metrics,
        )
        self.torch = get_module("torch")
        self.hyper_center = None
        self.device = self._device()
        self.hyper_criterion = self.torch.nn.MSELoss()

    def _device(self):
        device = "cpu"
        if self.torch.cuda.is_available():
            device = "cuda"
        elif self.torch.mps.is_available():
            device = "mps"
        return device

    def _calculate_hyper_center(self):
        logger.info("calculate_hyper_center")
        outputs = 0
        total_samples = 0

        def calculate_center(batch: dict[str, list]):
            nonlocal outputs, total_samples
            if self.tokenizer is None:
                return batch
            tokenized = self.tokenizer(
                batch["text"],
                padding=True,
                truncation=True,
                return_tensors="pt",
                max_length=512,
            )
            if self.model is None:
                return batch
            result = self.model.forward(**tokenized.to(self.device), output_hidden_states=True)
            cls_output = result.hidden_states[-1][:, 0, :]
            outputs += self.torch.sum(cls_output.detach().clone(), dim=0)
            total_samples += cls_output.size(0)
            return batch

        with self.torch.no_grad():
            if self.train_dataset:
                self.train_dataset.map(calculate_center, batched=True, batch_size=32)
            if self.eval_dataset:
                self.eval_dataset.map(calculate_center, batched=True, batch_size=32)

        logger.info("calculate_hyper_center done")
        return outputs / total_samples

    def calculate_hyper_center(self):
        if self.torch.cuda.is_available():
            with self.torch.cuda.amp.autocast():
                return self._calculate_hyper_center()
        else:
            return self._calculate_hyper_center()

    def compute_loss(self, model, inputs, return_outputs=False, _num_items_in_batch=None):
        outputs = model(**inputs, output_hidden_states=True)
        mask_loss = outputs["loss"]  # bert loss

        # [CLS]
        cls_output = outputs["hidden_states"][-1][:, 0, :]

        hyper_loss = 0
        # hypersphere_loss
        if self.hyper_center is None and self.state.epoch >= EPOCH_THRESHOLD_FOR_HYPER_CENTER:
            # init hyper_center it resumes from checkpoint
            self.hyper_center = self.calculate_hyper_center()

        if self.hyper_center is not None:
            hyper_loss = self.hyper_criterion(
                cls_output.squeeze(),
                self.hyper_center.expand(inputs["input_ids"].shape[0], -1),
            )

        # version 2.0 https://github.com/lukasruff/Deep-SVDD-PyTorch/blob/master/src/optim/deepSVDD_trainer.py
        # dist = torch.sum((cls_output - self.hyper_center) ** 2, dim=1)
        loss = mask_loss + 0.1 * hyper_loss
        # loss = mask_loss

        return (loss, outputs) if return_outputs else loss


class EarlyStoppingCallback(TrainerCallback):
    def __init__(self, early_stopping_patience: int, early_stopping_threshold: float, early_stopping_target: float):
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_threshold = early_stopping_threshold
        self.early_stopping_target = early_stopping_target
        self.early_stopping_patience_counter = 0

    def check_metric_value(self, args, state, _control, metric_value):
        # best_metric is set by code for load_best_model
        operator = np.greater if args.greater_is_better else np.less
        if state.best_metric is None or (
            operator(metric_value, state.best_metric)
            and abs(metric_value - state.best_metric) > self.early_stopping_threshold
        ):
            self.early_stopping_patience_counter = 0
        else:
            self.early_stopping_patience_counter += 1

    def on_evaluate(self, args, state, control, metrics, **_kwargs):
        metric_to_check = args.metric_for_best_model
        if not metric_to_check.startswith("eval_"):
            metric_to_check = f"eval_{metric_to_check}"
        print("metrics", metrics, state, args, self.early_stopping_patience_counter)
        metric_value = metrics.get(metric_to_check)
        if state.best_metric is not None and state.best_metric < self.early_stopping_target:
            control.should_training_stop = True
        self.check_metric_value(args, state, control, metric_value)
        if self.early_stopping_patience_counter >= self.early_stopping_patience:
            control.should_training_stop = True


class OnEpochBeginCallback(TrainerCallback):
    def __init__(self, trainer: LogBertTrainer):
        self.trainer = trainer

    def on_epoch_begin(self, _args: TrainingArguments, state: TrainerState, _control: TrainerControl, **_kwargs):
        logger.info(f"on_epoch_begin, state={state}")
        if state.epoch >= EPOCH_THRESHOLD_FOR_HYPER_CENTER:
            self.trainer.hyper_center = self.trainer.calculate_hyper_center()
