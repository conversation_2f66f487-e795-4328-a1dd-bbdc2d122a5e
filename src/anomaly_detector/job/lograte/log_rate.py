from datetime import datetime
from functools import cached_property

import orjson
import pandas as pd
import pendulum
import structlog
from pendulum import DateTime
from pydantic import BaseModel, ConfigDict, field_serializer, field_validator

from anomaly_detector.job.llm_analysis.log_rate import analysis_log_rate
from anomaly_detector.job.option import LADOptions
from anomaly_detector.job.preprocess.log_timeranges import LogTimeRangesSample
from anomaly_detector.s3_path_config import S3PathConfig
from custom_exceptions import ServiceError
from llm.log_rate_analysis import LogRateAnalysis
from prom import PrometheusClient, build_log_rate_query
from utils import prom_result_to_panda_df

RESOLUTION = 60


class LogRateTask:
    def __init__(
        self,
        prometheus_client: PrometheusClient,
        tenant_id: str,
        prophecy_id: str,
        metric_name: str,
        flow_id: str,
        start: DateTime,
        end: DateTime,
    ):
        self.tenant_id = tenant_id
        self.prometheus_client = prometheus_client
        self.prophecy_id = prophecy_id
        self.metric_name = metric_name
        self.flow_id = flow_id
        self.time = end
        self.start = start
        self.end = end
        self.logger = structlog.get_logger(
            "LogRateTask",
            prophecy_id=prophecy_id,
            metric_name=metric_name,
            flow_id=flow_id,
            start=start,
            end=end,
        )

    def daily_inference(self):
        df = self.df
        if df.empty:
            return 0
        outliers = ((df.log_rate > df.upper) | (df.log_rate < df.lower)).sum().item()
        return min(outliers / len(df), 1)

    @cached_property
    def log_rate(self):
        log_rate_query = build_log_rate_query(self.metric_name, self.flow_id, f"{RESOLUTION}s")
        return self.prometheus_client.query_range(
            start=self.start,
            end=self.end,
            step=f"{RESOLUTION}s",
            query=log_rate_query,
        )

    @cached_property
    def prophecy(self):
        snake_case_pid = self.prophecy_id.replace("-", "_")
        prophecy_yhat = f"prophecy:yhat:{snake_case_pid}"
        prophecy_stddev = f"prophecy:stddev:{snake_case_pid}"
        if not self.prometheus_client.mimir_enabled:
            prophecy_yhat = "_reserved_" + prophecy_yhat
            prophecy_stddev = "_reserved_" + prophecy_stddev
        yhat_res = self.prometheus_client.query_range(
            start=self.start,
            end=self.end,
            step=f"{RESOLUTION}s",
            query=prophecy_yhat,
        )
        stddev_res = self.prometheus_client.query_range(
            start=self.start,
            end=self.end,
            step=f"{RESOLUTION}s",
            query=prophecy_stddev,
        )
        return {
            "yhat": yhat_res,
            "stddev": stddev_res,
        }

    @cached_property
    def df(self):
        rate = self.log_rate
        prophecy = self.prophecy
        log_rate_df = prom_result_to_panda_df(rate, "log_rate")
        prophecy_yhat_df = prom_result_to_panda_df(prophecy["yhat"], "yhat")
        prophecy_stddev_df = prom_result_to_panda_df(prophecy["stddev"], "stddev")
        prophecy_yhat_df["upper"] = prophecy_yhat_df["yhat"] + 3 * prophecy_stddev_df["stddev"]
        prophecy_yhat_df["lower"] = prophecy_yhat_df["yhat"] - 3 * prophecy_stddev_df["stddev"]
        prophecy_yhat_df = prophecy_yhat_df.dropna()
        df = pd.merge_asof(log_rate_df, prophecy_yhat_df, on="timestamp", direction="nearest").sort_index()
        return df.ffill()


class LogRateReport(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    score: float = 0
    timestamps: list[DateTime] = []
    advisory: str = ""

    @field_validator("timestamps", mode="before")
    @classmethod
    def parse_timestamps(cls, ts_list: list[str | datetime]):
        return [
            pendulum.parse(ts, tz=pendulum.UTC) if isinstance(ts, str) else pendulum.instance(ts, tz=pendulum.UTC)
            for ts in ts_list
        ]

    @field_serializer("timestamps")
    def serialize_timestamps(self, timestamps):
        return [ts.to_iso8601_string() for ts in timestamps]

    def to_json(self):
        return orjson.dumps(self.model_dump())

    @cached_property
    def log_timeranges_sample(self) -> LogTimeRangesSample:
        return LogTimeRangesSample(self.timestamps)


def inference_log_rate(
    option: LADOptions,
    path_config: S3PathConfig,
    prometheus_client: PrometheusClient,
    metric_name: str,
    prophecy_id: str,
    lograte_analysis: LogRateAnalysis,
    use_vision: bool = True,
) -> LogRateReport:
    logger = structlog.get_logger("log_rate_report")
    if path_config.inference_log_rate_report.exists():
        try:
            report = orjson.loads(path_config.inference_log_rate_report.read_bytes())
            return LogRateReport(**report)
        except Exception:
            logger.warning("failed to load log rate report", exc_info=True)

    try:
        log_flow = option.log_flows[0]
        log_rate_task = LogRateTask(
            prometheus_client,
            tenant_id=option.tenant_id,
            flow_id=log_flow.flow_id,
            metric_name=metric_name,
            prophecy_id=prophecy_id,
            start=option.start,
            end=option.end,
        )
        score = log_rate_task.daily_inference()
        logger.warning("success log rate inference", log_rate_score=score)
    except Exception as e:
        raise ServiceError("failed to inference log rate") from e
    try:
        analysis_timestamps, advisory = analysis_log_rate(
            lograte_analysis,
            score,
            log_rate_task,
            use_vision=use_vision,
        )
        report = LogRateReport(
            score=score,
            timestamps=[t[0] for t in analysis_timestamps],
            advisory=advisory,
        )
        path_config.inference_log_rate_report.write_bytes(report.to_json())
    except Exception:
        logger.warning("failed to load openai provider", exc_info=True)
        return LogRateReport(score=score)
    else:
        return report
