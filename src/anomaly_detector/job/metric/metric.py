import pandas as pd
import structlog
from pendulum import DateTime

from anomaly_detector.job.prophecy.prophecy import get_prophecy, prophecy_result_to_dataframe
from anomaly_detector.marker import Marker, PromMarker
from anomaly_detector.sks.marker import CustomMarker, get_prom_marker_promql
from config import <PERSON>IM<PERSON><PERSON><PERSON><PERSON>
from constants import DF_TIMESTAMP_COL
from greptime import SyncGreptimeClient, greptime_timestamp_sql
from prom import PrometheusClient
from utils import prom_result_to_panda_df

RESOLUTION = 60 * 4
STEP = f"{RESOLUTION}s"


class MetricTask:
    def __init__(
        self,
        marker: Marker,
        start: DateTime,
        end: DateTime,
        *,
        greptime_client: SyncGreptimeClient,
        prometheus_client: PrometheusClient,
        tenant_id: str,
        prophecy_prometheus_client: PrometheusClient | None = None,
        prophecy_id: str | None = None,
        extra_filters: dict | None = None,
    ):
        self.tenant_id = tenant_id
        self.prometheus_client = prometheus_client
        self.prophecy_prometheus_client = prophecy_prometheus_client or prometheus_client
        self.greptime_client = greptime_client
        self.prophecy_id = prophecy_id
        self.marker = marker
        self.start = start
        self.end = end
        self.extra_filters = extra_filters
        self.logger = structlog.get_logger(
            "MetricTask",
            marker=marker,
            start=start,
            end=end,
        )

    def _get_prom_marker_value(self):
        if isinstance(self.marker, PromMarker):
            query = get_prom_marker_promql(self.marker, step=STEP, labels=self.extra_filters)
            return self.prometheus_client.query_range(
                start=self.start,
                end=self.end,
                step=STEP,
                query=query,
            )
        raise TypeError

    def _get_qoe_marker_value(self):
        if not (isinstance(self.marker, CustomMarker) and self.marker.qoe_score):
            raise TypeError
        res = self.greptime_client.execute(
            greptime_timestamp_sql(
                self.start,
                self.end,
                self.marker.sql,
                self.extra_filters,
            ),
        )
        df = pd.DataFrame(res).rename(columns={self.marker.timestamp_field: DF_TIMESTAMP_COL}).fillna(1)
        if df.empty:
            return df
        df[DF_TIMESTAMP_COL] = pd.to_datetime(df[DF_TIMESTAMP_COL], unit="ms", utc=True).dt.tz_convert(TIMEZONE)
        return df.set_index(DF_TIMESTAMP_COL)

    # TODO: support sql marker with prophecy

    def get_value_df(self):
        if isinstance(self.marker, PromMarker):
            return prom_result_to_panda_df(self._get_prom_marker_value(), self.marker.name, multi_series=True)
        if isinstance(self.marker, CustomMarker):
            if self.marker.qoe_score:
                return self._get_qoe_marker_value()
            raise TypeError
        return None

    def get_prophecy_df(self):
        if not self.prophecy_id:
            raise ValueError
        prophecy_res = get_prophecy(
            prometheus_client=self.prophecy_prometheus_client,
            tenant_id=self.tenant_id,
            prophecy_id=self.prophecy_id,
            start=self.start,
            end=self.end,
            step=STEP,
        )
        return prophecy_result_to_dataframe(prophecy_res, multi_series=True)

    def get_value_prophecy_df(self):
        prophecy_df = self.get_prophecy_df()
        value_df = self.get_value_df()
        if value_df is None or isinstance(value_df, list):
            raise TypeError("Multiple series are not supported for value_df")
        labels = value_df.attrs.get("labels", [])
        if value_df.empty or prophecy_df.empty:
            return pd.DataFrame()
        merged = pd.merge_asof(
            prophecy_df.sort_values(by=[DF_TIMESTAMP_COL, *labels]),
            value_df.sort_values(by=[DF_TIMESTAMP_COL, *labels]),
            on=DF_TIMESTAMP_COL,
            by=labels,
            direction="nearest",
        )
        merged["is_outlier"] = (merged[self.marker.name] > merged["upper"]) | (
            merged[self.marker.name] < merged["lower"]
        )
        merged.attrs["labels"] = labels
        return merged

    def inference_daily(self):
        df = self.get_value_prophecy_df()
        if df.empty:
            return []
        labels = df.attrs.get("labels", [])
        groups = [((), df)] if not labels else list(df.groupby(labels))
        results = []
        for label_values, group in groups:
            outliers = (
                ((group[self.marker.name] > group["upper"]) | (group[self.marker.name] < group["lower"])).sum().item()
            )
            score = min(outliers / len(group), 1)
            results.append({"labels": dict(zip(labels, label_values, strict=False)), "score": score, "group": group})
        return results
