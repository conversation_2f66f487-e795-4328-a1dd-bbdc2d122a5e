from dataclasses import dataclass, field

from pendulum import DateTime

from anomaly_detector.marker import Marker
from job.model import AnomalyDete<PERSON><PERSON>og<PERSON><PERSON>, AnomalyDetectorMetric<PERSON>low, RegexExcludedField


@dataclass
class SKSOptions:
    greptime_db: str
    prometheus_db: str
    site_id: str | None = None


@dataclass
class LADOptions:
    start: DateTime
    end: DateTime
    tenant_id: str
    run_id: str
    anomaly_detector_id: str
    mimir_enabled: bool
    log_flows: list[AnomalyDetectorLogFlow] = field(default_factory=list)
    metric_flows: list[AnomalyDetectorMetricFlow] = field(default_factory=list)
    markers: list[Marker] = field(default_factory=list)
    skip_public_holidays: bool = False
    skip_log_volume_profile: bool = False
    tenant_subdivision: str = ""
    tenant_country: str = ""
    job_start_time: int = 0
    greptime_table: str | None = None
    greptime_log_db: str | None = None
    greptime_db: str = "pythia"
    express_training: bool = False
    drain_ignored_keys: list[str] | None = None
    excluded_fields: list[str | RegexExcludedField] | None = None
    sks_options: SKSOptions | None = None
