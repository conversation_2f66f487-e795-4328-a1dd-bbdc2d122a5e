import time
from datetime import datetime, timezone

import pendulum

from anomaly_detector.job.share.utils import rmdir_recursive
from anomaly_detector.s3_path_config import S3PathConfig
from constants import DATE_FORMAT, DATETIME_FORMAT


class S3Cleaner:
    def __init__(self, path_config: S3PathConfig, retention_days: int, timeout: int):
        self.timeout = timeout
        self.path_config = path_config
        self.retention_days = retention_days
        self.now = time.time()

    @property
    def _timed_out(self):
        return time.time() - self.now > self.timeout

    def cleanup(self):
        self.cleanup_legacy_parquet_files()
        self.cleanup_parquet()
        self.cleanup_inference()
        self.cleanup_accounting()

    def cleanup_inference(self, retention_days=None):
        inference_date = self.path_config.inference_date
        if inference_date is None:
            return
        retention_days = retention_days or self.retention_days
        if self._timed_out:
            return
        for p in self.path_config.inference_base.parent.iterdir():
            if self._timed_out:
                return
            format_str = DATE_FORMAT if self.path_config.full_inference else DATETIME_FORMAT
            try:
                path_dt = datetime.strptime(p.name, format_str).replace(tzinfo=timezone.utc)
                path_dt = pendulum.instance(path_dt)
            except ValueError:
                continue
            if (inference_date - path_dt).days > retention_days and p.is_dir():
                rmdir_recursive(p)

    def cleanup_legacy_parquet_files(self, retention_days=None):
        if self._timed_out:
            return
        # inference_2024-08-31_2024-09-01.parquet
        # inference_2024-09-01-10-30_2024-09-01-10-40.parquet
        retention_days = retention_days or self.retention_days
        inference_date = self.path_config.inference_date
        if inference_date is None:
            return
        for p in self.path_config.parquet_base.iterdir():
            if self._timed_out:
                break
            if p.name.startswith("inference_") and p.name.endswith(".parquet"):
                _, end_dt_str = p.stem.split("_")[1:]
                try:
                    end_dt = datetime.strptime(end_dt_str, DATETIME_FORMAT).replace(tzinfo=timezone.utc)
                except ValueError:
                    end_dt = datetime.strptime(end_dt_str, DATE_FORMAT).replace(tzinfo=timezone.utc)
                end_dt = pendulum.instance(end_dt)
                if (inference_date - end_dt).days > retention_days:
                    p.unlink(missing_ok=True)

    def cleanup_parquet(self, retention_days=None):
        if self._timed_out:
            return
        retention_days = retention_days or self.retention_days
        inference_date = self.path_config.inference_date
        if inference_date is None:
            return
        base = (
            self.path_config.inference_daily_parquet_base
            if self.path_config.full_inference
            else self.path_config.inference_10m_parquet_base
        )
        for p in base.iterdir():
            if self._timed_out:
                break
            try:
                _, end_dt_str = p.name.split(".")[0].split("_")
                end_dt = datetime.strptime(end_dt_str, DATETIME_FORMAT).replace(tzinfo=timezone.utc)
                end_dt = pendulum.instance(end_dt)
            except Exception:
                continue

            if (inference_date - end_dt).days > retention_days and not p.is_dir():
                p.unlink(missing_ok=True)

    def cleanup_accounting(self, retention_days=None):
        if self._timed_out or self.path_config.inference_date is None:
            return
        retention_days = retention_days or self.retention_days
        for p in self.path_config.accounting_base.iterdir():
            if self._timed_out:
                break
            stat = p.stat()
            last_modified = pendulum.instance(stat.last_modified)
            if (self.path_config.inference_date - last_modified).days > retention_days:
                p.unlink(missing_ok=True)
