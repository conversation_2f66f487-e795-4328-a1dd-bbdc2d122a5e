import hashlib
import ipad<PERSON>
from datetime import datetime
from itertools import chain

import or<PERSON><PERSON>
import pyarrow as pa
import s3path
import structlog

from anomaly_detector.job.preprocess import utils
from anomaly_detector.job.share import Histogram
from anomaly_detector.job.utils import add_suffix_to_field_name
from constants import SPECIAL_KEYS, TEMPLATE_KEY


class IPValidator:
    def __init__(self):
        self.total_samples = 0
        self.failed_samples = 0

    def test(self, v):
        if self.total_samples >= 100:
            return self.is_valid_ip_field()
        if not self.is_valid_ip_field():
            return False
        if not v:
            return True
        self.total_samples += 1
        try:
            ipaddress.ip_address(v)
        except Exception:
            self.failed_samples += 1
            return self.is_valid_ip_field()
        else:
            return True

    def is_valid_ip_field(self):
        if self.total_samples < 100:
            return True
        failed_percentage = self.failed_samples / self.total_samples
        return not (self.total_samples >= 100 and failed_percentage >= 0.2)


class Field:
    def __init__(self, key: str, *, histogram_max_cardinality: int):
        self.logger = structlog.get_logger("field", key=key)
        self.key = key
        self.histogram: Histogram | None = Histogram()
        self.ip_validator: IPValidator | None = IPValidator()
        self.types = set()
        self.has_complex_type = False
        self.is_datetime = False
        self.logger.debug("init")
        self.histogram_max_cardinality = histogram_max_cardinality

    def test_value(self, value):
        if self.is_datetime:
            return
        t = type(value)
        if value is None:
            return
        if t is datetime:
            self.types.add(t)
            self.is_datetime = True
            return
        if t is list or t is dict:
            self.has_complex_type = True
            return
        if t is str and self.ip_validator:
            r = self.ip_validator.test(value)
            if not r:
                self.ip_validator = None
        if self.histogram:
            self.histogram.inc(value)
            if self.histogram.cardinality() > self.histogram_max_cardinality and self.key != TEMPLATE_KEY:
                self.histogram = None
        if t is float:
            if int in self.types:
                self.types.remove(int)
            self.types.add(float)
            return
        if t is int and float in self.types:
            return
        self.types.add(t)

    def is_numeric(self):
        return not self.has_complex_type and self.types.issubset({int, float}) and self.key != TEMPLATE_KEY

    def is_histogram(self):
        return (
            not self.has_complex_type
            and self.histogram
            and self.histogram.cardinality() <= self.histogram_max_cardinality
        )

    def is_enabled(self):
        return self.key in SPECIAL_KEYS or self.is_datetime or self.is_numeric() or self.is_histogram() or self.is_ip()

    def is_ip(self):
        if not self.ip_validator:
            return False
        return self.ip_validator and self.ip_validator.total_samples >= 100 and self.ip_validator.is_valid_ip_field()

    def to_dict(self):
        return {
            "key": self.key,
            "types": [t.__name__ for t in self.types],
            "enabled": self.is_enabled(),
            "numeric": self.is_numeric(),
            "is_ip": self.is_ip(),
            "is_datetime": self.is_datetime,
            "histogram": self.is_histogram(),
        }

    def to_arrow_field(self):
        if not self.is_enabled():
            return []
        pa_fields = [utils.type_to_arrow_type(t) for t in self.types]
        fields = []
        if len(pa_fields) == 1:
            fields.append(pa.field(self.key, pa_fields[0], nullable=True))
        else:
            fields += [pa.field(add_suffix_to_field_name(self.key, str(f)), f, nullable=True) for f in pa_fields]
        if self.is_ip():
            fields += [
                pa.field(add_suffix_to_field_name(self.key, "subdivisions"), pa.string(), nullable=True),
                pa.field(add_suffix_to_field_name(self.key, "country"), pa.string(), nullable=True),
            ]
        return fields

    def __str__(self):
        return f"Field(key={self.key}, types={self.types}, has_complex_type={self.has_complex_type}, histogram={self.is_histogram()}, numeric={self.is_numeric()})"


class LADSchema:
    def __init__(self, *, histogram_max_cardinality: int):
        self.fields: dict[str, Field] = {}
        self.extra_fields: dict[str, Field] = {}
        self.histogram_max_cardinality = histogram_max_cardinality

    def get_or_create_field(self, key: str, extra=False):
        fields = self.extra_fields if extra else self.fields
        if key in fields:
            return fields[key]
        field = Field(key, histogram_max_cardinality=self.histogram_max_cardinality)
        fields[key] = field
        return field

    def test_key_value(self, key: str, value, extra=False):
        field = self.get_or_create_field(key, extra)
        field.test_value(value)

    def get_hash(self):
        fields = [field.to_dict() for field in self.fields.values()]
        return hashlib.md5(orjson.dumps(fields, option=orjson.OPT_SORT_KEYS)).hexdigest()

    def save_schema(self, target_file: s3path.S3Path):
        target_file.write_bytes(orjson.dumps(self.to_object()))

    def to_object(self):
        return {field.key: field.to_dict() for field in self.fields.values()}

    @classmethod
    def load_from_object(cls, obj, histogram_max_cardinality: int):
        schema = cls(histogram_max_cardinality=histogram_max_cardinality)
        for key, value in obj.items():
            field = schema.get_or_create_field(key)
            field.is_datetime = value["is_datetime"]
            if value["is_ip"]:
                field.ip_validator = IPValidator()
                field.ip_validator.total_samples = 100
            else:
                field.ip_validator = None
            field.histogram = Histogram()
            for t in value["types"]:
                if t == "datetime":
                    field.types.add(type(datetime.now()))  # noqa: DTZ005
                elif t == "int":
                    field.types.add(int)
                elif t == "float":
                    field.types.add(float)
                elif t == "str":
                    field.types.add(str)
                elif t == "bool":
                    field.types.add(bool)
            if value["histogram"] is None:
                field.histogram = None
            elif value["histogram"] is False:
                field.has_complex_type = True
        return schema

    def get_histogram(self):
        hist: dict[str, Histogram] = {}
        for field_name, field in self.fields.items():
            histogram = field.histogram
            if histogram and histogram.total_rows > 0 and field_name not in SPECIAL_KEYS:
                hist[field_name] = histogram
        for field_name, field in self.extra_fields.items():
            if field.histogram and field.histogram.total_rows > 0:
                hist[field_name] = field.histogram
        return hist

    def save_histograms(self, filepath: s3path.S3Path):
        hist = self.get_histogram()
        filepath.write_bytes(orjson.dumps({k: v.to_object() for k, v in hist.items()}))
        return hist

    def create_arrow_schema(self):
        fields = list(chain(*(f.to_arrow_field() for f in self.fields.values())))
        return pa.schema(fields)
