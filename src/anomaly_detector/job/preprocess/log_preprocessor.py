from __future__ import annotations

import gc
import re
from typing import TYPE_CHECKING

import maxminddb
import orjson
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
import structlog
from pyarrow import RecordBatchStreamWriter, Schema
from pyarrow.fs import S3FileSystem

from anomaly_detector.job.preprocess.lad_schema import LADSchema
from anomaly_detector.job.share import Histogram, TaskType
from anomaly_detector.job.utils import (
    add_suffix_to_field_name,
    flatten_dict,
    flatten_nested_field,
    split_flattened_field,
)
from constants import ORIGINAL_TEMPLATE_KEY, ORIGINAL_TIMESTAMP_KEY

if TYPE_CHECKING:
    import s3path
    from pendulum import DateTime

    from anomaly_detector.config import AnomalyDetectorSettings
    from anomaly_detector.job.accounting import AccountingCollector
    from anomaly_detector.job.logbert import DrainParser
    from anomaly_detector.job.preprocess.log_retriever import Greptime<PERSON>ogRetriever
    from anomaly_detector.s3_path_config import S3PathConfig
    from config import S3Settings

    from .log_retriever import <PERSON><PERSON><PERSON><PERSON><PERSON>riever, LokiLogRetriever
    from .log_timeranges import LogTimeRangesSample

from .utils import extract_ip, reconcile_table, validate_type_by_name


class LogPreProcessor:
    def __init__(
        self,
        *,
        log_retriever: LokiLogRetriever | FileLogRetriever | GreptimeLogRetriever,
        anomaly_detector_setting: AnomalyDetectorSettings,
        s3_settings: S3Settings,
        s3_url: str,
        path_config: S3PathConfig,
        target_hosted_s3_path: s3path.S3Path,
        drain_parser: DrainParser,
        task_start: DateTime,
        task_date: DateTime,
        task_type: TaskType,
        accounting_collector: AccountingCollector,
        log_timeranges_sample: LogTimeRangesSample | None = None,
        drain_ignored_keys: list[str] | None = None,
        excluded_fields: list[str | dict] | None = None,
    ):
        self.logger = structlog.get_logger("LogPreProcessor")
        self.path_config = path_config
        self.drain_parser = drain_parser
        self.log_retriever = log_retriever
        self.task_start = task_start
        self.task_date = task_date
        self.task_type = task_type
        self.target_file_path = target_hosted_s3_path
        self.accounting_collector = accounting_collector
        self.log_timeranges_sample = log_timeranges_sample
        self.geo_database_path = anomaly_detector_setting.geo_database_path
        self.histogram_max_cardinality = anomaly_detector_setting.histogram_field.max_cardinality
        self.drain_ignored_keys = drain_ignored_keys
        self.excluded_keys = [k for k in excluded_fields if isinstance(k, str)] if excluded_fields else []
        self.excluded_regex: list[dict] = (
            [k for k in excluded_fields if isinstance(k, dict) and "regex" in k] if excluded_fields else []
        )
        self.parquet_s3_fs = S3FileSystem(
            anonymous=False,
            region="us-east-1",
            endpoint_override=s3_url,
            access_key=s3_settings.username,
            secret_key=s3_settings.password,
        )
        self.field_occurrence_hist = Histogram()
        self.drain_histogram = Histogram()
        self.current_schema: LADSchema = LADSchema(histogram_max_cardinality=self.histogram_max_cardinality)
        self.current_chunk = []
        self.schemas: dict[str, Schema] = {}
        self.arrow_writer: dict[str, RecordBatchStreamWriter] = {}
        self.chunk_size = anomaly_detector_setting.preprocess.record_batch_size
        self.logger.info("init")

    def load_model_schema(self):
        if self.path_config.schema.exists():
            self.current_schema.load_from_object(
                orjson.loads(self.path_config.schema.read_bytes()),
                self.histogram_max_cardinality,
            )

    def retrieve_logs_to_arrow_files(self):
        self.logger.info("start retrieve logs and convert to arrow files")
        for original_log in self.log_retriever.logs(self.task_type, self.task_start, self.task_date):
            template_id = str(self.drain_parser.parse_log(original_log, additional_ignore_keys=self.drain_ignored_keys))
            original_log[ORIGINAL_TEMPLATE_KEY] = template_id
            if self.log_timeranges_sample:
                self.log_timeranges_sample.add(
                    original_log[ORIGINAL_TIMESTAMP_KEY],
                    template_id,
                    orjson.dumps(original_log).decode("utf-8"),
                )
            log = flatten_dict(original_log)
            delete_keys = self.excluded_keys.copy()
            if self.excluded_keys:
                for key in log:
                    for rules in self.excluded_regex:
                        regex = rules["regex"]
                        if re.search(regex, key):
                            delete_keys.append(key)
            for key in delete_keys:
                log.pop(key, None)
            self._check_fields(log)
            self.current_chunk.append(log)

            if len(self.current_chunk) >= self.chunk_size:
                self._save_current_chunk()
                self.current_chunk.clear()
                gc.collect()
        if self.drain_parser.log_sample_persist:
            self.drain_parser.log_sample_persist.save_state()
        if self.current_chunk:
            self._save_current_chunk()
            self.current_chunk.clear()
        gc.collect()

    def convert_to_parquet(self):
        self.logger.debug("start convert to parquet", target_file=self.target_file_path)
        arrow_files = [self.path_config.staging_arrow_path(hash_key) for hash_key in self.schemas]
        arrow_schema = self.current_schema.create_arrow_schema()
        tables = []
        for af in arrow_files:
            with pa.ipc.open_stream(af) as reader:
                table = pa.Table.from_batches(batches=reader)
                reconciled_table = reconcile_table(table, arrow_schema)
                tables.append(reconciled_table)
        if not tables:
            return False
        pq.write_table(
            pa.concat_tables(tables, promote_options="default"),
            self.target_file_path.as_posix()[1:],
            filesystem=self.parquet_s3_fs,
        )
        parquet_file_stat = self.target_file_path.stat()
        self.accounting_collector.metrics.add("file_size_bytes", parquet_file_stat.st_size)
        self.logger.info("finished converting to parquet", target_file=self.target_file_path)
        for af in arrow_files:
            af.unlink()
        gc.collect()
        return True

    def _save_current_chunk(self):
        hash_key = self.current_schema.get_hash()

        if hash_key not in self.schemas:
            self.schemas[hash_key] = self.current_schema.create_arrow_schema()
            arrow_path = self.path_config.staging_arrow_path(hash_key)
            if not arrow_path.parent.exists():
                arrow_path.parent.mkdir(parents=True)
            self.arrow_writer[hash_key] = pa.ipc.new_stream(arrow_path, self.schemas[hash_key])
            self.logger.debug("create new schema", hash_key=hash_key, schema=self.current_schema.to_object())

        rb, df = self.chunks_to_record_batch(self.current_chunk, self.schemas[hash_key])
        self.logger.debug(
            "write record batch to arrow file",
            arrow_file_path=self.path_config.staging_arrow_path(hash_key),
            chunk_size=self.chunk_size,
        )
        self.arrow_writer[hash_key].write_batch(rb)
        del df

    def chunks_to_record_batch(self, chunks, schema: Schema):
        schema_names = schema.names
        d = pd.DataFrame(chunks)
        columns = pd.Index(schema.names)
        df = pd.concat([d[d.columns.intersection(schema.names)], pd.DataFrame(columns=columns.difference(d.columns))])
        geo_fields = set()
        for name in schema_names:
            # name is ["a"]["ipaddress"] or ["a"]["ipaddress@country"] etc
            nest_fields = split_flattened_field(name, keep_brackets=False)
            leaf_name = nest_fields[-1]
            has_suffix = "@" in leaf_name
            if not has_suffix:
                continue
            original_leaf_name, suffix = leaf_name.split("@") if has_suffix else (name, None)
            original_name = flatten_nested_field([*nest_fields[:-1], original_leaf_name])
            if suffix in ["country", "subdivisions"]:
                geo_fields.add(original_name)
            elif original_name in d:
                df[name] = d[original_name].apply(lambda x: x if validate_type_by_name(x, suffix) else None)

        with maxminddb.open_database(self.geo_database_path) as geo_reader:
            for geo_field in geo_fields:
                geo_locations = df[geo_field].apply(lambda x: extract_ip(geo_reader, x))
                (
                    df[add_suffix_to_field_name(geo_field, "country")],
                    df[add_suffix_to_field_name(geo_field, "subdivisions")],
                ) = zip(*geo_locations, strict=False)
                for name in ["country", "subdivisions"]:
                    field_name = add_suffix_to_field_name(geo_field, name)
                    for v in df[field_name]:
                        if v:
                            self.field_occurrence_hist.inc_keys([field_name])
                        self.current_schema.test_key_value(field_name, v, extra=True)
        del d
        return pa.RecordBatch.from_pandas(df, schema=schema), df

    def save_fields_occurrence(self):
        fields_occurrence_path = self.path_config.trained_fields_occurrence
        fields_occurrence_path.write_bytes(orjson.dumps(self.field_occurrence_hist))

    def _check_fields(self, obj):
        self.field_occurrence_hist.inc_keys(obj.keys())
        self.field_occurrence_hist.inc_rows(1)
        for k, v in obj.items():
            self.current_schema.test_key_value(k, v)
