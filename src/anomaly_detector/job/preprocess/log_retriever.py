import pathlib
import time
from datetime import datetime, timedelta, timezone

import or<PERSON>son
import pendulum
import structlog
from pendulum import DateTime
from pypika import Table
from tenacity import retry, stop_after_attempt, wait_fixed

import constants
from anomaly_detector.config import RetrieverSetting
from anomaly_detector.job.share import PublicHolidaySkipper, TaskType
from custom_exceptions import ServiceError
from greptime import SyncGreptimeClient, build_greptime_log_sql
from loki.loki import query_logs_from_loki

from .log_volume_profile import LogVolumeProfile


class LokiLogRetriever:
    def __init__(
        self,
        tenant_id: str,
        flow_id: str,
        log_volume_profile: LogVolumeProfile,
        *,
        loki_url: str,
        retriever_setting: RetrieverSetting,
    ):
        self.log_volume_profile = log_volume_profile
        self.flow_id = flow_id
        self.tenant_id = tenant_id
        self.retriever_setting = retriever_setting
        self.logger = structlog.get_logger("LokiLogRetriever", flow_id=flow_id, tenant_id=tenant_id)
        self.loki_url = loki_url
        self.public_holiday_skipper = PublicHolidaySkipper(
            country=log_volume_profile.holiday_setting.country,
            subdivision=log_volume_profile.holiday_setting.subdivision,
            mode="skip",
        )

    def logs(self, task_type: TaskType, start: datetime, end: datetime):
        if task_type == TaskType.TRAIN:
            return self._query_and_profile_training_logs(start, end)
        if task_type == TaskType.INFERENCE:
            return self._query_inference_logs(start, end)
        return None

    def _query_and_profile_training_logs(self, start: datetime, end: datetime):
        if self.log_volume_profile.holiday_setting.skip_public_holidays:
            ranges = self.public_holiday_skipper.date_ranges(start, end)
        else:
            ranges = [(start, end)]

        for range_start, range_end in ranges:
            if self.log_volume_profile.should_retrieve_all:
                yield from self._query_logs_range(range_start, range_end)
                return

            lines_time_slots = self.log_volume_profile.lines_time_slots()
            start = range_start
            end = range_start + timedelta(hours=1)
            while end < range_end:
                weekday = start.weekday()
                timeslot = start.hour
                required_lines = lines_time_slots[weekday][timeslot]
                yield from self._query_logs_range(start=start, end=end, total_lines=required_lines)
                start = end
                end = start + timedelta(hours=1)

    def _query_inference_logs(self, range_start: datetime, range_end: datetime):
        if self.log_volume_profile.should_retrieve_all:
            yield from self._query_logs_range(range_start, range_end)
            return

        lines_time_slots = self.log_volume_profile.lines_time_slots()
        start = range_start
        end = min(range_start + timedelta(hours=1), range_end)
        total_minutes = (range_end - range_start).total_seconds() / 60
        while end <= range_end:
            weekday = start.weekday()
            timeslot = start.hour
            required_lines = lines_time_slots[weekday][timeslot]
            if total_minutes < 60:
                # the required lines is hourly
                # so if total minutes is less than 60, we need to adjust the required lines
                required_lines = required_lines * total_minutes / 60
            yield from self._query_logs_range(start=start, end=end, total_lines=int(required_lines))
            start = end
            end = start + timedelta(hours=1)

    def _query_logs_range(self, start: datetime, end: datetime, total_lines: int | None = None):
        current_end_time = end
        lines = 0
        try:
            while current_end_time >= start and (total_lines is None or lines < total_lines):
                response = self._query_logs_from_loki(
                    current_end_time - timedelta(hours=1),
                    current_end_time,
                    limit=self.retriever_setting.line_limit,
                )
                if not len(response["result"]) or not response["result"][0]:
                    current_end_time = current_end_time - timedelta(minutes=30)
                    continue
                results = response["result"][0]["values"]
                for ts, log_str in results:
                    log = orjson.loads(log_str)
                    dt = datetime.fromtimestamp(int(ts) / 1000_000_000, tz=timezone.utc)
                    if start <= dt <= end:
                        log[constants.ORIGINAL_TIMESTAMP_KEY] = datetime.fromtimestamp(
                            int(ts) / 1000_000_000,
                            tz=timezone.utc,
                        )
                        yield log
                        lines += 1
                        if total_lines is not None and lines >= total_lines:
                            return
                next_end_time = datetime.fromtimestamp(int(results[-1][0]) / 1000_000_000, tz=timezone.utc)
                if next_end_time == current_end_time:
                    break
                current_end_time = next_end_time
                time.sleep(self.retriever_setting.sleep_time)
        except Exception as e:
            self.logger.exception("failed to retrieve logs")
            raise ServiceError("failed to retrieve logs") from e

    @retry(wait=wait_fixed(2), stop=stop_after_attempt(3))
    def _query_logs_from_loki(self, start: datetime, end: datetime, limit=1000):
        q = f'{{_reserved_flow_id="{self.flow_id}"}}'
        return query_logs_from_loki(
            self.loki_url,
            self.tenant_id,
            start,
            end,
            query=q,
            limit=limit,
        )


class FileLogRetriever:
    def __init__(self, filepath: pathlib.Path):
        self.filepath = filepath
        self.logger = structlog.get_logger("FileLogRetriever", filepath=filepath)

    def logs(self, _task_type: TaskType, task_date=datetime.now(tz=timezone.utc), _end_date: datetime | None = None):
        with open(self.filepath) as f:
            for line in f:
                log = self._process_line(line)
                if not log:
                    continue
                if log["timestamp"] > task_date:
                    continue
                yield log

    def _process_line(self, line: str):
        try:
            json_line = orjson.loads(line)
            timestamp = datetime.fromtimestamp(json_line["ts"], tz=timezone.utc)
            log = orjson.loads(json_line["log"])
            log["timestamp"] = timestamp
        except Exception:
            self.logger.exception("Failed to process line", line=line)
            return None
        else:
            return log


class GreptimeLogRetriever:
    def __init__(
        self,
        greptime_client: SyncGreptimeClient,
        table: str,
        retriever_setting: RetrieverSetting,
        log_volume_profile: LogVolumeProfile,
        filters: list | None = None,
        timestamp_field: str = "ts",
        log_field: str = "line",
        seq_id_field: str = "seq_id",
    ):
        self.retriever_setting = retriever_setting
        self.client = greptime_client
        self.table = table
        self.filters = filters
        self.timestamp_field = timestamp_field
        self.log_field = log_field
        self.seq_id_field = seq_id_field
        self.log_volume_profile = log_volume_profile
        self.public_holiday_skipper = PublicHolidaySkipper(
            country=log_volume_profile.holiday_setting.country,
            subdivision=log_volume_profile.holiday_setting.subdivision,
            mode="skip",
        )

    def logs(self, task_type: TaskType, start: DateTime, end: DateTime):
        if task_type == TaskType.TRAIN:
            return self._query_and_profile_training_logs(start, end)
        if task_type == TaskType.INFERENCE:
            return self._query_inference_logs(start, end)
        return None

    def _query_and_profile_training_logs(self, start: DateTime, end: DateTime):
        if self.log_volume_profile.holiday_setting.skip_public_holidays:
            ranges = self.public_holiday_skipper.date_ranges(start, end)
        else:
            ranges = [(start, end)]

        for range_start, range_end in ranges:
            if self.log_volume_profile.should_retrieve_all:
                yield from self._query_logs_range(range_start, range_end)
                return

            lines_time_slots = self.log_volume_profile.lines_time_slots()
            start = range_start
            end = range_start + timedelta(hours=1)
            while end < range_end:
                weekday = start.weekday()
                timeslot = start.hour
                required_lines = lines_time_slots[weekday][timeslot]
                yield from self._query_logs_range(start=start, end=end, total_lines=int(required_lines))
                start = end
                end = start + timedelta(hours=1)

    def _query_inference_logs(self, range_start: DateTime, range_end: DateTime):
        if self.log_volume_profile.should_retrieve_all:
            yield from self._query_logs_range(range_start, range_end)
            return

        lines_time_slots = self.log_volume_profile.lines_time_slots()
        start = range_start
        end = min(range_start + timedelta(hours=1), range_end)
        total_minutes = (range_end - range_start).total_seconds() / 60
        while end <= range_end:
            weekday = start.weekday()
            timeslot = start.hour
            required_lines = lines_time_slots[weekday][timeslot]
            if total_minutes < 60:
                # the required lines is hourly
                # so if total minutes is less than 60, we need to adjust the required lines
                required_lines = required_lines * total_minutes / 60
            yield from self._query_logs_range(start=start, end=end, total_lines=int(required_lines))
            start = end
            end = start + timedelta(hours=1)

    def _build_sql(self, start: datetime, end: datetime, limit=1000, _offset=0):
        t = Table(self.table)
        return build_greptime_log_sql(
            t,
            start=pendulum.instance(start),
            end=pendulum.instance(end),
            conditions=self.filters,
            limit=limit,
            timestamp_field=self.timestamp_field,
        )

    def _query_logs_range(self, start: datetime, end: datetime, total_lines: int | None = None):
        current_end_time = end
        lines = 0
        try:
            while current_end_time >= start and (total_lines is None or lines < total_lines):
                response = self._query_logs_greptime(
                    current_end_time - timedelta(hours=4),
                    current_end_time,
                    limit=self.retriever_setting.line_limit,
                    offset=0,
                )
                if not response:
                    current_end_time = current_end_time - timedelta(minutes=30)
                    continue
                for record in response:
                    dt = datetime.fromtimestamp(record[self.timestamp_field] / 1e9, tz=timezone.utc)
                    log = orjson.loads(record[self.log_field])
                    if start <= dt <= end:
                        log[constants.ORIGINAL_TIMESTAMP_KEY] = dt
                        if self.seq_id_field in record:
                            log[constants.ORIGINAL_LOG_SEQ_ID_KEY] = record[self.seq_id_field]
                        yield log
                        lines += 1
                        if total_lines is not None and lines >= total_lines:
                            return
                next_end_time = datetime.fromtimestamp(response[-1][self.timestamp_field] / 1e9, tz=timezone.utc)
                if next_end_time == current_end_time:
                    break
                current_end_time = next_end_time
                time.sleep(self.retriever_setting.sleep_time)
        except Exception as e:
            print("failed to retrieve logs")
            raise ServiceError("failed to retrieve logs") from e

    @retry(wait=wait_fixed(2), stop=stop_after_attempt(3))
    def _query_logs_greptime(self, start: datetime, end: datetime, limit: int, offset: int):
        sql = self._build_sql(start, end, limit, offset)
        return self.client.execute(sql)
