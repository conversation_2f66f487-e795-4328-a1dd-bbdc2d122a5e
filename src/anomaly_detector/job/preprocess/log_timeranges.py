from datetime import datetime, timedelta

from pendulum import DateTime

from anomaly_detector.job.share import Histogram, PrioritySamples, count_keyword


class TimeSample:
    def __init__(self, timestamp: DateTime, delta=timedelta(minutes=10)):
        self.timestamp = timestamp
        self.delta = delta
        self._time_range = (timestamp - delta, timestamp + delta)
        self.template_samples: dict[str, PrioritySamples[str]] = {}
        self.histogram = Histogram()

    def add(self, dt: datetime, key: str, log: str):
        if self._time_range[0] <= dt <= self._time_range[1]:
            if key not in self.template_samples:
                self.template_samples[key] = PrioritySamples(sample_size=20)
            keyword_count = count_keyword(log)
            self.template_samples[key].push(keyword_count, log)
            self.histogram.inc(key)
            return True
        return False


class LogTimeRangesSample:
    def __init__(self, timestamps: list[DateTime], delta=timedelta(minutes=10)):
        self.time_range_samples = [TimeSample(timestamp, delta) for timestamp in timestamps]

    def add(self, dt: datetime, template_id: str, log: str):
        for sample in self.time_range_samples:
            if sample.add(dt, template_id, log):
                break
