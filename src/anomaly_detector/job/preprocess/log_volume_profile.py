import math
from datetime import datetime, timedelta, timezone

import httpx

import utils
from anomaly_detector.config import HolidaySetting
from anomaly_detector.job.share import PublicHolidaySkipper
from custom_exceptions import ServiceError
from prom import (
    PrometheusClient,
    build_flow_bytes_query,
    build_log_rate_query,
)

http_client = httpx.Client()

"""
* for a given week, total volume and total log lines are known via Loki and TDengine
* Use previous week's (day -14 to -7) volume density profile to determine current week's sampling profile
* divide the week into 28 slots with 6 hours per time-slot
* query TDengine for total GB per each 6hr slot, calculate and assign a density percentage D(s)% of logs for each slot
  in percentage, i.e. (time-slot GB)/(week's total GB)
* query mimir for total log lines per each 6hr slot
* calculate average byte "L" per log line for the week (we need this to work out X lines look-back query)

* for log groups with more than 30GB per week of data, retrieve up to 10GB per week
  (this is W, the total week's GB to retrieve)
* For log groups with less than 30GB per week of data, retrieve 30% (up to 10GB) of week's volume.
  (this is corresponding W for this group)
* If 30% is less than 3.3GB for the week(max 10GB), retrieve all logs from the week for training.
  (this is corresponding W for this group)

* Perform query on current week (past 7 days): within each of the 28 time-slots, extract D(s)% multiply by "W" GB,
  divided by "L" to workout number of lines,  via Loki instance query with look-back X number of lines (knowing what byte/log on average)
"""

RETRIEVE_ALL_THRESHOLD_BYTES = 3.3 * 1e9
MAX_WEEKLY_BYTES = 10 * 1e9
MAX_DAILY_BYTES = 2 * 1e9
FFWD_ACCOUNT_ID = "_reserved_ffwd_accounting"


class LogVolumeProfile:
    def __init__(
        self,
        tenant_id: str,
        flow_id: str,
        metric_name: str,
        *,
        prometheus_client: PrometheusClient,
        holiday_setting: HolidaySetting,
        metronome_url: str,
        profile_date=datetime.now(tz=timezone.utc),
    ):
        self.tenant_id = tenant_id
        self.flow_id = flow_id
        self.metric_name = metric_name
        self.profile_date = profile_date
        self.total_bytes = 0
        self.total_log_lines = 0
        self.prometheus_client = prometheus_client
        self.metronome_accounting_client = PrometheusClient(
            mimir_proxy_url=metronome_url,
            uri="/metrics",
            mimir_enabled=False,
        )
        self.holiday_setting = holiday_setting
        self.holiday_skipper = PublicHolidaySkipper(
            country=self.holiday_setting.country,
            subdivision=self.holiday_setting.subdivision,
            mode="same_day_of_week",
        )
        self.adjusted = False
        # from Monday to Sunday, UTC
        self.weekly_profile = [[{"lines": 0, "bytes": 0}.copy() for _ in range(24)] for _ in range(7)]
        self._lines_time_slots = [[0] * 24 for _ in range(7)]
        self.has_profile = False

    @property
    def start_date(self):
        return self.profile_date.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=7)

    @property
    def end_date(self):
        return self.profile_date.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=0)

    @property
    def should_retrieve_all(self):
        return self.total_bytes * 0.3 <= RETRIEVE_ALL_THRESHOLD_BYTES and not self.adjusted

    def adjust_daily_profile(self, start_date: datetime, end_date: datetime):
        try:
            total_bytes = 0
            total_log_lines = 0
            step = "1h"
            weekly_profile = [[{"lines": 0, "bytes": 0}.copy() for _ in range(24)] for _ in range(7)]
            for datapoints, _ in self._fetch_bytes_per_slot_mimir(start_date, end_date):
                for point in datapoints:
                    total_bytes += point.value
                    weekly_profile[point.timestamp.weekday()][point.timestamp.hour]["bytes"] = point.value
            delta_days = (end_date - start_date).total_seconds() / 86400
            desired_bytes = MAX_DAILY_BYTES * delta_days
            if total_bytes < desired_bytes or desired_bytes < (0.1 * 1e9):
                return
            self.adjusted = True
            for datapoints, _ in self.prometheus_client.query_range_matrix(
                start_date,
                end_date,
                step,
                build_log_rate_query(self.metric_name, self.flow_id, step),
            ):
                for point in datapoints:
                    weekly_profile[point.timestamp.weekday()][point.timestamp.hour]["lines"] = point.value
                    total_log_lines += point.value
            average_log_line_byte = total_bytes / total_log_lines
            for weekday, day in enumerate(weekly_profile):
                for hour, profile in enumerate(day):
                    if profile["bytes"] > 0:
                        lines = math.ceil(profile["bytes"] / total_bytes * desired_bytes / average_log_line_byte)
                        if lines > 0:
                            self._lines_time_slots[weekday][hour] = max(512, lines) if lines else 0
        except Exception as e:
            raise ServiceError("Failed to retrieve log lines from mimir") from e

    def generate_profile(self):
        if self.holiday_setting.skip_public_holidays:
            ranges = self.holiday_skipper.date_ranges(self.start_date, self.end_date)
        else:
            ranges = [(self.start_date, self.end_date)]
        for start, end in ranges:
            try:
                for datapoints, _ in self._fetch_bytes_per_slot_mimir(start, end):
                    for point in datapoints:
                        self.weekly_profile[point.timestamp.weekday()][point.timestamp.hour]["bytes"] += point.value
                        self.total_bytes += point.value

                for datapoints, _ in self._fetch_lines_per_slot_mimir(start, end):
                    for point in datapoints:
                        self.weekly_profile[point.timestamp.weekday()][point.timestamp.hour]["lines"] += point.value
                        self.total_log_lines += point.value
                self.has_profile = True
            except Exception as e:
                raise ServiceError("Failed to retrieve log lines from mimir") from e
        if not self.should_retrieve_all:
            self._calculate_lines_time_slots()

    def _calculate_lines_time_slots(self):
        desired_bytes = min(self.total_bytes * 0.3, MAX_WEEKLY_BYTES)
        avg_line_byte = self.total_bytes / self.total_log_lines
        for d, day in enumerate(self.weekly_profile):
            for h, hour_profile in enumerate(day):
                lines = math.ceil(hour_profile["bytes"] / self.total_bytes * desired_bytes / avg_line_byte)
                self._lines_time_slots[d][h] = max(512, lines) if lines else 0

    def lines_time_slots(self):
        return self._lines_time_slots

    def _fetch_bytes_per_slot_mimir(self, start_date: datetime, end_date: datetime):
        step = "1h"
        query = build_flow_bytes_query(self.flow_id, step)
        return self.metronome_accounting_client.query_range_matrix(start_date, end_date, step, query)

    def _fetch_lines_per_slot_mimir(self, start_date: datetime, end_date: datetime):
        step = "1h"
        query = build_log_rate_query(self.metric_name, self.flow_id, step)
        return self.prometheus_client.query_range_matrix(start_date, end_date, step, query)

    def to_dict(self):
        return {
            "tenant_id": self.tenant_id,
            "flow_id": self.flow_id,
            "metric_name": self.metric_name,
            "profile_date": utils.iso_format(self.profile_date),
            "total_bytes": self.total_bytes,
            "total_log_lines": self.total_log_lines,
            "weekly_profile": self.weekly_profile,
            "holiday_setting": self.holiday_setting.model_dump(),
        }

    @classmethod
    def load_from_dict(cls, profile: dict, prometheus_client: PrometheusClient, metronome_url: str):
        p = cls(
            tenant_id=profile["tenant_id"],
            flow_id=profile["flow_id"],
            metric_name=profile["metric_name"],
            profile_date=datetime.fromisoformat(profile["profile_date"].replace("Z", "+00:00")),
            holiday_setting=HolidaySetting(
                skip_public_holidays=bool(profile["holiday_setting"]["skip_public_holidays"]),
                country=profile["holiday_setting"]["country"],
                subdivision=profile["holiday_setting"]["subdivision"],
            ),
            prometheus_client=prometheus_client,
            metronome_url=metronome_url,
        )
        if "total_batches" in profile:
            p.total_bytes = profile["total_batches"] * 1e9
            p.weekly_profile = [
                [{"lines": hour["lines"], "bytes": hour["batches"] * 1e9} for hour in week_day]
                for week_day in profile["weekly_profile"]
            ]
        else:
            p.total_bytes = profile["total_bytes"]
            p.weekly_profile = profile["weekly_profile"]
        p.total_log_lines = profile["total_log_lines"]
        p.has_profile = True
        if not p.should_retrieve_all:
            p._calculate_lines_time_slots()
        return p
