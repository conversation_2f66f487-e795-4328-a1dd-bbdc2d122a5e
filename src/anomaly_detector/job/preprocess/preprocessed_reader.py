import copy
from datetime import datetime, timed<PERSON>ta

import orjson
from pendulum import duration
from s3path import S3Path

from anomaly_detector.job.option import LADOptions
from anomaly_detector.job.share import Histogram, LogSamplePersist
from anomaly_detector.job.share.log_sample import RegularSamples
from anomaly_detector.s3_path_config import S3PathConfig


class PreprocessedReader:
    def __init__(self, option: LADOptions, path_config: S3PathConfig, last_trained_date: datetime | None = None):
        self.path_config = path_config
        self.option = option
        self.last_trained_date = last_trained_date
        self._is_train = self.path_config.inference_date is None

    def _past_path_configs(self, days: int):
        return [
            S3PathConfig(
                self.path_config.anomaly_detector_id,
                aws_s3_base_path=self.path_config.aws_s3_base,
                hosted_s3_base_path=self.path_config.base_path.parent,
                staging_base_path=self.path_config.staging_base_path,
                trained_date=self.path_config.trained_date - timedelta(days=i)
                if self.path_config.trained_date
                else None,
            )
            for i in range(days)
        ]

    def parquet_files(self) -> list[S3Path]:
        if self._is_train:
            return self._train_parquet_files()
        if self.path_config.inference_date is None:
            return []
        if self.path_config.full_inference:
            return [
                self.path_config.inference_daily_parquet_file(
                    self.path_config.inference_date.add(days=-1),
                    self.path_config.inference_date,
                ),
            ]
        return [
            self.path_config.inference_10m_parquet_file(
                self.path_config.inference_date.add(minutes=-10),
                self.path_config.inference_date,
            ),
        ]

    def _train_parquet_files(self) -> list[S3Path]:
        # the past 7 days
        if self.last_trained_date or self.option.express_training:
            return [self.path_config.train_parquet_file(self.option.start, self.option.end)]
        if self.path_config.trained_date is None:
            return []
        return [
            self.path_config.train_parquet_file(
                start=self.path_config.trained_date - duration(days=i + 1),
                end=self.path_config.trained_date - duration(days=i),
            )
            for i in range(7)
        ]

    def field_occurrence_hist(self):
        if self._is_train:
            files = self._train_field_occurrence_hist_files()
        else:
            files = self._inference_field_occurrence_hist_files()
        result = Histogram()
        for file in files:
            if file.exists():
                result.merge(Histogram.from_object(orjson.loads(file.read_bytes())))
        return result

    def _inference_field_occurrence_hist_files(self):
        return [self.path_config.inference_field_hist]

    def _train_past_path_configs(self):
        path_configs = []
        if self.path_config.trained_date is not None:
            for i in range(7):
                past_path_config = copy.copy(self.path_config)
                past_path_config.trained_date = self.path_config.trained_date - timedelta(days=i)
                path_configs.append(past_path_config)
        return path_configs

    def _train_field_occurrence_hist_files(self):
        if self.last_trained_date:
            return [self.path_config.trained_fields_occurrence]
        path_configs = self._past_path_configs(days=7)
        return [p.trained_fields_occurrence for p in path_configs]

    def discrete_fields_histograms(self):
        files = self._train_fields_histogram_files() if self._is_train else self._inference_fields_histogram_files()
        result = {}
        for file in files:
            if not file.exists():
                continue
            discrete_hist = {
                field: Histogram.from_object(obj) for field, obj in orjson.loads(file.read_bytes()).items()
            }
            for field, hist in discrete_hist.items():
                if field not in result:
                    result[field] = hist
                result[field].merge(hist)
        return result

    def _inference_fields_histogram_files(self):
        return [self.path_config.inference_histograms]

    def _train_fields_histogram_files(self):
        if self.last_trained_date:
            return [self.path_config.trained_histograms]
        path_configs = self._past_path_configs(days=7)
        return [p.trained_histograms for p in path_configs]

    def train_drain_histogram(self):
        files = self._train_drain_histogram_files()
        result = Histogram()
        for file in files:
            if file.exists():
                result.merge(Histogram.from_object(orjson.loads(file.read_bytes())))
        return result

    def _train_drain_histogram_files(self):
        if self.last_trained_date:
            return [self.path_config.trained_drain_histogram]
        path_configs = self._past_path_configs(days=7)
        return [p.trained_drain_histogram for p in path_configs]

    def train_log_samples(self):
        files = self._train_log_samples_files()
        final_log_sample_persist = LogSamplePersist(self.path_config.train_log_samples, priority=False)
        for file in files:
            log_sample = LogSamplePersist(file, priority=False)
            log_sample.load()
            for tid, samples in log_sample.log_samples.items():
                if tid not in final_log_sample_persist.log_samples:
                    final_log_sample_persist.log_samples[tid] = samples
                else:
                    # merge samples
                    s = final_log_sample_persist.log_samples[tid]
                    if isinstance(s, RegularSamples) and isinstance(samples, RegularSamples):
                        s.samples.extend(samples.samples)
        return final_log_sample_persist

    def _train_log_samples_files(self):
        if self.last_trained_date:
            return [self.path_config.train_log_samples]
        path_configs = self._past_path_configs(days=7)
        return [p.train_log_samples for p in path_configs]
