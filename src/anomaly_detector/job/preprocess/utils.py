from datetime import datetime
from functools import lru_cache

import pyarrow as pa
import structlog

logger = structlog.get_logger(__name__)


def type_to_arrow_type(t: type):
    if t is str:
        return pa.string()
    if t is int:
        return pa.int64()
    if t is float:
        return pa.float64()
    if t is bool:
        return pa.bool_()
    if t is datetime:
        return pa.timestamp("ns")
    return pa.null()


def validate_type_by_name(suffix: str, value):
    if suffix == "string":
        return isinstance(value, str)
    if suffix == "int64":
        return isinstance(value, int)
    if suffix == "float64":
        return isinstance(value, float)
    if suffix == "bool":
        return isinstance(value, bool)
    if suffix == "timestamp":
        return isinstance(value, datetime)
    return False


def reconcile_table(table: pa.Table, target_schema: pa.Schema):
    new_table = table
    changed_columns = []
    for name in table.schema.names:
        field_index = table.schema.get_field_index(name)
        field = table.schema.field(field_index)
        target_field_index = target_schema.get_field_index(name)
        if target_field_index != -1:
            if field.type == pa.int64() and target_schema.field(target_field_index).type == pa.float64():
                new_c = new_table.column(field_index).cast(pa.float64())
                changed_columns.append((field_index, name, new_c))
            continue

        new_column_name = f'{name}["@{field.type!s}"]'
        target_field_index = target_schema.get_field_index(new_column_name)
        if target_field_index == -1:
            logger.warning("Field not found", name=name, field_type=field.type)
            continue
        column_names = new_table.column_names
        column_names[field_index] = new_column_name
        new_table = new_table.rename_columns(column_names)
    if changed_columns:
        for index, name, new_column in changed_columns:
            new_table = new_table.set_column(index, name, new_column)
    return new_table


@lru_cache(maxsize=64)
def read_ip(reader, ip: str, with_city=False):
    try:
        r = reader.get(ip)
        if not r:
            return (None, None, None) if with_city else (None, None)
        country = r.get("country", {}).get("names", {}).get("en", None)
        subdivisions = r.get("subdivisions", [])
        subdivision = None
        if subdivisions:
            subdivision = subdivisions[0].get("names", {}).get("en")
        if with_city:
            city = r.get("city", {}).get("names", {}).get("en", None)
            return country, subdivision, city
        return country, subdivision
    except Exception:
        return (None, None, None) if with_city else (None, None)


def extract_ip(reader, ip, with_city=False):
    if not ip or not isinstance(ip, str):
        return (None, None, None) if with_city else (None, None)
    return read_ip(reader, ip, with_city)
