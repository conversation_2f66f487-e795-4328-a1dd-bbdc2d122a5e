from pendulum import DateTime

from prom import PrometheusClient
from prophecy.utils import prophecy_table_name
from utils import prom_result_to_panda_df


def get_prophecy(
    prometheus_client: PrometheusClient,
    tenant_id: str,
    prophecy_id: str,
    start: DateTime,
    end: DateTime,
    step: str,
):
    if prometheus_client.mimir_enabled:
        snake_case_pid = prophecy_id.replace("-", "_")
        prophecy_yhat = f"prophecy:yhat:{snake_case_pid}"
        prophecy_stddev = f"prophecy:stddev:{snake_case_pid}"
    else:
        prophecy_yhat = prophecy_table_name(tenant_id, prophecy_id, "yhat")
        prophecy_stddev = prophecy_table_name(tenant_id, prophecy_id, "stddev")
    yhat_res = prometheus_client.query_range(
        start=start,
        end=end,
        step=step,
        query=prophecy_yhat,
    )
    stddev_res = prometheus_client.query_range(
        start=start,
        end=end,
        step=step,
        query=prophecy_stddev,
    )
    return {
        "yhat": yhat_res,
        "stddev": stddev_res,
    }


def prophecy_result_to_dataframe(prophecy_result, *, multi_series=False):
    prophecy_yhat_df = prom_result_to_panda_df(prophecy_result["yhat"], "yhat", multi_series=multi_series)
    prophecy_stddev_df = prom_result_to_panda_df(prophecy_result["stddev"], "stddev", multi_series=multi_series)
    prophecy_yhat_df["upper"] = prophecy_yhat_df["yhat"] + 3 * prophecy_stddev_df["stddev"]
    prophecy_yhat_df["lower"] = prophecy_yhat_df["yhat"] - 3 * prophecy_stddev_df["stddev"]
    return prophecy_yhat_df.dropna()
