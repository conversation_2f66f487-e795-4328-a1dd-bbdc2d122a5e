from collections import defaultdict
from dataclasses import dataclass, field
from typing import Any

import pandas as pd

from anomaly_detector.job.utils import flatten_dict_keys


@dataclass
class Histogram:
    total_rows: int = 0
    cardinalities: defaultdict[Any, int] = field(default_factory=lambda: defaultdict(int))

    def inc(self, key, n=1):
        self.cardinalities[key] += n
        self.total_rows += n

    def inc_keys(self, keys, n=1):
        for key in keys:
            self.cardinalities[key] += n

    def inc_rows(self, n=1):
        self.total_rows += n

    def cardinality(self):
        return len(self.cardinalities)

    def to_object(self):
        cardinalities = [{"value": k, "count": v} for k, v in self.cardinalities.items()]
        return {
            "total_rows": self.total_rows,
            "cardinalities": cardinalities,
        }

    @classmethod
    def from_object(cls, obj, flatten_value=False):
        hist = cls()
        hist.total_rows = obj["total_rows"]
        cardinalities = obj["cardinalities"]
        if isinstance(cardinalities, dict):
            if flatten_value:
                migrated_cardinalities = flatten_dict_keys(cardinalities)
                hist.cardinalities = defaultdict(int, migrated_cardinalities)
            else:
                hist.cardinalities = defaultdict(int, cardinalities)
        else:
            hist.cardinalities = defaultdict(int, {c["value"]: c["count"] for c in cardinalities})
        return hist

    def to_percentage_df(self):
        df = pd.DataFrame(
            {"value": list(self.cardinalities.keys()), "count": list(self.cardinalities.values())},
        ).set_index("value")
        df["percentage"] = df["count"] / self.total_rows
        return df

    def merge(self, hist):
        self.total_rows += hist.total_rows
        for key, count in hist.cardinalities.items():
            self.cardinalities[key] += count
