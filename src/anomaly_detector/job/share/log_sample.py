import random
import re
from heapq import heappush, heappushpop
from pathlib import Path
from typing import Generic, TypeVar

import orjson

from constants import PARTIAL_CONFIG_KEYWORDS, PARTIAL_ERROR_KEYWORDS, WHOLE_CONFIG_KEYWORDS

partial_keyword_regex = re.compile("|".join(PARTIAL_CONFIG_KEYWORDS | PARTIAL_ERROR_KEYWORDS), re.IGNORECASE)
whole_keyword_regex = re.compile(
    r"\b(" + "|".join(WHOLE_CONFIG_KEYWORDS | PARTIAL_ERROR_KEYWORDS) + r")\b",
    re.IGNORECASE,
)


def count_keyword(text: str):
    partial_count = len(partial_keyword_regex.findall(text))
    whole_count = len(whole_keyword_regex.findall(text))
    return partial_count + whole_count


T = TypeVar("T")


class BaseSamples(Generic[T]):
    def __init__(self, sample_size=10):
        self.sample_size = sample_size

    def push(self, priority: int, content: T):
        raise NotImplementedError("Subclasses should implement this method")

    @property
    def items(self):
        raise NotImplementedError("Subclasses should implement this method")

    def load(self, data):
        raise NotImplementedError("Subclasses should implement this method")

    def save(self):
        raise NotImplementedError("Subclasses should implement this method")


class PrioritySamples(BaseSamples[T]):
    def __init__(self, sample_size=10):
        super().__init__(sample_size)
        self.samples: list[tuple[int, T]] = []

    def push(self, priority: int, content: T):
        if len(self.samples) == self.sample_size:
            smallest = self.samples[0][0]
            if priority > smallest:
                heappushpop(self.samples, (priority, content))
        else:
            heappush(self.samples, (priority, content))

    @property
    def items(self):
        return [x[1] for x in sorted(self.samples, reverse=True)]

    def sample(self, priority_threshold: int, sample_size: int, keep_severity: bool = False):
        items = [x for x in sorted(self.samples, reverse=True) if x[0] >= priority_threshold]
        severity_logs = []
        if keep_severity:
            for _, log in items:
                try:
                    parsed = orjson.loads(log)
                    if parsed.get("severity") in ["err", "alert", "crit", "emerg"]:
                        severity_logs.append(log)
                except:
                    pass
        if len(items) > sample_size:
            items = random.sample(items, sample_size)
        if keep_severity:
            return [x[1] for x in items], severity_logs
        return [x[1] for x in items]

    def load(self, data):
        if len(data) and isinstance(data[0], str):
            self.samples = [(0, c) for c in data]
        else:
            self.samples = [(p, c) for p, c in data]

    def dump(self):
        return self.samples


class RegularSamples(BaseSamples[T]):
    def __init__(self, sample_size=10):
        super().__init__(sample_size)
        self.samples: list[T] = []

    def push(self, priority: int, content: T):
        if len(self.samples) == self.sample_size:
            return
        self.samples.append(content)

    @property
    def items(self):
        return self.samples

    def load(self, data):
        self.samples = data
        self.sample_size = len(self.samples)

    def dump(self):
        return self.samples


class LogSamplePersist:
    def __init__(self, sample_path: Path, priority: bool = True):
        self.priority = priority
        self.log_samples: dict[str, BaseSamples] = {}
        self.sample_path = sample_path

    def get_mixed_samples(self, n_per_template: int):
        res = []
        for ps in self.log_samples.values():
            items = ps.items
            if len(items) <= n_per_template:
                res.extend(items)
            else:
                res.extend(random.sample(items, min(len(items), n_per_template)))
        random.shuffle(res)
        return res

    def load(self):
        if self.sample_path.exists():
            saved = orjson.loads(self.sample_path.read_bytes())
            for tid, template_samples in saved.items():
                ps = PrioritySamples() if self.priority else RegularSamples()
                ps.load(template_samples)
                self.log_samples[tid] = ps

    def add_sample(self, key: str, log: str):
        if key not in self.log_samples:
            if self.priority:
                self.log_samples[key] = PrioritySamples(sample_size=50)
            else:
                self.log_samples[key] = RegularSamples(sample_size=50)
        if isinstance(self.log_samples[key], PrioritySamples):
            keyword_count = count_keyword(log)
            self.log_samples[key].push(keyword_count, log)
        else:
            self.log_samples[key].push(0, log)

    def save_state(self):
        self.sample_path.write_bytes(orjson.dumps({tid: ps.dump() for tid, ps in self.log_samples.items()}))
