from collections.abc import Callable


def validate_score(res: dict) -> bool:
    return res.get("score", 0) > 0.2


def validate_numeric_fields(field_res: dict) -> bool:
    return field_res.get("inference", {}).get("trained_noise", 0) > 0.2


panel_bad_score_validation: dict[str, Callable[[dict], bool]] = {
    "log_structure": validate_score,
    "sequence_pattern": validate_score,
    "numeric_fields": validate_numeric_fields,
    "discrete_fields": validate_score,
    "notable_fields": validate_score,
    "log_rate": validate_score,
}
