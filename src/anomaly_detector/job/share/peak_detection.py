import numpy as np
import pandas as pd
from scipy import signal


def _create_wavelet(data, widths=np.arange(1, 12)):
    normalized_data = (data - np.mean(data)) / np.std(data)
    coefficients = signal.cwt(normalized_data, signal.ricker, widths)
    wavelet = np.sum(coefficients, axis=0)
    return wavelet * np.std(data) / np.std(wavelet) + np.mean(data)


def peak_detection(input_df: pd.DataFrame, col: str, prominence=0.2, distance=6):
    df = input_df.copy()
    df["smoothed"] = signal.savgol_filter(df[col], distance, 2)
    df["wavelet"] = _create_wavelet(df["smoothed"])
    df["roc"] = df["wavelet"].diff()
    markers, properties = signal.find_peaks(
        df["wavelet"],
        prominence=prominence,
        distance=distance,  # 1 hour
    )
    return df, markers, properties


def rising_edges_detection(input_df: pd.DataFrame, col: str):
    df, markers, properties = peak_detection(input_df, col)
    rising_edges = []

    for left, right in zip(properties["left_bases"], markers, strict=False):
        max_index = left + np.argmax(df["roc"][left:right])
        rising_edges.append(max_index)
    return rising_edges
