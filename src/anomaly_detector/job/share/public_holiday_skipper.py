import typing
from datetime import datetime, timedelta

import holidays
from pendulum import DateTime, timezone

from config import TIME<PERSON><PERSON><PERSON>

from .public_holiday_timezones import PUBLIC_HOLIDAY_TIMEZONES


class PublicHolidaySkipper:
    def __init__(self, country, subdivision: str | None = None, *, mode: typing.Literal["same_day_of_week", "skip"]):
        self.country = country
        self.subdivision = subdivision
        self.public_holidays = holidays.country_holidays(country, subdiv=subdivision)
        tz_str = self._get_timezone() or TIMEZONE
        self.timezone = timezone(tz_str)
        self.mode = mode

    def skip(self, date):
        return date in self.public_holidays

    def date_ranges(self, start: DateTime, end: DateTime):
        local_start = start.astimezone(self.timezone)
        local_end = end.astimezone(self.timezone)
        start_of_date_range = local_start
        ranges = list(self._daily_ranges(local_start, local_end))
        ranges.reverse()
        skipped_ranges = []
        while ranges:
            r = ranges.pop(0)
            if r[0] in self.public_holidays:
                if self.mode == "same_day_of_week":
                    range_start = r[0] - timedelta(days=7)
                    range_end = r[1] - timedelta(days=7)
                    while range_start >= start_of_date_range:
                        range_start = range_start - timedelta(days=7)
                        range_end = range_end - timedelta(days=7)
                    ranges.append((range_start, range_end))
                elif self.mode == "skip":
                    start_of_date_range = (start_of_date_range - timedelta(days=1)).replace(
                        hour=r[0].hour,
                        minute=r[0].minute,
                        second=r[0].second,
                        microsecond=r[0].microsecond,
                    )
                    ranges.append(
                        (
                            start_of_date_range,
                            (start_of_date_range + timedelta(days=1)).replace(
                                hour=r[1].hour,
                                minute=r[1].minute,
                                second=r[1].second,
                                microsecond=r[1].microsecond,
                            ),
                        ),
                    )
            else:
                skipped_ranges.append(r)
        skipped_ranges = sorted(skipped_ranges, key=lambda r: r[0])
        merged_ranges: list[tuple[DateTime, DateTime]] = []
        for r in skipped_ranges:
            if not merged_ranges:
                merged_ranges.append(r)
            else:
                last_range = merged_ranges[-1]
                if last_range[1] == r[0]:
                    merged_ranges[-1] = (last_range[0], r[1])
                else:
                    merged_ranges.append(r)
        return ((r[0].astimezone(timezone("UTC")), r[1].astimezone(timezone("UTC"))) for r in merged_ranges)

    def _get_timezone(self):
        country = PUBLIC_HOLIDAY_TIMEZONES.get(self.country)
        if not country:
            return None
        if country and "timezone" in country:
            return country["timezone"]
        if "subdivisions" in country:
            if self.subdivision:
                subdivision = country["subdivisions"].get(self.subdivision)
                if subdivision:
                    return subdivision["timezone"]
            first_subdivision = next(iter(country["subdivisions"].values()))
            return first_subdivision["timezone"]
        return None

    def _daily_ranges(self, start: datetime, end: datetime):
        current_date = start
        while current_date < end:
            next_date = (current_date + timedelta(days=1)).replace(hour=0, minute=0)
            next_date = min(next_date, end)
            yield current_date, next_date
            current_date = next_date
