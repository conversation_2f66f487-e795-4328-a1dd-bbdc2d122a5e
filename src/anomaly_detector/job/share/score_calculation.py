def regular_score_function(panel_result: dict):
    return panel_result.get("score", 0)


def numeric_score_function(panel_result: dict):
    scores = []
    for v in panel_result.values():
        scores.append(v.get("inference", {}).get("trained_noise", 0))
    if not scores:
        return 0
    return max(scores)


def discrete_score_function(panel_result: dict):
    scores = []
    for v in panel_result.values():
        scores.append(v.get("score", 0))
    if not scores:
        return 0
    return max(scores)


def regular_overall_score(report):
    return (
        sum(
            [
                regular_score_function(report.get("sequence_pattern", {})) * 5,
                regular_score_function(report.get("log_structure", {})),
                regular_score_function(report.get("log_rate", {})),
                regular_score_function(report.get("notable_fields", {})),
                numeric_score_function(report.get("numeric_fields", {})),
                discrete_score_function(report.get("discrete_fields", {})),
            ],
        )
        / 10
    )


def overall_score(report):
    regular_overall = regular_overall_score(report)
    if "reasoning" not in report:
        return regular_overall
    reasoning = regular_score_function(report.get("reasoning", {}))
    return 0.8 * reasoning + 0.2 * regular_overall
