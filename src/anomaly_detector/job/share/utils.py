import pathlib
import tarfile
from datetime import datetime, timezone
from enum import Enum
from typing import IO

import s3path
import structlog

logger = structlog.get_logger("utils")


class TaskType(str, Enum):
    TRAIN = "TRAIN"
    INFERENCE = "INFERENCE"


def extract_start_end_date(stem: str, fmt: str):
    [_, start, end] = stem.split("_")
    return (
        datetime.strptime(start, fmt).replace(tzinfo=timezone.utc),
        datetime.strptime(end, fmt).replace(tzinfo=timezone.utc),
    )


def append_tar_file(
    original_fileobj: IO,
    target_fileobj: IO,
    append_files: list[pathlib.Path],
    prefix="",
):
    with tarfile.open(fileobj=original_fileobj, mode="r:gz") as tar:
        append_arcnames = [f"{prefix}/{file.name}" if prefix else file.name for file in append_files]
        with tarfile.open(fileobj=target_fileobj, mode="w:gz") as target_tar:
            for member in tar.getmembers():
                if member.path not in append_arcnames:
                    target_tar.addfile(member, tar.extractfile(member))
            for file in append_files:
                target_tar.add(file, arcname=f"{prefix}/{file.name}" if prefix else file.name)


def uncompress_tar_file_and_upload(tar_fileobj: IO, target_dir: s3path.S3Path):
    with tarfile.open(fileobj=tar_fileobj, mode="r:gz") as tar:
        for member in tar.getmembers():
            if member.isfile():
                extracted_file = tar.extractfile(member)
                if extracted_file:
                    target_dir.joinpath(member.path).write_bytes(extracted_file.read())


def batch(arr, batch_size):
    for i in range(0, len(arr), batch_size):
        yield arr[i : i + batch_size]


def rmdir_recursive(path):
    for item in path.iterdir():
        if item.is_dir():
            rmdir_recursive(item)
        else:
            item.unlink(missing_ok=True)
