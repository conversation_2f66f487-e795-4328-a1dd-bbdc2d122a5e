import pathlib
from typing import cast

import or<PERSON><PERSON>
import structlog
from pendulum import DateTime
from pypika import Table
from qdrant_client import QdrantClient

from anomaly_detector.config import HolidaySetting, get_anomaly_detector_settings
from anomaly_detector.job.accounting import AccountingCollector
from anomaly_detector.job.dependency import ServiceDependencies, get_hosted_s3_storage_options
from anomaly_detector.job.fields import FieldTask
from anomaly_detector.job.logbert import DrainParser, LogBERTTask
from anomaly_detector.job.option import LADOptions
from anomaly_detector.job.preprocess import (
    GreptimeLogRetriever,
    LogPreProcessor,
    LogVolumeProfile,
    LokiLogRetriever,
    PreprocessedReader,
)
from anomaly_detector.job.share import TaskType
from anomaly_detector.s3_path_config import S3PathConfig
from config import Settings
from custom_exceptions import RecoverableError
from greptime import SyncGreptimeClient
from llm.baseline_context import BaselineContext, LLMLogGrouping
from llm.contextual_ranking import ContextualRanking
from llm.providers import OpenAIProvider
from prom import PrometheusClient
from s3 import register_aws_s3, register_hosted_s3
from vector_db.vector_search import EMBEDDING_MODELS, VectorSearch, create_embedding_provider


def train(
    option: LADOptions,
    settings: Settings,
    service_dependencies: ServiceDependencies,
    staging_base_path: pathlib.Path,
    metric_name: str,
    last_trained_date: DateTime | None = None,
    is_preprocess: bool | None = False,
):
    log_flow = option.log_flows[0]  # Support multiple log flows
    logger = structlog.get_logger("train", flow_id=log_flow.flow_id)
    anomaly_detector_settings = get_anomaly_detector_settings()
    register_hosted_s3(settings, service_dependencies.s3)
    register_aws_s3(settings)
    path_config = S3PathConfig(
        option.anomaly_detector_id,
        trained_date=option.end,
        aws_s3_base_path=settings.sagemaker.aws_path,
        hosted_s3_base_path=settings.s3.base_path,
        staging_base_path=staging_base_path,
    )
    accounting_collector = AccountingCollector(
        tenant_id=option.tenant_id,
        anomaly_detector_id=option.anomaly_detector_id,
        run_id=option.run_id,
        job_start_time=option.job_start_time,
        path_config=path_config,
        metronome_url=service_dependencies.metronome or "",
    )
    prometheus_client = PrometheusClient(
        mimir_proxy_url=service_dependencies.mimir_proxy,
        mimir_frontend_url=service_dependencies.mimir_frontend,
        mimir_enabled=option.mimir_enabled,
        headers={"X-Scope-OrgID": option.tenant_id},
    )
    holiday_setting = HolidaySetting(
        skip_public_holidays=option.skip_public_holidays,
        country=option.tenant_country,
        subdivision=option.tenant_subdivision,
    )
    last_log_volume_profile = None
    if last_trained_date:
        try:
            last_log_volume_profile = LogVolumeProfile.load_from_dict(
                orjson.loads(path_config.log_profile(last_trained_date).read_bytes()),
                prometheus_client=prometheus_client,
                metronome_url=service_dependencies.metronome or "",
            )
        except Exception as e:
            logger.exception("Failed to load last log volume profile", error=str(e))
    log_volume_profile = LogVolumeProfile(
        tenant_id=option.tenant_id,
        flow_id=log_flow.flow_id,
        metric_name=metric_name,
        profile_date=option.end,
        prometheus_client=prometheus_client,
        metronome_url=service_dependencies.metronome or "",
        holiday_setting=holiday_setting,
    )
    logger.info("start generate log volume profile")
    if not option.skip_log_volume_profile:
        log_volume_profile.generate_profile()
        logger.info("start save log volume profile")
    else:
        logger.info("skip log volume profile")
    if last_log_volume_profile and log_volume_profile.total_bytes == 0:
        log_volume_profile = last_log_volume_profile
    try:
        path_config.trained_log_profile.write_bytes(orjson.dumps(log_volume_profile.to_dict()))
    except Exception as e:
        raise RecoverableError("Failed to save log volume profile") from e

    drain_ignored_keys = option.drain_ignored_keys if option.drain_ignored_keys else []

    # Always create greptime_client for consistency
    greptime_client = SyncGreptimeClient(
        url=service_dependencies.greptime,
        db=option.greptime_log_db or "pythia",
    )

    if option.greptime_table:
        filters: list = []
        table = Table(cast(str, option.greptime_table))
        if option.sks_options and option.sks_options.site_id:
            filters.append(table.site_id == option.sks_options.site_id)
        else:
            filters.extend(
                [
                    table.flow_id == log_flow.flow_id,
                    table.type != "metric",
                ],
            )
        log_retriever = GreptimeLogRetriever(
            greptime_client=greptime_client,
            timestamp_field="ts" if not settings.is_sks_env else "timestamp",
            log_field="line" if not settings.is_sks_env else "message",
            table=option.greptime_table,
            log_volume_profile=log_volume_profile,
            retriever_setting=anomaly_detector_settings.retriever,
            filters=filters,
        )
    else:
        assert service_dependencies.loki_frontend, "Loki frontend URL must be provided for log retrieval"
        log_retriever = LokiLogRetriever(
            tenant_id=option.tenant_id,
            flow_id=log_flow.flow_id,
            log_volume_profile=log_volume_profile,
            loki_url=service_dependencies.loki_frontend,
            retriever_setting=anomaly_detector_settings.retriever,
        )

    drain_parser = DrainParser(
        config_path=anomaly_detector_settings.drain_config_path,
        task_type=TaskType.TRAIN,
        persist_path=path_config.trained_drain_state,
        skip_drain_templates_threshold=anomaly_detector_settings.skip_drain_templates_threshold,
        sample_path=path_config.train_log_samples,
        is_priority_sample=False,
    )
    preprocessor = LogPreProcessor(
        log_retriever=log_retriever,
        path_config=path_config,
        target_hosted_s3_path=path_config.train_parquet_file(option.start, option.end),
        drain_parser=drain_parser,
        task_start=option.start,
        task_date=option.end,
        task_type=TaskType.TRAIN,
        accounting_collector=accounting_collector,
        anomaly_detector_setting=anomaly_detector_settings,
        s3_settings=settings.s3,
        s3_url=service_dependencies.s3,
        drain_ignored_keys=drain_ignored_keys,
        excluded_fields=option.excluded_fields,  # type: ignore FIXME
    )

    hosted_s3_storage_options = get_hosted_s3_storage_options(settings.s3, service_dependencies.s3)

    if not last_trained_date:  # re-generate schema during retrain
        preprocessor.load_model_schema()

    if (
        path_config.train_parquet_file(option.start, option.end).exists()
        and path_config.trained_fields_occurrence.exists()
    ):
        logger.info("skip preprocess")
    else:
        try:
            preprocessor.retrieve_logs_to_arrow_files()
            logger.info("start to save schema")
            preprocessor.current_schema.save_schema(path_config.schema)
            logger.info("start to save histograms")
            preprocessor.current_schema.save_histograms(path_config.trained_histograms)
            preprocessor.convert_to_parquet()
            preprocessor.save_fields_occurrence()
            drain_parser.force_save_state("train")
        except Exception as e:
            raise RecoverableError("Failed to do preprocess") from e
    if is_preprocess:
        accounting_collector.save()
        return

    preprocessed_reader = PreprocessedReader(option, path_config, last_trained_date=last_trained_date)

    openai_base_url = (
        service_dependencies.ollama if anomaly_detector_settings.on_premises_gpu else settings.llm.openai_base_url
    )

    qdrant_client = QdrantClient(url=service_dependencies.qdrant)
    vector_search = VectorSearch(
        greptime_client=greptime_client,
        qdrant_client=qdrant_client,
        embedding_provider=create_embedding_provider(
            settings.llm,
            model=EMBEDDING_MODELS[anomaly_detector_settings.embedding_model()],
        ),
        tenant_id=option.tenant_id,
        flow_id=log_flow.flow_id,
        greptime_logs_enabled=option.greptime_log_db is not None,
    )

    logger.info("start field related tasks")
    contextual_ranking_task = ContextualRanking(
        vector_search=vector_search,
        provider=OpenAIProvider(
            openai_api_key=settings.llm.openai_api_key,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.contextual_ranking_model),
            openai_url=openai_base_url,
        ),
    )
    field_task = FieldTask(
        flow_id=log_flow.flow_id,
        anomaly_detector_id=option.anomaly_detector_id,
        task_type=TaskType.TRAIN,
        date=option.end,
        accounting_collector=accounting_collector,
        preprocessed_reader=preprocessed_reader,
        path_config=path_config,
        anomaly_detector_settings=anomaly_detector_settings,
        parquet_storage_options=hosted_s3_storage_options,
        vector_search=vector_search,
        contextual_ranking=contextual_ranking_task,
    )
    hdbscan_result = field_task.train_hdbscan()
    try:
        field_task.save_hdbscan_result(hdbscan_result)
        field_task.save_drain_histogram()
        field_task.save_trained_histograms(preprocessed_reader.discrete_fields_histograms())
        logger.info("field related tasks done")
        logger.info("start training logbert on sagemaker")
        logger.info("start preparing tokenizer and dataset")
    except Exception as e:
        raise RecoverableError("Failed to save FieldTask results") from e

    if not drain_parser.should_skip:
        bert_task = LogBERTTask(
            tenant_id=option.tenant_id,
            flow_id=log_flow.flow_id,
            anomaly_detector_id=option.anomaly_detector_id,
            parquet_files=preprocessed_reader.parquet_files(),
            task_type=TaskType.TRAIN,
            date=option.end,
            last_trained_date=last_trained_date,
            accounting_collector=accounting_collector,
            contextual_ranking=contextual_ranking_task,
            sagemaker_settings=settings.sagemaker,
            path_config=path_config,
            logbert_settings=anomaly_detector_settings.logbert,
            parquet_storage_options=hosted_s3_storage_options,
            train_source_dir=anomaly_detector_settings.train_source_dir,
            inference_source_dir=anomaly_detector_settings.inference_source_dir,
            loki_url=service_dependencies.loki_frontend,
        )
        bert_task.build_tokenizer(drain_parser)
        if not anomaly_detector_settings.on_premises_gpu:
            try:
                bert_task.upload_tokenizer_to_s3()
            except Exception as e:
                raise RecoverableError("Failed to upload tokenizer to S3") from e
        else:
            (path_config.hosted_gpu_staging_dir / "tokenizer").mkdir(parents=True, exist_ok=True)
            bert_task.tokenizer_builder.save(path_config.hosted_gpu_staging_dir / "tokenizer")

        logger.info("build training dataset")
        ds = bert_task.build_dataset()

        if anomaly_detector_settings.on_premises_gpu:
            bert_task.save_dataset(ds, anomaly_detector_settings.on_premises_gpu)
            bert_task.train_on_hosted_gpu()
            bert_task.upload_hosted_gpu_model()
            bert_task.cleanup_hosted_gpu()
        else:
            try:
                bert_task.save_dataset(ds, anomaly_detector_settings.on_premises_gpu)
            except Exception as e:
                raise RecoverableError("Failed to save dataset to S3") from e
            bert_task.train_on_sagemaker()
            logger.info("training on sagemaker done")

    merged_train_log_samples = preprocessed_reader.train_log_samples()
    merged_train_log_samples.save_state()

    try:
        provider = OpenAIProvider(
            openai_api_key=settings.llm.openai_api_key,
            model=anomaly_detector_settings.llm_model(anomaly_detector_settings.llm.log_grouping_model),
            openai_url=openai_base_url,
        )
        log_grouping = LLMLogGrouping(provider)
        groups = log_grouping.invoke(merged_train_log_samples.get_mixed_samples(100))
        base_context = BaselineContext(vector_search=vector_search, log_groups=groups)
        path_config.trained_baseline_context.write_bytes(orjson.dumps(base_context.dump()))
        base_context.save_to_vectordb(path_config.trained_date_str)
    except Exception:
        logger.error("Failed to do llm grouping", exc_info=True)

    accounting_collector.save()
