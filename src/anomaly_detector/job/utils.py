import re

from anomaly_detector.job.share.score_calculation import overall_score
from anomaly_detector.utils import prom_metric_name
from prom_writer import MetricPoint


def build_metric_points(anomaly_detector_id: str, points_timestamp: int, report, *, full_inference: bool = False):
    inference_label = {"inference": "daily" if full_inference else "10min"}
    metrics = [
        MetricPoint(
            metric_name=prom_metric_name(anomaly_detector_id, "prominent_fields"),
            timestamp=points_timestamp,
            value=report["notable_fields"]["score"],
            labels=inference_label,
        ),
        MetricPoint(
            metric_name=prom_metric_name(anomaly_detector_id, "log_structure"),
            timestamp=points_timestamp,
            value=report["log_structure"]["score"],
            labels=inference_label,
        ),
    ]

    metrics += [
        MetricPoint(
            metric_name=prom_metric_name(anomaly_detector_id, "discrete_values"),
            timestamp=points_timestamp,
            value=result["score"],
            labels={"field_name": field, **inference_label},
        )
        for field, result in report["discrete_fields"].items()
    ]
    # numeric cluster scores
    metrics += [
        MetricPoint(
            metric_name=prom_metric_name(anomaly_detector_id, "numeric_clusters"),
            timestamp=points_timestamp,
            value=result.get("inference", {}).get("trained_noise", 0),
            labels={"field_name": field, **inference_label},
        )
        for field, result in report["numeric_fields"].items()
    ]
    if full_inference:
        metrics.append(
            MetricPoint(
                metric_name=prom_metric_name(anomaly_detector_id, "sequence_pattern"),
                timestamp=points_timestamp,
                value=report["sequence_pattern"]["score"],
                labels=inference_label,
            ),
        )
        metrics.append(
            MetricPoint(
                metric_name=prom_metric_name(anomaly_detector_id, "reasoning_score"),
                timestamp=points_timestamp,
                value=report["reasoning"]["score"],
                labels=inference_label,
            ),
        )
        metrics.append(
            MetricPoint(
                metric_name=prom_metric_name(anomaly_detector_id, "overall_score"),
                timestamp=points_timestamp,
                value=overall_score(report),
                labels=inference_label,
            ),
        )
        if report.get("log_rate", {}).get("score") is not None:
            metrics.append(
                MetricPoint(
                    metric_name=prom_metric_name(anomaly_detector_id, "log_rate"),
                    timestamp=points_timestamp,
                    value=report["log_rate"]["score"],
                    labels=inference_label,
                ),
            )
        for field, hdbscan_result in report["numeric_fields"].items():
            for task_type in ["trained", "inference"]:
                for cluster in hdbscan_result.get(task_type, {}).get("clusters", []):
                    if cluster["label"] != -1:
                        metrics.append(
                            MetricPoint(
                                metric_name=prom_metric_name(anomaly_detector_id, "numeric_clusters:mean"),
                                timestamp=points_timestamp,
                                value=cluster["mean"],
                                labels={
                                    "field_name": field,
                                    "cluster": cluster["label"],
                                    "type": task_type,
                                    **inference_label,
                                },
                            ),
                        )
    return metrics


def flatten_dict(d, parent_key="", sep="."):
    items = {}
    for k, v in d.items():
        new_key = "".join([parent_key, '["', k, '"]'])
        if isinstance(v, dict):
            items.update(flatten_dict(v, new_key, sep=sep))
        else:
            items[new_key] = v
    return items


def flattened_key_to_dot(key: str | int):
    if isinstance(key, str) and key.startswith('["') and key.endswith('"]'):
        return re.sub(r'\["(.+?)"]', r"\1.", key)[:-1]
    return key


def split_flattened_field(key: str, keep_brackets=True):
    if keep_brackets:
        return re.findall(r'(\[".+?"])', key)
    return re.findall(r'\["(.+?)"]', key)


def flatten_nested_field(keys: list[str]):
    return "".join([f'["{k}"]' for k in keys])


def add_suffix_to_field_name(field_name: str, suffix: str):
    """:param field_name: '["a"]["ipaddress"]'
    :param suffix: 'country'
    return: '["a"]["ipaddress@country"]'
    """
    fields = split_flattened_field(field_name, keep_brackets=False)
    return flatten_nested_field(fields[:-1] + [f"{fields[-1]}@{suffix}"])


def _migrate_field_name_flatten(key: str):
    if "[" in key:
        return key
    if key.startswith("grpc."):
        return f'["{key}"]'
    keys = key.split(".")
    return flatten_nested_field(keys)


def flatten_dict_keys(x: dict):
    return {_migrate_field_name_flatten(k): v for k, v in x.items()}
