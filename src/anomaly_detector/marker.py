from dataclasses import dataclass, field


@dataclass
class Marker:
    name: str


@dataclass
class SubMarker:
    name: str


@dataclass
class SQLMarker(Marker):
    sql: str
    timestamp_field: str = "ts"
    timestamp_unit: str = "ns"
    label_fields: list[str] = field(default_factory=list)
    omit_labels: list[str] = field(default_factory=list)
    sub_markers: list[SubMarker] = field(default_factory=list)


@dataclass
class PromMarker(Marker):
    promql: str
    prophecy_id: str
    omit_labels: list[str] = field(default_factory=list)


def metric_marker_name(name: str, labels: dict[str, str]):
    label_pairs_str = ",".join([f'{k}="{v}"' for k, v in labels.items()])
    label_str = f"{{{label_pairs_str}}}" if label_pairs_str else ""
    return f"{name}{label_str}"
