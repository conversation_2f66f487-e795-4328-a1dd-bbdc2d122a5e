import asyncio
from collections import defaultdict
from typing import Annotated, cast

import httpx
import or<PERSON><PERSON>
import pendulum
from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Response, status
from pypika import PostgreSQLQuery as Q, Table
from starlette.concurrency import run_in_threadpool

from config import Settings, get_settings
from custom_exceptions import TableNotFoundError
from dependencies import db_conn, db_txn, get_async_greptime, get_async_greptime_promql_client, get_sks_async_greptime
from greptime import AsyncGreptimeClient
from job.model import AnomalyDetectorConfig, JobStatus
from job.service import job_exists

from . import ad_service
from .dependencies import get_inference_path_config
from .s3_path_config import S3PathConfig
from .schema import (
    AnomalyCorrelationsQuery,
    AnomalyDetectorDefinition,
    MarkerResponse,
    MetricResponse,
    MetricsQuery,
    ReportScoresQuery,
    RetrainRequest,
)
from .sks import markers as sks_markers
from .utils import column_tuple_to_str, measurement_name, metric_str_to_column_tuple

router = APIRouter(prefix="/tenants/{tenant_id}/anomaly-detectors/{ad_id}")


@router.put("")
async def define(
    conn: Annotated[Connection, Depends(db_txn)],
    tenant_id: str,
    ad_id: str,
    request: AnomalyDetectorDefinition,
):
    if not await job_exists(conn, ad_id):
        await ad_service.create(conn, tenant_id, ad_id, request)
    else:
        await ad_service.update(conn, tenant_id, ad_id, request)
    return Response(status_code=status.HTTP_200_OK)


@router.delete("")
async def delete(
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    tenant_id: str,
    ad_id: str,
):
    await ad_service.delete(conn, greptime_client, tenant_id, ad_id)
    return Response(status_code=status.HTTP_200_OK)


@router.get("/status")
async def get_status(
    conn: Annotated[Connection, Depends(db_conn)],
    tenant_id: str,
    ad_id: str,
):
    train_job = await ad_service.get_train_job(conn, tenant_id, ad_id)
    if not train_job:
        return Response(status_code=404)
    config = AnomalyDetectorConfig(**train_job["config"])

    if config.trained_date:
        return {
            "status": "active",
            "skip-public-holidays": config.skip_public_holidays,
            "initial-trained-date": config.initial_trained_date and config.initial_trained_date.to_iso8601_string(),
            "last-trained-date": config.trained_date and config.trained_date.to_iso8601_string(),
            "retrain-date": config.scheduled_train_date and config.scheduled_train_date.to_iso8601_string(),
        }
    if train_job["status"] == JobStatus.failed:
        return {
            "status": "failed",
            "skip-public-holidays": config.skip_public_holidays,
        }
    return {
        "status": "waiting",
        "scheduled-train-date": config.scheduled_train_date and config.scheduled_train_date.to_iso8601_string(),
        "skip-public-holidays": config.skip_public_holidays,
    }


@router.get("/advisory")
async def get_advisory(
    path_config: Annotated[S3PathConfig, Depends(get_inference_path_config)],
    settings: Annotated[Settings, Depends(get_settings)],
    lang: str = "en",
):
    file = path_config.inference_advisory_report if lang == "en" else path_config.inference_advisory_report_lang(lang)
    try:
        content = await run_in_threadpool(file.read_bytes)
        return {"advisory": orjson.loads(content)}
    except Exception:
        if settings.is_sks_env:
            return {}
        return Response(status_code=404)


@router.get("/report")
async def get_report(
    path_config: Annotated[S3PathConfig, Depends(get_inference_path_config)],
    settings: Annotated[Settings, Depends(get_settings)],
):
    try:
        content = await run_in_threadpool(path_config.inference_report.read_bytes)
        report = orjson.loads(content)
    except Exception:
        if settings.is_sks_env:
            return {}
        return Response(status_code=404)
    if not path_config.full_inference:
        return {"report": report}
    try:
        content = await run_in_threadpool(path_config.inference_logs_report.read_bytes)
        log_report = orjson.loads(content)
    except Exception:
        log_report = None
    return {"report": report, "log-sequences": log_report}


@router.get("/anomalies/correlations")
async def get_correlation_timestamps(
    ad_id: str,
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    query: Annotated[AnomalyCorrelationsQuery, Depends()],
):
    marker_name = column_tuple_to_str((query.marker_type, query.name))
    table = Table(measurement_name(ad_id))
    q = (
        Q.from_(table)
        .select("*")
        .where(
            ((table.marker_1 == marker_name) | (table.marker_2 == marker_name))
            & (table.ts >= int(query.start.timestamp() * 1e9))
            & (table.ts <= int(query.end.timestamp() * 1e9)),
        )
    )
    try:
        result = await greptime_client.execute(q)
    except TableNotFoundError:
        return {"correlations": []}
    anomalies = defaultdict(list)
    for record in result:
        ts = record["ts"]
        marker = record["marker_1"] if record["marker_1"] != marker_name else record["marker_2"]
        marker_type, name = metric_str_to_column_tuple(marker)
        corr = {"marker_type": marker_type, "name": name, "correlation_score": record["correlation_score"]}
        anomalies[ts].append(corr)
    response = []
    for ts, items in anomalies.items():
        response.append(
            {
                "timestamp": pendulum.from_timestamp(ts / 1e9).to_iso8601_string(),
                "marker_type": query.marker_type,
                "name": query.name,
                "correlations": items,
            },
        )
    return {"correlations": sorted(response, key=lambda x: x["timestamp"])}


@router.get("/metrics")
async def query_scores(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    ad_id: str,
    query: Annotated[ReportScoresQuery, Depends()],
):
    return await ad_service.query_scores(greptime_client, ad_id, query)


@router.get("/scores/overall")
async def get_overall_scores(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    ad_id: str,
    query: Annotated[MetricsQuery, Depends()],
):
    return await ad_service.get_overall_scores(greptime_client, ad_id, query)


@router.get(
    "/markers",
    summary="Get raw markers and their latest values",
    response_model=dict[str, dict[str, MarkerResponse]],
)
async def get_markers(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_sks_async_greptime)],
    httpx_client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    _ad_id: str,
    tags: str | None = None,
    markers: str | None = None,
    lang: str = "zh-cn",
):
    if settings.is_sks_env:
        filter_tags = [f.strip() for f in tags.split(",")] if tags else None
        filter_markers = [f.strip() for f in markers.split(",")] if markers else None
        return await sks_markers.list_markers(
            tenant_id,
            greptime_client,
            httpx_client,
            filter_tags,
            filter_markers,
            lang=lang,
        )
    return {}


@router.get("/qoe_scores")
async def get_qoe_markers(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_sks_async_greptime)],
    httpx_client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    _ad_id: str,
    datetime: str | None = None,
    lang: str = "zh-cn",
):
    try:
        dt = cast(pendulum.DateTime, pendulum.parse(datetime)) if datetime else None
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid datetime format") from e
    if settings.is_sks_env:
        return await sks_markers.qoe_scores(tenant_id, greptime_client, httpx_client, datetime=dt, lang=lang)
    return {}


@router.get("/markers/qoe/query")
async def query_qoe_marker(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_sks_async_greptime)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    _ad_id: str,
    query: Annotated[MetricsQuery, Depends()],
) -> list[MetricResponse]:
    if settings.is_sks_env:
        return await sks_markers.query_qoe_marker(tenant_id, query, greptime_client)
    return []


@router.get("/markers/{marker_name}/query")
async def query_marker(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_sks_async_greptime)],
    httpx_client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    __ad_id: str,
    marker_name: str,
    query: Annotated[MetricsQuery, Depends()],
) -> list[MetricResponse]:
    if settings.is_sks_env:
        return await sks_markers.query_marker(tenant_id, marker_name, query, greptime_client, httpx_client)
    return []


@router.get("/markers/query")
async def query_markers(
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_sks_async_greptime)],
    httpx_client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    _ad_id: str,
    markers: str,
    query: Annotated[MetricsQuery, Depends()],
):
    marker_names = [m.strip() for m in markers.split(",")]
    if settings.is_sks_env:
        results = await asyncio.gather(
            *[
                sks_markers.query_qoe_marker(tenant_id, query, greptime_client)
                if marker_name == "qoe"
                else sks_markers.query_marker(tenant_id, marker_name, query, greptime_client, httpx_client)
                for marker_name in marker_names
            ],
        )
        return dict(zip(marker_names, results, strict=False))
    return {}


@router.post("/retrain")
async def retrain(
    conn: Annotated[Connection, Depends(db_txn)],
    tenant_id: str,
    ad_id: str,
    request: RetrainRequest,
):
    await ad_service.retrain(conn, tenant_id, ad_id, request.retrain, request.skip_public_holiday)
    return Response(status_code=status.HTTP_200_OK)


@router.delete("/retrain")
async def cancel_retrain(
    conn: Annotated[Connection, Depends(db_txn)],
    tenant_id: str,
    ad_id: str,
):
    train_job = await ad_service.get_train_job(conn, tenant_id, ad_id)
    if not train_job:
        return Response(status_code=status.HTTP_404_NOT_FOUND)

    config = AnomalyDetectorConfig(**train_job["config"])
    if not (config.trained_date and config.scheduled_train_date):
        return Response(status_code=400)

    if train_job["status"] != JobStatus.pending:
        return Response(status_code=409)

    await ad_service.cancel_retrain(conn, train_job["id"])

    return Response(status_code=status.HTTP_200_OK)
