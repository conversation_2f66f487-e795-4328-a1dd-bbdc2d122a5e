import pathlib
from datetime import datetime, timedelta

import s3path
from pendulum import DateTime

from constants import DATE_FORMAT, DATETIME_FORMAT


class S3PathConfig:
    def __init__(
        self,
        anomaly_detector_id: str,
        *,
        hosted_s3_base_path: s3path.S3Path | None = None,
        staging_base_path: pathlib.Path | None = None,
        aws_s3_base_path: s3path.S3Path | None = None,
        trained_date: DateTime | None = None,
        inference_date: DateTime | None = None,
        full_inference: bool = True,
    ):
        self.anomaly_detector_id = anomaly_detector_id
        self.trained_date = trained_date
        self.inference_date = inference_date
        self.full_inference = full_inference
        self.staging_base_path = staging_base_path
        self.base_path = hosted_s3_base_path / "log-anomaly" if hosted_s3_base_path else s3path.S3Path("./log-anomaly")
        self.aws_s3_base = aws_s3_base_path

    @property
    def trained_date_str(self):
        if self.trained_date is None:
            raise ValueError("trained_date is None")
        return self.trained_date.strftime(DATE_FORMAT)

    @property
    def inference_date_str(self):
        if self.inference_date is None:
            raise ValueError("inference_date is None")
        if self.full_inference:
            return self.inference_date.strftime(DATE_FORMAT)
        return self.inference_date.strftime(DATETIME_FORMAT)

    @property
    def anomaly_detector_base_path(self):
        return self.base_path / self.anomaly_detector_id

    @property
    def train_base_without_date(self):
        return self.base_path / self.anomaly_detector_id / "train"

    @property
    def train_base(self):
        return self.train_base_without_date / self.trained_date_str

    @property
    def staging_base(self):
        if self.staging_base_path is None:
            raise ValueError("staging_base_path is None")
        return self.staging_base_path / self.anomaly_detector_id / "staging"

    @property
    def inference_base(self):
        return self.base_path / self.anomaly_detector_id / "inference" / self.inference_date_str

    @property
    def hosted_gpu_model_dir(self):
        return self.train_base / "model"

    @property
    def hosted_gpu_staging_dir(self):
        return self.staging_base / "logbert"

    @property
    def aws_s3_train_base(self):
        if self.aws_s3_base is None:
            raise ValueError("aws_s3_base is None")
        return self.aws_s3_base / self.anomaly_detector_id / self.trained_date_str

    @property
    def aws_s3_inference_base(self):
        if self.aws_s3_base is None:
            raise ValueError("aws_s3_base is None")
        return self.aws_s3_base / self.anomaly_detector_id / self.inference_date_str

    @property
    def schema(self):
        return self.base_path / self.anomaly_detector_id / "schema.json"

    @property
    def inference_schema(self):
        return self.inference_base / "schema.json"

    @property
    def inference_histograms(self):
        return self.inference_base / "histograms" / "histograms.json"

    @property
    def inference_field_hist(self):
        return self.inference_base / "histograms" / "field_hist.json"

    @property
    def parquet_base(self):
        return self.base_path / self.anomaly_detector_id / "parquet"

    @property
    def accounting_base(self):
        return self.base_path / self.anomaly_detector_id / "accounting"

    def accounting_metrics(self, run_id: str):
        return self.accounting_base / f"{run_id}.json"

    @staticmethod
    def _parquet_filename(start: DateTime, end: DateTime):
        return f"{start.strftime(DATETIME_FORMAT)}_{end.strftime(DATETIME_FORMAT)}.parquet"

    @property
    def inference_10m_parquet_base(self):
        return self.parquet_base / "inference" / "10m"

    @property
    def inference_daily_parquet_base(self):
        return self.parquet_base / "inference" / "daily"

    @property
    def train_parquet_base(self):
        return self.parquet_base / "train"

    def inference_10m_parquet_file(self, start: DateTime, end: DateTime):
        return self.inference_10m_parquet_base / f"{self._parquet_filename(start, end)}"

    def inference_daily_parquet_file(self, start: DateTime, end: DateTime):
        return self.inference_daily_parquet_base / f"{self._parquet_filename(start, end)}"

    def train_parquet_file(self, start: DateTime, end: DateTime):
        return self.train_parquet_base / f"{self._parquet_filename(start, end)}.parquet"

    @property
    def trained_log_profile(self):
        return self.train_base / "log_profile" / "profile.json"

    def log_profile(self, date: DateTime):
        return self.train_base_without_date / date.strftime(DATE_FORMAT) / "log_profile" / "profile.json"

    @property
    def trained_drain_state(self):
        # drain state will be updated when re-train
        return self.train_base_without_date / "drain" / "state.json"

    @property
    def trained_baseline_context(self):
        return self.train_base / "baseline_context.json"

    @property
    def trained_drain_histogram(self):
        return self.train_base / "histograms" / "drain_histogram.json"

    @property
    def trained_histograms(self):
        return self.train_base / "histograms" / "histograms.json"

    @property
    def trained_fields_occurrence(self):
        return self.train_base / "histograms" / "fields_occurrence.json"

    @property
    def trained_hdbscan(self):
        return self.train_base / "hdbscan" / "hdbscan.json"

    @property
    def train_log_samples(self):
        return self.train_base / "log_samples.json"

    @property
    def tokenizer_dir(self):
        return self.train_base_without_date / "tokenizer"

    @property
    def inference_keyword_problematic_log_groups(self):
        return self.inference_base / "llm" / "keyword_problematic_log_groups.json"

    @property
    def inference_drain_problematic_log_groups(self):
        return self.inference_base / "llm" / "drain_problematic_log_groups.json"

    @property
    def inference_log_sequences_problematic_log_groups(self):
        return self.inference_base / "llm" / "log_sequences_problematic_log_groups.json"

    @property
    def inference_report(self):
        return self.inference_base / "report" / "report.json"

    @property
    def inference_anomaly_sequences(self):
        return self.inference_base / "report" / "anomaly_sequences.csv"

    @property
    def inference_logs_report(self):
        return self.inference_base / "report" / "logs_report.json"

    @property
    def inference_advisory_report(self):
        return self.inference_base / "report" / "advisory_report.json"

    @property
    def inference_metrics_report(self):
        return self.inference_base / "report" / "metrics_report.json"

    def inference_past_advisory_report(self, days=7):
        if self.inference_date is None:
            raise ValueError("inference_date is None")
        today = self.inference_date
        past_start_date = today - timedelta(days=days)

        def past_day_advisory_path(date: datetime):
            return (
                self.base_path
                / self.anomaly_detector_id
                / "inference"
                / date.strftime(DATE_FORMAT)
                / "report"
                / "advisory_report.json"
            )

        return [past_day_advisory_path(past_start_date + timedelta(days=day)) for day in range(days)]

    @property
    def inference_log_rate_report(self):
        return self.inference_base / "report" / "log_rate_report.json"

    def inference_advisory_report_lang(self, lang: str):
        return self.inference_base / "report" / f"advisory_report_{lang.lower()}.json"

    @property
    def inference_log_samples(self):
        return self.inference_base / "report" / "log_samples.json"

    def staging_arrow_path(self, hash_key: str):
        return self.staging_base / "arrow" / f"{hash_key}.arrow"

    @property
    def aws_s3_trained_output(self):
        return self.aws_s3_train_base / "output"

    @property
    def aws_s3_trained_tokenizer(self):
        return self.aws_s3_train_base / "tokenizer"

    @property
    def aws_s3_pretrained_model(self):
        return self.aws_s3_train_base / "pretrained_model"

    @property
    def aws_s3_trained_dataset(self):
        return self.aws_s3_train_base / "dataset"

    @property
    def aws_s3_trained_checkpoint(self):
        return self.aws_s3_train_base / "checkpoint"

    @property
    def aws_s3_inference_dataset(self):
        return self.aws_s3_inference_base / "inference_dataset.jsonl"

    @property
    def aws_s3_inference_output(self):
        return self.aws_s3_inference_base / "inference_output"
