from datetime import datetime

from pydantic import BaseModel, ConfigDict, Field
from pydantic_extra_types.pendulum_dt import DateTime

from job.model import AnomalyDetectorLog<PERSON>low, AnomalyDetectorMetricFlow
from utils import to_kebab


class AnomalyDetectorDefinition(BaseModel):
    model_config = ConfigDict(alias_generator=to_kebab)
    created: DateTime
    log_flows: list[AnomalyDetectorLogFlow] = Field(default_factory=list)
    metric_flows: list[AnomalyDetectorMetricFlow] = Field(default_factory=list)
    skip_public_holidays: bool = False


class RetrainRequest(BaseModel):
    retrain: DateTime
    skip_public_holidays: bool = False


class AnomalyDetectorStatus(BaseModel):
    status: str = Field(..., description="active, failed, or waiting")
    skip_public_holidays: bool = False
    initial_trained_date: datetime | None = None
    last_trained_date: datetime | None = None
    retrain_date: datetime | None = None
    scheduled_train_date: datetime | None = None


class MetricsQuery(BaseModel):
    start: DateTime
    end: DateTime


class ReportScoresQuery(MetricsQuery):
    full: bool = False


class AnomalyCorrelationsQuery(BaseModel):
    start: DateTime
    end: DateTime
    marker_type: str
    name: str | None = None


class MetricResponse(BaseModel):
    labels: dict
    values: list[tuple[int, float | None]]


# {
#     "connection": {
#         "ap_node_connection_success_duration_ns": {
#             "results": [
#                 {
#                     "value": 304849152.0,
#                     "labels": {
#                         "reason": "SUCCESS",
#                         "webauth": "false",
#                         "auth_mode": "AUTH_TYPE_PSK",
#                         "ap_mac": "00:16:16:42:43:f2",
#                         "bssid": "02:16:16:42:43:f4"
#                     }
#                 },
#             ]
#             "name": "ap_node_connection_success_duration_ns"
#         }
#     }
# }


class InstantMarkerValue(BaseModel):
    value: float | None = Field(description="The lastest value ")
    labels: dict[str, str] = Field(description="The labels of the marker")


class MarkerResponse(BaseModel):
    name: str = Field(description="The name of the marker")
    results: list[InstantMarkerValue] = Field(description="The value of markers")
    description: str = Field(description="The description of the marker")
    qoes: list[str] | None = Field(description="Related QoEs", default=None)
