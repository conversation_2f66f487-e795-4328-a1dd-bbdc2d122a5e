import asyncio

import structlog
from asyncpg import Connection
from fastapi import HTTPException, status
from pendulum import DateTime
from pypika import Criterion, Order, PostgreSQLQuery as Q, Table

from anomaly_detector.schema import AnomalyDetectorDefinition, MetricsQuery, ReportScoresQuery
from anomaly_detector.utils import prom_metric_name
from config import TIMEZONE
from greptime import AsyncGreptimeClient, greptime_sql_response_metrics
from job.model import AnomalyDetectorConfig, JobStatus, JobType, table as jobs
from prophecy import prophecy_service

logger = structlog.get_logger("anomaly_detector:service")


async def get_train_job(conn: Connection, tenant_id: str, ad_id: str):
    q = str(
        Q.from_(jobs)
        .select("id", "status", "config", "next_run", "created")
        .where(
            (jobs.type == JobType.log_anomaly)
            & (jobs.tenant_id == tenant_id)
            & (jobs.job_source_id == ad_id)
            & jobs.config.get_json_value("inference-interval-minutes").isnull(),
        ),
    )
    records = await conn.fetch(q)
    return records[0] if records else None


async def create(
    conn: Connection,
    tenant_id: str,
    ad_id: str,
    create_request: AnomalyDetectorDefinition,
):
    start_local_time = create_request.created.in_tz(TIMEZONE).start_of("day").in_tz("UTC")
    config = AnomalyDetectorConfig(
        log_flows=create_request.log_flows,
        metric_flows=create_request.metric_flows,
        skip_public_holidays=create_request.skip_public_holidays,
        scheduled_train_date=start_local_time.add(days=8),
    )
    await conn.execute(
        """
            INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
        tenant_id,
        ad_id,
        create_request.created.naive(),
        start_local_time.add(days=2).naive(),
        JobType.log_anomaly,
        40,
        config.kebab_dump(),
    )


async def update(
    conn: Connection,
    tenant_id: str,
    ad_id: str,
    update_request: AnomalyDetectorDefinition,
):
    config = AnomalyDetectorConfig(
        log_flows=update_request.log_flows,
        metric_flows=update_request.metric_flows,
        skip_public_holidays=update_request.skip_public_holidays,
    )
    await conn.execute(
        """
        UPDATE jobs SET config = config || $4
        WHERE tenant_id = $1 AND job_source_id = $2 AND type = $3
        """,
        tenant_id,
        ad_id,
        JobType.log_anomaly,
        config.kebab_dump(),
    )


fields_tables_map = {
    "sequence-pattern": "sequence_pattern",
    "discrete-values": "discrete_values",
    "numeric-clusters": "numeric_clusters",
    "log-rate": "log_rate",
    "numeric-clusters-value": "numeric_clusters:mean",
    "prominent-fields": "prominent_fields",
    "log-structure": "log_structure",
}


async def delete(
    conn: Connection,
    greptime_client: AsyncGreptimeClient,
    tenant_id: str,
    ad_id: str,
):
    await conn.execute(
        """
DELETE FROM jobs WHERE type = $1 AND tenant_id = $2 AND job_source_id = $3 AND  "config"->'inference-interval-minutes' IS NOT NULL """,
        JobType.log_anomaly,
        tenant_id,
        ad_id,
    )

    await conn.execute(
        """
    UPDATE jobs SET
        config = config || $4,
        status = $5,
        next_run = NOW()
    WHERE type = $1 AND tenant_id = $2 AND job_source_id = $3 AND config->'inference-interval-minutes' IS NULL
    """,
        JobType.log_anomaly,
        tenant_id,
        ad_id,
        {"deleted": True},
        JobStatus.pending,
    )

    cleanup_jobs = []
    greptime_tables = list(fields_tables_map.values())
    greptime_tables.append("overall_score")
    cleanup_jobs.append(
        greptime_client.batch_execute(
            Q.drop_table(prom_metric_name(ad_id, panel)).if_exists() for panel in greptime_tables
        ),
    )
    await asyncio.gather(*cleanup_jobs)


async def retrain(
    conn: Connection,
    tenant_id: str,
    ad_id: str,
    retrain_date: DateTime,
    skip_public_holiday: bool,
):
    q = str(
        Q.from_(jobs)
        .select(jobs.id, jobs.config)
        .where(
            (jobs.type == JobType.log_anomaly)
            & (jobs.tenant_id == tenant_id)
            & (jobs.job_source_id == ad_id)
            & (jobs.status == JobStatus.completed),
        ),
    )
    anomaly_jobs = await conn.fetch(q)  # https://github.com/MagicStack/asyncpg/issues/580#issuecomment-1199593855
    if not anomaly_jobs:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    job = anomaly_jobs[0]

    prophecy_id = job["config"].get("prophecy-id")
    next_run = retrain_date.in_tz(TIMEZONE).start_of("day").add(days=1).in_tz("UTC")

    if prophecy_id and not await prophecy_service.retrain(conn, tenant_id, prophecy_id, next_run):
        raise HTTPException(status_code=status.HTTP_409_CONFLICT)

    await conn.execute(
        "UPDATE jobs SET status = $1, config = config || $2, next_run = $3 WHERE id = $4;",
        JobStatus.pending,
        {
            "trained": False,
            "scheduled-train-date": next_run.to_iso8601_string(),
            "skip-public-holidays": skip_public_holiday,
        },
        next_run.naive(),
        job["id"],
    )


async def cancel_retrain(conn: Connection, job_id: str):
    q = str(
        Q.from_(jobs).select(jobs.tenant_id, jobs.config).where(jobs.id == job_id),
    )
    job_records = await conn.fetch(q)
    if not job_records:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    if job_records:
        job = job_records[0]
        prophecy_id = job["config"].get("prophecy-id")
        if prophecy_id and not await prophecy_service.cancel_retrain(conn, job["tenant_id"], prophecy_id):
            raise HTTPException(status_code=status.HTTP_409_CONFLICT)

    await conn.execute(
        "UPDATE jobs SET status = $1, next_run = NULL, config = config || $2 WHERE id = $3",
        JobStatus.completed,
        {
            "trained": True,
            "scheduled-train-date": None,
        },
        job_id,
    )


def _build_score_query(panel: str, ad_id: str, start: DateTime, end: DateTime, full: bool | None = None):
    table = Table(prom_metric_name(ad_id, panel))
    conditions = [
        table.greptime_timestamp >= start.int_timestamp * 1000,
        table.greptime_timestamp <= end.int_timestamp * 1000,
    ]
    if full is not None:
        conditions.append(table.inference == ("daily" if full else "10min"))
    return (
        Q.from_(table).select("*").where(Criterion.all(conditions)).orderby(table.greptime_timestamp, order=Order.asc)
    )


async def _try_query_scores(greptime_client: AsyncGreptimeClient, q: str):
    try:
        return await greptime_client.execute(q)
    except Exception:
        logger.exception("Error querying scores")
        return None


async def query_scores(greptime_client: AsyncGreptimeClient, ad_id: str, q: ReportScoresQuery):
    panel_names = list(fields_tables_map.values())
    results = await asyncio.gather(
        *(
            _try_query_scores(greptime_client, _build_score_query(panel, ad_id, q.start, q.end, q.full))
            for panel in panel_names
        ),
    )
    exclude_keys = ["greptime_timestamp", "inference", "greptime_value"]
    panel_results = dict(
        zip(
            panel_names,
            (greptime_sql_response_metrics(r, exclude_fields=exclude_keys) for r in results),
            strict=False,
        ),
    )
    return {
        field: panel_results[panel_name]
        for field, panel_name in fields_tables_map.items()
        if panel_name in panel_results
    }


async def get_overall_scores(
    greptime_client: AsyncGreptimeClient,
    ad_id: str,
    q: MetricsQuery,
):
    panel = "overall_score"
    query = _build_score_query(panel, ad_id, q.start, q.end)
    result = await _try_query_scores(greptime_client, query)

    if result is None:
        return {"overall-scores": []}

    r = greptime_sql_response_metrics(result, exclude_fields=["greptime_timestamp", "inference", "greptime_value"])

    return {"overall-scores": r}
