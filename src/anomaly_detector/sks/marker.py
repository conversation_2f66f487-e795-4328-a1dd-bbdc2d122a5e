from dataclasses import dataclass, field
from enum import Str<PERSON>num

from fastapi import HTT<PERSON>Exception
from starlette import status

from anomaly_detector.marker import Marker, Prom<PERSON><PERSON><PERSON>, SQLMarker
from utils import prom_metric_selector


class MarkerType(StrEnum):
    COUNTER = "counter"
    GAUGE = "gauge"
    INDIVIDUAL_HISTOGRAM = "individual_histogram"


class Tag(StrEnum):
    QOE = "qoe"
    CONNECTION = "connection"
    THROUGHPUT = "throughput"
    LINK = "link"
    COVERAGE = "coverage"
    AP_HEALTH = "ap_health"
    OTHER = "other"


@dataclass
class SksMarker(Marker):
    tag: Tag
    type: MarkerType
    qoe_score: bool = False
    qoes: list[str] = field(default_factory=list)
    hide: bool = False


@dataclass
class SksSQLMarker(SQLMarker, SksMarker):
    pass


@dataclass
class SksPromMarker(PromMarker, SksMarker):
    def to_prom_marker(self, extra_filter: dict | None = None) -> PromMarker:
        promql = get_prom_marker_promql(self, "10m", extra_filter)
        return PromMarker(promql=promql, prophecy_id=self.name, name=self.name)


def get_prom_marker_promql(marker: SksPromMarker, step: str, labels: dict | None = None):
    metric_selector = prom_metric_selector(marker.name, labels)
    if marker.type == MarkerType.GAUGE:
        promql = f"avg_over_time({metric_selector}[{step}])"
    elif marker.type in (MarkerType.COUNTER, MarkerType.INDIVIDUAL_HISTOGRAM):
        if marker.omit_labels:
            promql = f"increase({metric_selector}[{step}])"
        else:
            promql = f"sum(increase({metric_selector}[{step}]))"
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported marker type")
    if marker.omit_labels:
        promql = f"avg without ({', '.join(marker.omit_labels)}) ({promql})"
    return promql


def get_prom_marker_sql(
    marker: SksPromMarker,
    step_seconds: int,
    labels: list[str] | None = None,
):
    select_labels = ",".join(labels) if labels else ""
    if marker.type == MarkerType.GAUGE:
        agg_value = f"AVG(greptime_value) RANGE '{step_seconds}s' FILL PREV AS greptime_value"
    elif marker.type == MarkerType.COUNTER:
        agg_value = f"(MAX(greptime_value) - MIN(greptime_value)) RANGE '{step_seconds}s' FILL PREV AS greptime_value"
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported marker type")

    align_by_sql = (
        f"ALIGN '{step_seconds}s' BY ({select_labels}) FILL PREV"
        if select_labels
        else f"ALIGN '{step_seconds}s' FILL PREV"
    )
    return f"""
SELECT
  greptime_timestamp,
  {select_labels + ", " if select_labels else ""}
  {agg_value}
FROM {marker.name}
WHERE $__timeFilter(greptime_timestamp) $__extraFilter
{align_by_sql}
"""
