from dataclasses import dataclass


@dataclass
class MarkerDescriptions:
    """Marker description data class containing multilingual names and descriptions.

    Attributes:
        name: Unique identifier for the marker
        name_lang: Dictionary of localized names (key: language code, value: name)
        descriptions: Dictionary of localized descriptions (key: language code, value: description)

    """

    name: str
    name_lang: dict[str, str]
    descriptions: dict[str, str]


marker_descriptions = [
    # QoE Composite Scores
    MarkerDescriptions(
        name="connection_qoe",
        name_lang={
            "zh-cn": "连接质量评分",
            "en": "Connection QoE Score",
        },
        descriptions={
            "zh-cn": "连接质量综合评分",
            "en": "Overall connection quality score",
        },
    ),
    MarkerDescriptions(
        name="coverage_qoe",
        name_lang={
            "zh-cn": "覆盖质量评分",
            "en": "Coverage QoE Score",
        },
        descriptions={
            "zh-cn": "覆盖质量综合评分",
            "en": "Overall coverage quality score",
        },
    ),
    MarkerDescriptions(
        name="throughput_qoe",
        name_lang={
            "zh-cn": "传输效能评分",
            "en": "Throughput QoE Score",
        },
        descriptions={
            "zh-cn": "传输效能综合评分",
            "en": "Overall throughput quality score",
        },
    ),
    # Connection Quality Sub-metrics
    MarkerDescriptions(
        name="connection_successful_connect_rate",
        name_lang={
            "zh-cn": "连接成功率",
            "en": "Connection Success Rate",
        },
        descriptions={
            "zh-cn": "客户端连接成功的比例",
            "en": "Rate of successful client connections",
        },
    ),
    MarkerDescriptions(
        name="connection_successful_state_change_rate",
        name_lang={
            "zh-cn": "状态变化成功率",
            "en": "State Change Success Rate",
        },
        descriptions={
            "zh-cn": "连接状态变化成功率",
            "en": "Success rate of connection state changes",
        },
    ),
    MarkerDescriptions(
        name="connection_successful_duration_score",
        name_lang={
            "zh-cn": "连接时长评分",
            "en": "Connection Duration Score",
        },
        descriptions={
            "zh-cn": "连接建立时长评分",
            "en": "Connection establishment time score",
        },
    ),
    # Coverage Quality Sub-metrics
    MarkerDescriptions(
        name="coverage_interference_score",
        name_lang={
            "zh-cn": "干扰评估评分",
            "en": "Interference Assessment Score",
        },
        descriptions={
            "zh-cn": "信道干扰水平评分",
            "en": "Channel interference level score",
        },
    ),
    MarkerDescriptions(
        name="coverage_rssi_score",
        name_lang={
            "zh-cn": "信号质量",
            "en": "RSSI Score",
        },
        descriptions={
            "zh-cn": "信号质量",
            "en": "RSSI Score",
        },
    ),
    MarkerDescriptions(
        name="coverage_link_score",
        name_lang={
            "zh-cn": "链路质量评分",
            "en": "Link Quality Score",
        },
        descriptions={
            "zh-cn": "链路质量评分",
            "en": "Link quality score",
        },
    ),
    # Throughput Quality Sub-metrics
    MarkerDescriptions(
        name="throughput_efficiency",
        name_lang={
            "zh-cn": "终端传输效能",
            "en": "Throughput Efficiency",
        },
        descriptions={
            "zh-cn": "终端传输效能",
            "en": "Throughput Efficiency",
        },
    ),
    # MarkerDescriptions(
    #     name="throughput_rssi",
    #     name_lang={
    #         "zh-cn": "RSSI分布",
    #         "en": "RSSI",
    #     },
    #     descriptions={
    #         "zh-cn": "RSSI分布",
    #         "en": "RSSI",
    #     },
    # ),
    MarkerDescriptions(
        name="throughput_success_rate",
        name_lang={
            "zh-cn": "传输成功率",
            "en": "Transmission Success Rate",
        },
        descriptions={
            "zh-cn": "数据传输成功率",
            "en": "Data transmission success rate",
        },
    ),
    MarkerDescriptions(
        name="throughput_err_rate",
        name_lang={
            "zh-cn": "接收错误率",
            "en": "Reception Error Rate",
        },
        descriptions={
            "zh-cn": "接收错误率评分",
            "en": "Reception error rate score",
        },
    ),
    MarkerDescriptions(
        name="throughput_symmetry_score",
        name_lang={
            "zh-cn": "链路对称性评分",
            "en": "Link Symmetry Score",
        },
        descriptions={
            "zh-cn": "上下行对称性评分",
            "en": "Uplink-downlink symmetry score",
        },
    ),
    # Raw Network Metrics - Signal and Channel
    MarkerDescriptions(
        name="ap_radio_obss_chan_util",
        name_lang={
            "zh-cn": "OBSS信道利用率",
            "en": "OBSS Channel Utilization",
        },
        descriptions={
            "zh-cn": "信道利用率",
            "en": "Channel utilization",
        },
    ),
    # Raw Network Metrics - Error and Packet Counts
    MarkerDescriptions(
        name="ap_radio_rx_crc_errors_total",
        name_lang={
            "zh-cn": "CRC校验错误总数",
            "en": "Total CRC Check Errors",
        },
        descriptions={
            "zh-cn": "CRC校验错误数",
            "en": "CRC check errors",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_rx_phy_errors_total",
        name_lang={
            "zh-cn": "物理层错误总数",
            "en": "Total Physical Layer Errors",
        },
        descriptions={
            "zh-cn": "物理层信号传输错误的累计数量, 反映射频信号质量问题",
            "en": "Cumulative count of physical layer signal transmission errors, indicating RF signal quality issues",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_rx_data_packets_total",
        name_lang={
            "zh-cn": "数据包接收总数",
            "en": "Total Data Packets Received",
        },
        descriptions={
            "zh-cn": "累计接收到的数据包总数, 衡量网络流量负载",
            "en": "Cumulative total of received data packets, measuring network traffic load",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_rx_mgmt_frames_total",
        name_lang={
            "zh-cn": "管理帧接收数",
            "en": "Management Frame Receive Count",
        },
        descriptions={
            "zh-cn": "接收的网络管理帧数量",
            "en": "Management Frame Receive Count",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_rx_rssi",
        name_lang={
            "zh-cn": "接收信噪比",
            "en": "Receive SNR",
        },
        descriptions={
            "zh-cn": "接收信噪比(SNR), 衡量无线信号质量, 数值越高表示信号质量越好",
            "en": "Receive Signal-to-Noise Ratio (SNR), measuring wireless signal quality, higher values indicate better signal quality",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_rx_rssi",
        name_lang={
            "zh-cn": "客户端接收信噪比",
            "en": "Client Receive SNR",
        },
        descriptions={
            "zh-cn": "客户端特定的接收信噪比(SNR), 反映特定终端的信号接收质量",
            "en": "Client-specific receive Signal-to-Noise Ratio (SNR), reflecting signal reception quality for specific terminals",
        },
    ),
    # Raw Network Metrics - Data Rate and Throughput
    MarkerDescriptions(
        name="ap_radio_vap_node_maxium_rate_per_client_kbps",
        name_lang={
            "zh-cn": "客户端最大数据速率",
            "en": "Maximum Data Rate Per Client",
        },
        descriptions={
            "zh-cn": "单个客户端能实现的最大数据传输速率(kbps), 反映网络容量上限",
            "en": "Maximum data transmission rate achievable by a single client (kbps), indicating network capacity ceiling",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_rx_data_bytes_total",
        name_lang={
            "zh-cn": "数据接收字节总数",
            "en": "Total Data Bytes Received",
        },
        descriptions={
            "zh-cn": "累计接收的数据字节总数, 衡量网络数据流入量",
            "en": "Cumulative total of received data bytes, measuring network data inflow volume",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_tx_data_bytes_total",
        name_lang={
            "zh-cn": "数据包发送字节数",
            "en": "Data Packet Send Bytes Count",
        },
        descriptions={
            "zh-cn": "数据包发送字节数",
            "en": "Data Packet Send Bytes Count",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_tx_data_packets_total",
        name_lang={
            "zh-cn": "数据包发送总数",
            "en": "Total Data Packets Transmitted",
        },
        descriptions={
            "zh-cn": "累计发送的数据包总数, 反映网络传输活动强度",
            "en": "Cumulative total of transmitted data packets, reflecting network transmission activity intensity",
        },
    ),
    MarkerDescriptions(
        name="throughput_err_rate",
        name_lang={
            "zh-cn": "接收成功率",
            "en": "Receive Success Rate",
        },
        descriptions={
            "zh-cn": "接收成功率",
            "en": "Receive Success Rate",
        },
    ),
    MarkerDescriptions(
        name="throughput_symmetry_score",
        name_lang={
            "zh-cn": "链路对称性",
            "en": "Link Symmetry Score",
        },
        descriptions={
            "zh-cn": "上下行链路传输对称性评分",
            "en": "Link Symmetry Score",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_tx_failures_total",
        name_lang={
            "zh-cn": "数据包发送失败数",
            "en": "Data Packet Send Failure Count",
        },
        descriptions={
            "zh-cn": "数据包发送失败数",
            "en": "Data Packet Send Failure Count",
        },
    ),
    MarkerDescriptions(
        name="ap_node_signal_power",
        name_lang={
            "zh-cn": "客户端信号强度",
            "en": "Client Signal Strength",
        },
        descriptions={
            "zh-cn": "客户端设备的无线信号功率",
            "en": "Client Signal Strength",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_average_tx_rate_kbps",
        name_lang={
            "zh-cn": "终端平均发送数据速率",
            "en": "VAP Average Send Data Rate",
        },
        descriptions={
            "zh-cn": "终端平均数据发送速率",
            "en": "VAP Average Send Data Rate",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_average_rx_rate_kbps",
        name_lang={
            "zh-cn": "终端平均接受数据速率",
            "en": "VAP Average Receive Data Rate",
        },
        descriptions={
            "zh-cn": "终端平均数据接收速率",
            "en": "VAP Average Receive Data Rate",
        },
    ),
    MarkerDescriptions(
        name="ap_status_cpu_avg_usage",
        name_lang={
            "zh-cn": "AP CPU平均使用率",
            "en": "AP CPU Average Usage",
        },
        descriptions={
            "zh-cn": "接入点处理器平均使用率",
            "en": "AP CPU Average Usage",
        },
    ),
    MarkerDescriptions(
        name="ap_status_mem_avg_usage",
        name_lang={
            "zh-cn": "AP MEM平均使用率",
            "en": "AP Memory Average Usage",
        },
        descriptions={
            "zh-cn": "接入点内存平均使用率",
            "en": "AP Memory Average Usage",
        },
    ),
    # Connection Duration Metrics
    MarkerDescriptions(
        name="ap_node_connection_success_duration_ns",
        name_lang={
            "zh-cn": "连接成功耗时",
            "en": "Connection Success Duration",
        },
        descriptions={
            "zh-cn": "客户端成功建立连接所耗费的时间(纳秒), 反映连接建立效率",
            "en": "Time taken for client to successfully establish connection (nanoseconds), reflecting connection establishment efficiency",
        },
    ),
    MarkerDescriptions(
        name="ap_node_connection_failure_duration_ns",
        name_lang={
            "zh-cn": "连接失败耗时",
            "en": "Connection Failure Duration",
        },
        descriptions={
            "zh-cn": "客户端连接失败时从开始到失败的时间(纳秒), 用于分析连接问题",
            "en": "Time from start to failure when client connection fails (nanoseconds), used for connection problem analysis",
        },
    ),
    MarkerDescriptions(
        name="ap_node_connection_state_duration_ns",
        name_lang={
            "zh-cn": "连接状态切换耗时",
            "en": "Connection State Change Duration",
        },
        descriptions={
            "zh-cn": "客户端连接状态变化所耗费的时间(纳秒), 衡量状态切换效率",
            "en": "Time taken for client connection state changes (nanoseconds), measuring state transition efficiency",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_nss_symmetry",
        name_lang={
            "zh-cn": "NSS对称性",
            "en": "NSS Symmetry",
        },
        descriptions={
            "zh-cn": "NSS对称性",
            "en": "NSS Symmetry",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_mcs_symmetry",
        name_lang={
            "zh-cn": "MCS对称性",
            "en": "MCS Symmetry",
        },
        descriptions={
            "zh-cn": "MCS对称性",
            "en": "MCS Symmetry",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_tx_mcs_efficiency_ratio",
        name_lang={
            "zh-cn": "发送效率",
            "en": "Send MCS Efficiency",
        },
        descriptions={
            "zh-cn": "发送效率",
            "en": "Send MCS Efficiency",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_rx_mcs_efficiency_ratio",
        name_lang={
            "zh-cn": "接收效率",
            "en": "Receive MCS Efficiency",
        },
        descriptions={
            "zh-cn": "接收效率",
            "en": "Receive MCS Efficiency",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_rx_mcs_bucket",
        name_lang={
            "zh-cn": "接收MCS分布",
            "en": "Receive MCS Distribution",
        },
        descriptions={
            "zh-cn": "接收MCS分布",
            "en": "Receive MCS Distribution",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_tx_mcs_bucket",
        name_lang={
            "zh-cn": "发送MCS分布",
            "en": "Send MCS Distribution",
        },
        descriptions={
            "zh-cn": "发送MCS分布",
            "en": "Send MCS Distribution",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_rx_nss_bucket",
        name_lang={
            "zh-cn": "接收NSS分布",
            "en": "Receive NSS Distribution",
        },
        descriptions={
            "zh-cn": "接收NSS分布",
            "en": "Receive NSS Distribution",
        },
    ),
    MarkerDescriptions(
        name="ap_radio_vap_node_tx_nss_bucket",
        name_lang={
            "zh-cn": "发送NSS分布",
            "en": "Send NSS Distribution",
        },
        descriptions={
            "zh-cn": "发送NSS分布",
            "en": "Send NSS Distribution",
        },
    ),
]


def get_marker_description(name: str, lang: str):
    normalized_lang = lang.lower()
    if name not in marker_description_dict:
        return ""
    if normalized_lang not in marker_description_dict[name].descriptions:
        normalized_lang = "zh-cn"
    return marker_description_dict[name].descriptions[normalized_lang]


def get_marker_name(name: str, lang: str):
    normalized_lang = lang.lower()
    if name not in marker_description_dict:
        return ""
    if normalized_lang not in marker_description_dict[name].name_lang:
        normalized_lang = "zh-cn"
    return marker_description_dict[name].name_lang[normalized_lang]


marker_description_dict = {md.name: md for md in marker_descriptions}
