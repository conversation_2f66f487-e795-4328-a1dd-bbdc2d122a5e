import asyncio
from collections import defaultdict
from datetime import timedelta

import httpx
import numpy as np
import pandas as pd
import pendulum
from fastapi import HTTPEx<PERSON>, status
from pendulum import DateTime

from anomaly_detector.marker import Prom<PERSON>arker, SQLMarker
from anomaly_detector.schema import MetricsQuery
from anomaly_detector.sks.marker import CustomMarker, MarkerType, get_prom_marker_promql
from anomaly_detector.sks.marker_descriptions import get_marker_description, get_marker_name
from anomaly_detector.sks.qoe import marker_registry
from greptime import (
    AsyncGreptimeClient,
    greptime_prom_response_metrics,
    greptime_sql_response_metrics,
    greptime_timestamp_sql,
)
from utils import prom_result_to_panda_df, try_parse_float


async def _query_custom_marker(
    site_id: str,
    greptime_client: AsyncGreptimeClient,
    marker: CustomMarker | SQLMarker,
    start: DateTime,
    end: DateTime,
    value_field: str | None = None,
):
    extra_filters = {"site_id": site_id} if site_id and site_id != "sks" else None
    query_end = end + timedelta(minutes=20)
    res = await greptime_client.execute(
        greptime_timestamp_sql(
            start,
            query_end,
            marker.sql,
            extra_filters,
        ),
    )
    return greptime_sql_response_metrics(
        res,
        marker.timestamp_field,
        timestamp_unit=marker.timestamp_unit,
        value_field=value_field or marker.name,
        exclude_all_labels=bool(isinstance(marker, CustomMarker)),
        label_fields=marker.label_fields if isinstance(marker, SQLMarker) else None,
        end_time=end,
    )


def _remove_name_label(labels):
    if "__name__" in labels:
        del labels["__name__"]
    return labels


def _format_instant_response(marker_name: str, res):
    result = res.get("data", {}).get("result", [])
    return [{"value": float(v["value"][1]), "labels": _remove_name_label(v["metric"])} for v in result]


def _format_range_response(res):
    result = res.get("data", {}).get("result", [])
    return [
        {
            "values": sorted([[t, try_parse_float(v)] for t, v in v["values"]], key=lambda x: x[0]),
            "labels": _remove_name_label(v["metric"]),
        }
        for v in result
    ]


def _histogram_to_incremental(group, name: str, bucket_column="le"):
    group = group.sort_values(bucket_column)
    group[name] = group[name].diff().fillna(group[name]).clip(lower=0)
    return group


def _calculate_individual_histogram_instant(marker, res):
    rows = res.get("data", {}).get("result", [])
    data = [{marker.name: float(r["value"][1]), **_remove_name_label(r["metric"])} for r in rows]
    bucket_column = "le"
    df = pd.DataFrame(data)
    label_columns = list(df.columns.difference([bucket_column, marker.name]))
    df[bucket_column] = pd.to_numeric(df[bucket_column], errors="coerce")
    df = df[~np.isinf(df[bucket_column]) & ~np.isnan(df[bucket_column])]
    df[bucket_column] = df[bucket_column].astype(int)
    df = (
        df.groupby(label_columns)
        .apply(lambda x: _histogram_to_incremental(x, marker.name), include_groups=False)
        .reset_index()
    )
    df[bucket_column] = df[bucket_column].astype(str)
    labels_columns = [*label_columns, bucket_column]
    return [
        {"value": row[marker.name], "labels": dict(zip(labels_columns, row[labels_columns], strict=False))}
        for _, row in df.iterrows()
    ]


def _calculate_individual_histogram_range(marker, res):
    value_column = marker.name
    timestamp_column = "timestamp"
    bucket_column = "le"
    df = prom_result_to_panda_df(res, marker.name, multi_series=True)
    label_columns = list(df.columns.difference([timestamp_column, value_column, bucket_column]))
    groupby_columns = [*label_columns, timestamp_column]
    df[bucket_column] = pd.to_numeric(df[bucket_column], errors="coerce")
    df = df[~np.isinf(df[bucket_column]) & ~np.isnan(df[bucket_column])]
    df[bucket_column] = df[bucket_column].astype(int)

    df = (
        df.groupby(groupby_columns)
        .apply(lambda g: _histogram_to_incremental(g, marker.name), include_groups=False)
        .reset_index()
    )
    df = df.drop(columns=[f"level_{len(groupby_columns)}"])
    df[timestamp_column] = df[timestamp_column].astype(int) // 1e9
    df[bucket_column] = df[bucket_column].astype(str)
    return greptime_prom_response_metrics(
        df.to_dict(orient="records"),
        timestamp_column=timestamp_column,
        value_column=value_column,
        exclude_columns=[timestamp_column, value_column],
    )


async def _query_normal_marker(
    site_id: str,
    httpx_client: httpx.AsyncClient,
    marker: PromMarker,
    start: DateTime | None = None,
    end: DateTime | None = None,
    time: DateTime | None = None,
    step: str = "10m",
):
    extra_filters = {"site_id": site_id} if site_id and site_id != "sks" else None
    q = get_prom_marker_promql(marker, step, labels=extra_filters)
    if start is not None or end is not None:
        res = await httpx_client.get(
            "/query_range",
            params={
                "start": start.to_iso8601_string() if start else None,
                "end": end.to_iso8601_string() if end else None,
                "query": q,
                "step": step,
                "db": "sks",
            },
        )
        if marker.type == MarkerType.INDIVIDUAL_HISTOGRAM:
            return _calculate_individual_histogram_range(marker, res.json())
        return _format_range_response(res.json())
    params = {
        "query": q,
        "db": "sks",
    }
    if time is not None:
        params["time"] = time.to_iso8601_string()
    res = await httpx_client.get(
        "/query",
        params=params,
    )
    if marker.type == MarkerType.INDIVIDUAL_HISTOGRAM:
        return _calculate_individual_histogram_instant(marker, res.json())
    return _format_instant_response(marker.name, res.json())


async def query_marker(
    site_id: str,
    marker_name: str,
    query: MetricsQuery,
    greptime_client: AsyncGreptimeClient,
    http_client: httpx.AsyncClient,
):
    marker = marker_registry.marker(marker_name)
    if not marker:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    if not query.start or not query.end:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="start and end are required")
    start = query.start - timedelta(minutes=10)
    end = query.end - timedelta(minutes=10)
    if isinstance(marker, CustomMarker):
        if marker.name == marker_name:
            return await _query_custom_marker(site_id, greptime_client, marker, start, end)
        return await _query_custom_marker(site_id, greptime_client, marker, start, end, marker_name)
    if isinstance(marker, PromMarker):
        return await _query_normal_marker(site_id, http_client, marker, start, end)
    if isinstance(marker, SQLMarker):
        return await _query_custom_marker(site_id, greptime_client, marker, start, end, marker_name)
    return None


async def query_qoe_marker(site_id: str, query: MetricsQuery, greptime_client: AsyncGreptimeClient):
    markers = marker_registry.qoe_markers()
    extra_filters = {"site_id": site_id} if site_id and site_id != "sks" else None
    query_end = query.end + timedelta(minutes=20)
    all_res = await asyncio.gather(
        *[
            greptime_client.execute(
                greptime_timestamp_sql(
                    query.start,
                    query_end,
                    marker.sql,
                    extra_filters,
                ),
            )
            for marker in markers
        ],
    )
    dfs = []
    empty_columns = set()
    for res, marker in zip(all_res, markers, strict=False):
        if not res:
            empty_columns.add(marker.name)
            continue
        df = pd.DataFrame.from_records(res)
        df = df[[marker.timestamp_field, marker.name]]
        df[marker.timestamp_field] = df[marker.timestamp_field] / 1000
        df = df.set_index(marker.timestamp_field)
        dfs.append(df)
    if not dfs:
        return [{"labels": {}, "values": []}]
    merged = pd.concat(dfs, axis=1).fillna(1).sort_index()
    merged = merged.loc[query.start.timestamp() : (query.end - timedelta(minutes=10)).timestamp()]
    for col in empty_columns:
        merged[col] = 1
    merged["qoe"] = merged.mean(axis=1)
    ts_array = merged.index.to_numpy()
    values_array = merged["qoe"].to_numpy()
    return [{"labels": {}, "values": np.column_stack((ts_array, values_array)).tolist()}]


def _find_closest_value(values, target: DateTime, tolerance: timedelta = timedelta(minutes=10)):
    if not values:
        return {}
    for value in values[::-1]:
        if value.get("ts") <= target.timestamp() * 1000 and value.get("ts") > (target - tolerance).timestamp() * 1000:
            return value
    return {}


async def qoe_scores(
    site_id: str,
    greptime_client: AsyncGreptimeClient,
    http_client: httpx.AsyncClient,
    datetime: DateTime | None = None,
    lang="zh-cn",
):
    markers_result = defaultdict(dict)
    step = timedelta(minutes=10)
    datetime = (datetime if datetime else pendulum.now()) - timedelta(minutes=10)
    end = datetime + step
    start = end.subtract(minutes=30)
    qoe_total_value = 0
    qoe_marker_count = 0
    extra_filters = {"site_id": site_id} if site_id and site_id != "sks" else None
    for marker in marker_registry.qoe_markers():
        if isinstance(marker, CustomMarker):
            res = await greptime_client.execute(greptime_timestamp_sql(start, end, marker.sql, extra_filters))
        elif isinstance(marker, PromMarker):
            res = await _query_normal_marker(site_id, http_client, marker)
        elif isinstance(marker, SQLMarker):
            res = await greptime_client.execute(greptime_timestamp_sql(start, end, marker.sql, extra_filters))
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported marker type")
        latest_value = _find_closest_value(res, datetime) if res else {}
        marker_result = {
            "value": latest_value.get(marker.name, 1),
            "name": marker.name,
            "name_lang": get_marker_name(marker.name, lang),
            "description": get_marker_description(marker.name, lang),
            "tag": marker.tag,
            "raw_markers": [
                {
                    "name": m.name,
                    "description": get_marker_description(m.name, lang),
                    "name_lang": get_marker_name(m.name, lang),
                }
                for m in marker_registry.markers_by_tag(marker.tag)
            ],
            "markers": {
                sub_marker.name: {
                    "value": latest_value.get(sub_marker.name, 1),
                    "name": sub_marker.name,
                    "description": get_marker_description(sub_marker.name, lang),
                    "raw_markers": [
                        {
                            "name": m.name,
                            "description": get_marker_description(m.name, lang),
                            "name_lang": get_marker_name(m.name, lang),
                        }
                        for m in marker_registry.qoe_related_markers(sub_marker.name)
                    ],
                }
                for sub_marker in marker.sub_markers
            },
        }

        v = latest_value.get(marker.name, 1)
        qoe_total_value += v if v is not None else 0
        qoe_marker_count += 1
        markers_result[marker.name] = marker_result
    return {
        "name": "qoe",
        "value": qoe_total_value / qoe_marker_count if qoe_marker_count else 0,
        "markers": markers_result,
    }


async def list_markers(
    site_id: str,
    greptime_client: AsyncGreptimeClient,
    http_client: httpx.AsyncClient,
    filter_tags: list[str] | None = None,
    filter_markers: list[str] | None = None,
    lang="zh-cn",
):
    markers_result = defaultdict(dict)
    end = pendulum.now()
    start = end.subtract(minutes=20)
    extra_filters = {"site_id": site_id} if site_id and site_id != "sks" else None
    for marker in marker_registry.markers().values():
        if marker.qoe_score:
            continue
        if filter_tags and marker.tag not in filter_tags:
            continue
        if filter_markers and marker.name not in filter_markers:
            continue
        if isinstance(marker, CustomMarker):
            res = await greptime_client.execute(greptime_timestamp_sql(start, end, marker.sql, extra_filters))
            latest_value = res[-1] if res else {}
            if not marker.hide:
                marker_result = {
                    "value": latest_value.get(marker.name, None),
                    "name": marker.name,
                    "description": get_marker_description(marker.name, lang),
                }
                marker_result["markers"] = {
                    sub_marker.name: {
                        "value": latest_value.get(sub_marker.name, None),
                        "name": sub_marker.name,
                        "description": get_marker_description(sub_marker.name, lang),
                    }
                    for sub_marker in marker.sub_markers
                }
        elif isinstance(marker, PromMarker):
            res = await _query_normal_marker(site_id, http_client, marker)
            marker_result = {
                "results": res,
                "name": marker.name,
                "description": get_marker_description(marker.name, lang),
            }
            if marker.qoes:
                marker_result["qoes"] = marker.qoes
            markers_result[marker.tag][marker.name] = marker_result
        elif isinstance(marker, SQLMarker):
            res = await greptime_client.execute(greptime_timestamp_sql(start, end, marker.sql, extra_filters))
            latest_value = res[-1] if res else {}
            for sub_marker in marker.sub_markers:
                marker_result = {
                    "results": [] if latest_value else [],  # FIXME
                    "name": sub_marker.name,
                    "description": get_marker_description(sub_marker.name, lang),
                }
                markers_result[marker.tag][sub_marker.name] = marker_result
                if marker.qoes:
                    marker_result["qoes"] = marker.qoes
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported marker type")
    return markers_result
