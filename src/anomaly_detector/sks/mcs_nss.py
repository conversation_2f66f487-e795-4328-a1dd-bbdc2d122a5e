import asyncio
from functools import reduce
from typing import Final, Literal

import httpx
import numpy as np
import pandas as pd
import pendulum
from pypika import PostgreSQLQuery as Q, Table, functions as fn

from anomaly_detector.sks.utils import get_mcs_index
from greptime import AsyncGreptimeClient
from utils import prom_result_to_panda_df

McsDirectionType = Literal["rx", "tx"]
MCS_DIRECTION: Final[list[McsDirectionType]] = ["rx", "tx"]

McsNssType = Literal["mcs", "nss"]
MCS_NSS: Final[list[McsNssType]] = ["mcs", "nss"]


async def _get_sta_bandwidth(
    greptime_client: AsyncGreptimeClient,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    site_id: str,
):
    table = Table("ap_radio_vap_node_band_width")
    q = (
        Q.from_(table)
        .where(table.site_id == site_id)
        .where(table.greptime_timestamp[start_time:end_time])
        .orderby(table.sta_mac, table.radio, table.ap_mac)
        .groupby(table.sta_mac, table.radio, table.ap_mac)
        .select(fn.Max(table.greptime_value).as_("greptime_value"), table.sta_mac, table.radio, table.ap_mac)
    )
    res = await greptime_client.execute(q)
    return {(item["sta_mac"], item["radio"], item["ap_mac"]): item for item in res}


async def _get_sta_max_rate(
    greptime_client: AsyncGreptimeClient,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    site_id: str,
):
    table = Table("ap_radio_vap_node_maxium_rate_per_client_kbps")
    q = (
        Q.from_(table)
        .where(table.site_id == site_id)
        .where(table.greptime_timestamp[start_time:end_time])
        .orderby(table.sta_mac, table.radio, table.ap_mac)
        .groupby(table.sta_mac, table.radio, table.ap_mac)
        .select(fn.Max(table.greptime_value).as_("greptime_value"), table.sta_mac, table.radio, table.ap_mac)
    )
    res = await greptime_client.execute(q)
    return {(item["sta_mac"], item["radio"], item["ap_mac"]): item for item in res}


def _mcs_metric_name(direction: McsDirectionType):
    return f"ap_radio_vap_node_{direction}_mcs_bucket"


def _nss_metric_name(direction: McsDirectionType):
    return f"ap_radio_vap_node_{direction}_nss_bucket"


def _mcs_efficiency_ratio_metric_name(direction: McsDirectionType):
    return f"ap_radio_{direction}_mcs_efficiency_ratio"


def _mcs_or_nss_packets_metric_name(direction: McsDirectionType, mcs_or_nss: Literal["mcs", "nss"]):
    return f"ap_radio_{direction}_{mcs_or_nss}_packets_gauge"


def _mcs_or_nss_weighed_metric_name(direction: McsDirectionType, mcs_or_nss: Literal["mcs", "nss"]):
    return f"ap_radio_{direction}_{mcs_or_nss}_weighed_gauge"


def _mcs_or_nss_symmetry_value_metric_name(direction: McsDirectionType, mcs_or_nss: Literal["mcs", "nss"]):
    return f"ap_radio_{direction}_{mcs_or_nss}_symmetry_value"


def _mcs_or_nss_symmetry_metric_name(mcs_or_nss: Literal["mcs", "nss"]):
    return f"ap_radio_{mcs_or_nss}_symmetry"


def _mcs_or_nss_promql(metric_name: str, site_id: str, step: str):
    return f'sum(increase({metric_name}{{site_id="{site_id}", le=~"\\\\d+"}}[{step}])) by (sta_mac, radio, ap_mac, le)'


async def _fetch_mcs_buckets_promql_df(
    promql_async_client: httpx.AsyncClient,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    metric_name: str,
    site_id: str,
) -> pd.DataFrame:
    step = "10m"
    promql = _mcs_or_nss_promql(metric_name, site_id, step)
    res = await promql_async_client.get(
        "/query_range",
        params={
            "query": promql,
            "start": start_time.to_iso8601_string(),
            "end": end_time.to_iso8601_string(),
            "step": step,
        },
    )
    response = res.json()
    return prom_result_to_panda_df(response, metric_name, multi_series=True)


async def _get_aggregated_mcs_or_nss_data(
    promql_async_client: httpx.AsyncClient,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    direction: McsDirectionType,
    mcs_or_nss: Literal["mcs", "nss"],
    site_id: str,
):
    metric_name = _mcs_metric_name(direction) if mcs_or_nss == "mcs" else _nss_metric_name(direction)
    mcs_df = await _fetch_mcs_buckets_promql_df(promql_async_client, start_time, end_time, metric_name, site_id)
    if mcs_df.empty:
        return pd.DataFrame()

    # Filter and aggregate across time dimensions
    mcs_df["le"] = pd.to_numeric(mcs_df["le"], errors="coerce")
    mcs_df = mcs_df[~np.isinf(mcs_df["le"]) & ~np.isnan(mcs_df["le"])]
    mcs_df = mcs_df.groupby(["sta_mac", "radio", "ap_mac", "le"])[metric_name].sum().reset_index()

    # Convert cumulative histogram buckets to incremental values
    def to_incremental(group):
        group = group.sort_values("le")
        group[metric_name] = group[metric_name].diff().fillna(group[metric_name]).clip(lower=0)
        return group

    mcs_df = (
        mcs_df.sort_values(["sta_mac", "radio", "ap_mac", "le"])
        .groupby(["sta_mac", "radio", "ap_mac"])
        .apply(to_incremental)
        .reset_index(drop=True)
    )

    # Calculate weighted values and aggregate
    mcs_df[f"le_{mcs_or_nss}"] = (mcs_df["le"] + 1) * mcs_df[metric_name]

    return (
        mcs_df.groupby(["sta_mac", "radio", "ap_mac"])
        .agg(
            **{
                _mcs_or_nss_packets_metric_name(direction, mcs_or_nss): (metric_name, "sum"),
                _mcs_or_nss_weighed_metric_name(direction, mcs_or_nss): (f"le_{mcs_or_nss}", "sum"),
            },
        )
        .reset_index()
    )


def _calculate_mcs_efficiency_ratio(
    combined_df: pd.DataFrame,
    sta_max_rate: dict,
    sta_bandwidth: dict,
    direction: McsDirectionType,
) -> pd.DataFrame:
    total_mcs_col = _mcs_or_nss_packets_metric_name(direction, "mcs")
    weighted_mcs_col = _mcs_or_nss_weighed_metric_name(direction, "mcs")
    ideal_weighted_col = f"ideal_weighted_{direction}_mcs"
    mcs_efficiency_ratio_col = _mcs_efficiency_ratio_metric_name(direction)
    best_mcs_index_col = f"best_{direction}_mcs_index"

    if combined_df.empty or total_mcs_col not in combined_df.columns or weighted_mcs_col not in combined_df.columns:
        return combined_df

    def _add_best_mcs_index_to_row(row: pd.Series) -> int | None:
        sta_mac = row["sta_mac"]
        radio = row["radio"]
        ap_mac = row["ap_mac"]
        max_rate_item = sta_max_rate.get((sta_mac, radio, ap_mac), None)
        bandwidth_item = sta_bandwidth.get((sta_mac, radio, ap_mac), None)
        if max_rate_item is None or bandwidth_item is None:
            return None
        bandwidth = bandwidth_item["greptime_value"]
        max_rate = max_rate_item["greptime_value"]
        mcs_info = get_mcs_index(bandwidth, max_rate)
        if mcs_info is None:
            return None
        return mcs_info.mcs_index

    combined_df[best_mcs_index_col] = combined_df.apply(_add_best_mcs_index_to_row, axis=1)
    combined_df[ideal_weighted_col] = (combined_df[best_mcs_index_col] + 1) * combined_df[total_mcs_col]
    combined_df[mcs_efficiency_ratio_col] = (
        (combined_df[weighted_mcs_col] / combined_df[ideal_weighted_col])
        .where(combined_df[ideal_weighted_col] != 0, 0)
        .clip(0, 1)
    )
    return combined_df.dropna()


async def calculate_mcs_nss_qoes(
    greptime_client: AsyncGreptimeClient,
    promql_async_client: httpx.AsyncClient,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    site_id: str,
) -> pd.DataFrame:
    rx_mcs_df, tx_mcs_df, rx_nss_df, tx_nss_df, sta_max_rate, sta_bandwidth = await asyncio.gather(
        _get_aggregated_mcs_or_nss_data(promql_async_client, start_time, end_time, "rx", "mcs", site_id),
        _get_aggregated_mcs_or_nss_data(promql_async_client, start_time, end_time, "tx", "mcs", site_id),
        _get_aggregated_mcs_or_nss_data(promql_async_client, start_time, end_time, "rx", "nss", site_id),
        _get_aggregated_mcs_or_nss_data(promql_async_client, start_time, end_time, "tx", "nss", site_id),
        _get_sta_max_rate(greptime_client, start_time, end_time, site_id),
        _get_sta_bandwidth(greptime_client, start_time, end_time, site_id),
    )
    dfs = [rx_mcs_df, tx_mcs_df, rx_nss_df, tx_nss_df]
    dfs = [df for df in dfs if not df.empty]
    if not dfs:
        return pd.DataFrame()
    combined_df = reduce(lambda left, right: pd.merge(left, right, on=["sta_mac", "radio", "ap_mac"]), dfs)
    combined_df = combined_df.drop_duplicates(subset=["sta_mac", "radio", "ap_mac"])
    combined_df = _calculate_mcs_efficiency_ratio(combined_df, sta_max_rate, sta_bandwidth, "rx")
    combined_df = _calculate_mcs_efficiency_ratio(combined_df, sta_max_rate, sta_bandwidth, "tx")

    rx_ratio_col = _mcs_efficiency_ratio_metric_name("rx")
    tx_ratio_col = _mcs_efficiency_ratio_metric_name("tx")

    if combined_df.empty:
        return pd.DataFrame()

    agg_dict = {}
    if rx_ratio_col in combined_df.columns:
        agg_dict[rx_ratio_col] = "mean"
    if tx_ratio_col in combined_df.columns:
        agg_dict[tx_ratio_col] = "mean"

    for direction in MCS_DIRECTION:
        for mcs_or_nss in MCS_NSS:
            packets_col = _mcs_or_nss_packets_metric_name(direction, mcs_or_nss)
            weighed_col = _mcs_or_nss_weighed_metric_name(direction, mcs_or_nss)
            if packets_col in combined_df.columns and weighed_col in combined_df.columns:
                agg_dict[packets_col] = "sum"
                agg_dict[weighed_col] = "sum"

    aggregated_df = combined_df.groupby(["radio", "ap_mac"]).agg(agg_dict).reset_index()

    for mcs_or_nss in MCS_NSS:
        rx_symmetry_value_col = _mcs_or_nss_symmetry_value_metric_name("rx", mcs_or_nss)
        tx_symmetry_value_col = _mcs_or_nss_symmetry_value_metric_name("tx", mcs_or_nss)
        rx_weighed_col = _mcs_or_nss_weighed_metric_name("rx", mcs_or_nss)
        tx_weighed_col = _mcs_or_nss_weighed_metric_name("tx", mcs_or_nss)
        rx_packets_col = _mcs_or_nss_packets_metric_name("rx", mcs_or_nss)
        tx_packets_col = _mcs_or_nss_packets_metric_name("tx", mcs_or_nss)
        symmetry_col = _mcs_or_nss_symmetry_metric_name(mcs_or_nss)
        if rx_packets_col in aggregated_df.columns and rx_weighed_col in aggregated_df.columns:
            aggregated_df[rx_symmetry_value_col] = np.where(
                aggregated_df[rx_packets_col] == 0,
                0,
                aggregated_df[rx_weighed_col] / aggregated_df[rx_packets_col],
            )
        if tx_packets_col in aggregated_df.columns and tx_weighed_col in aggregated_df.columns:
            aggregated_df[tx_symmetry_value_col] = np.where(
                aggregated_df[tx_packets_col] == 0,
                0,
                aggregated_df[tx_weighed_col] / aggregated_df[tx_packets_col],
            )

        if rx_symmetry_value_col in aggregated_df.columns and tx_symmetry_value_col in aggregated_df.columns:
            min_values = aggregated_df[[rx_symmetry_value_col, tx_symmetry_value_col]].min(axis=1)
            max_values = aggregated_df[[rx_symmetry_value_col, tx_symmetry_value_col]].max(axis=1)
            aggregated_df[symmetry_col] = np.where(max_values == 0, 0, min_values / max_values)

    aggregated_df = aggregated_df.dropna()
    aggregated_df["site_id"] = site_id

    return aggregated_df


async def run_mcs_nss_qoes_job(
    greptime_client: AsyncGreptimeClient,
    promql_async_client: httpx.AsyncClient,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    site_id: str,
):
    qoe_df = await calculate_mcs_nss_qoes(greptime_client, promql_async_client, start_time, end_time, site_id)
    if qoe_df.empty:
        return
    qoe_df["timestamp"] = end_time.timestamp() * 1000
    await greptime_client.push_influxdb("ap_radio_mcs_nss_qoes", qoe_df.to_dict(orient="records"))


def sync_mcs_nss_qoes(
    greptime_client_url: str,
    start_time: pendulum.DateTime,
    end_time: pendulum.DateTime,
    sks_db: str,
    site_id: str,
):
    greptime_client = AsyncGreptimeClient(greptime_client_url, sks_db)
    promql_async_client = httpx.AsyncClient(
        base_url=f"{greptime_client_url}/v1/prometheus/api/v1",
        headers={"x-greptime-db-name": sks_db},
    )
    asyncio.run(run_mcs_nss_qoes_job(greptime_client, promql_async_client, start_time, end_time, site_id))
