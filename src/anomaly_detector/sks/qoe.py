from anomaly_detector.marker import (
    PromMark<PERSON>,
    SQLMarker,
    SubMarker,
)
from anomaly_detector.sks.marker import MarkerType, SksMarker, SksPromMarker, Tag
from anomaly_detector.sks.sqls import connection_sql, coverage_sql, mcs_nss_sql, throughput_qoe


class SksMarkerRegistry:
    def __init__(self):
        self._markers: dict[str, SksMarker] = {}

    def register(self, marker: "SksMarker"):
        self._markers[marker.name] = marker

    def markers(self):
        return self._markers

    def markers_by_tag(self, tag: Tag):
        markers = []
        for marker in self._markers.values():
            if marker.tag == tag and not marker.qoe_score:
                print(marker)
                if isinstance(marker, SksPromMarker):
                    markers.append(marker)
                elif isinstance(marker, SQLMarker):
                    markers.extend(marker.sub_markers)
        return markers

    def qoe_related_markers(self, qoe: str):
        markers = []
        for marker in self._markers.values():
            if marker.qoe_score:
                continue

            if qoe not in marker.qoes:
                continue
            if isinstance(marker, SQLMarker):
                for sub_marker in marker.sub_markers:
                    markers.append(sub_marker)
            else:
                markers.append(marker)
        return markers

    def qoe_markers(self) -> list[CustomMarker]:
        return [marker for marker in self._markers.values() if isinstance(marker, CustomMarker) and marker.qoe_score]

    def marker(self, name: str):
        if name in self._markers:
            return self._markers[name]
        for marker in self._markers.values():
            if isinstance(marker, (CustomMarker, SQLMarker)):
                if name in [sub_marker.name for sub_marker in marker.sub_markers]:
                    return marker
        return None


marker_registry = SksMarkerRegistry()

default_omit_labels = ["__name__", "sta_mac", "state", "site_id"]

sks_markers = [
    CustomMarker(
        "connection_qoe",
        Tag.CONNECTION,
        MarkerType.GAUGE,
        qoe_score=True,
        sql=connection_sql,
        sub_markers=[
            SubMarker("connection_successful_connect_rate"),
            SubMarker("connection_successful_state_change_rate"),
            SubMarker("connection_successful_duration_score"),
        ],
    ),
    CustomMarker(
        "coverage_qoe",
        Tag.COVERAGE,
        MarkerType.GAUGE,
        qoe_score=True,
        sql=coverage_sql,
        sub_markers=[
            SubMarker("coverage_interference_score"),
            SubMarker("coverage_rssi_score"),
            # SubMarker("coverage_link_score"),
        ],
    ),
    CustomMarker(
        "throughput_qoe",
        Tag.THROUGHPUT,
        MarkerType.GAUGE,
        qoe_score=True,
        sql=throughput_qoe,
        sub_markers=[
            SubMarker("throughput_efficiency"),
            SubMarker("throughput_success_rate"),
            SubMarker("throughput_symmetry_score"),
            SubMarker("throughput_err_rate"),
        ],
    ),
    SQLMarker(
        "mcs_nss_qoe_efficiency",
        Tag.THROUGHPUT,
        MarkerType.GAUGE,
        hide=True,
        sql=mcs_nss_sql,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_efficiency"],
        label_fields=["radio", "ap_mac"],
        sub_markers=[
            SubMarker("ap_radio_rx_mcs_efficiency_ratio"),
            SubMarker("ap_radio_tx_mcs_efficiency_ratio"),
        ],
    ),
    SQLMarker(
        "mcs_nss_qoe_symmetry",
        Tag.THROUGHPUT,
        MarkerType.GAUGE,
        hide=True,
        sql=mcs_nss_sql,
        omit_labels=list(default_omit_labels),
        label_fields=["radio", "ap_mac"],
        qoes=["throughput_symmetry_score"],
        sub_markers=[
            SubMarker("ap_radio_nss_symmetry"),
            SubMarker("ap_radio_mcs_symmetry"),
        ],
    ),
    PromMarker(
        "ap_radio_obss_chan_util",
        Tag.COVERAGE,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
        qoes=["coverage_interference_score"],
    ),
    PromMarker(
        "ap_radio_rx_crc_errors_total",
        Tag.THROUGHPUT,
        MarkerType.COUNTER,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_err_rate"],
    ),
    PromMarker(
        "ap_radio_rx_phy_errors_total",
        Tag.THROUGHPUT,
        MarkerType.COUNTER,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_err_rate"],
    ),
    PromMarker(
        "ap_radio_rx_data_packets_total",
        Tag.THROUGHPUT,
        MarkerType.COUNTER,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_err_rate"],
    ),
    PromMarker(
        "ap_radio_rx_mgmt_frames_total",
        Tag.THROUGHPUT,
        MarkerType.COUNTER,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_err_rate"],
    ),
    PromMarker(
        "ap_radio_rx_rssi",
        Tag.COVERAGE,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
        qoes=["coverage_rssi_score"],
    ),
    # PromMarker(
    #     "ap_radio_vap_node_rx_rssi",
    #     Tag.COVERAGE,
    #     MarkerType.GAUGE,
    #     omit_labels=list(default_omit_labels),
    #     qoes=["coverage_rssi_score", "throughput_rssi"],
    # ),
    PromMarker(
        "ap_radio_vap_node_rx_mcs_bucket",
        Tag.THROUGHPUT,
        MarkerType.INDIVIDUAL_HISTOGRAM,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_efficiency", "throughput_symmetry_score"],
    ),
    PromMarker(
        "ap_radio_vap_node_tx_mcs_bucket",
        Tag.THROUGHPUT,
        MarkerType.INDIVIDUAL_HISTOGRAM,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_efficiency", "throughput_symmetry_score"],
    ),
    PromMarker(
        "ap_radio_vap_node_rx_nss_bucket",
        Tag.THROUGHPUT,
        MarkerType.INDIVIDUAL_HISTOGRAM,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_symmetry_score"],
    ),
    PromMarker(
        "ap_radio_vap_node_tx_nss_bucket",
        Tag.THROUGHPUT,
        MarkerType.INDIVIDUAL_HISTOGRAM,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_symmetry_score"],
    ),
    PromMarker(
        "ap_radio_vap_node_maxium_rate_per_client_kbps",
        Tag.THROUGHPUT,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_efficiency"],
    ),
    # PromMarker(
    #     "ap_radio_rx_data_bytes_total",
    #     Tag.THROUGHPUT,
    #     MarkerType.COUNTER,
    #     omit_labels=list(default_omit_labels),
    #     qoes=["throughput_efficiency"],
    # ),
    # PromMarker(
    #     "ap_radio_tx_data_bytes_total",
    #     Tag.THROUGHPUT,
    #     MarkerType.COUNTER,
    #     omit_labels=list(default_omit_labels),
    #     qoes=["throughput_efficiency"],
    # ),
    PromMarker(
        "ap_radio_tx_data_packets_total",
        Tag.THROUGHPUT,
        MarkerType.COUNTER,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_success_rate"],
    ),
    PromMarker(
        "ap_radio_tx_failures_total",
        Tag.THROUGHPUT,
        MarkerType.COUNTER,
        omit_labels=list(default_omit_labels),
        qoes=["throughput_success_rate"],
    ),
    PromMarker(
        "ap_node_signal_power",
        Tag.OTHER,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
    ),
    PromMarker(
        "ap_radio_vap_node_average_tx_rate_kbps",
        Tag.OTHER,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
    ),
    PromMarker(
        "ap_radio_vap_node_average_rx_rate_kbps",
        Tag.OTHER,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
    ),
    PromMarker(
        "ap_status_cpu_avg_usage",
        Tag.OTHER,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
    ),
    PromMarker(
        "ap_status_mem_avg_usage",
        Tag.OTHER,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
    ),
    PromMarker(
        "ap_node_connection_success_duration_ns",
        Tag.CONNECTION,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
        qoes=[
            "connection_successful_connect_rate",
            "connection_successful_duration_score",
        ],
    ),
    PromMarker(
        "ap_node_connection_failure_duration_ns",
        Tag.CONNECTION,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
        qoes=[
            "connection_successful_connect_rate",
            "connection_successful_state_change_rate",
        ],
    ),
    PromMarker(
        "ap_node_connection_state_duration_ns",
        Tag.CONNECTION,
        MarkerType.GAUGE,
        omit_labels=list(default_omit_labels),
        qoes=["connection_successful_state_change_rate"],
    ),
]

for m in sks_markers:
    marker_registry.register(m)
