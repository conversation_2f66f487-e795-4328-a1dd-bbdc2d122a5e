from pathlib import Path

file_path = Path(__file__)
sql_template_path = file_path.parent / "sqls"


def get_sql(sql_name: str):
    sql_file = sql_template_path / f"{sql_name}.sql"
    return sql_file.read_text()


connection_sql = get_sql("connection_qoe")
# link_qoe_sql = get_sql("link_qoe")
coverage_sql = get_sql("coverage_qoe")
throughput_qoe = get_sql("throughput_qoe")
mcs_nss_sql = get_sql("mcs_nss")
