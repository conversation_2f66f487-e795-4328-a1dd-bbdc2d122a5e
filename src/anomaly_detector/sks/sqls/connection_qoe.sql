WITH success AS (
  SELECT greptime_timestamp AS ts,
    auth_mode,
    webauth,
    count(*) RANGE '10m' FILL 0 AS success_count,
    (
      approx_percentile_cont(greptime_value, 0.9) / 1e9
    ) RANGE '10m' FILL 0 AS duration_second
  FROM ap_node_connection_success_duration_ns
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter
    AND auth_mode != 'nil' ALIGN '10m' BY (auth_mode, webauth)
),
failure AS (
  SELECT greptime_timestamp AS ts,
    auth_mode,
    webauth,
    count(*) RANGE '10m' FILL 0 AS failure_count
  FROM ap_node_connection_failure_duration_ns
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter
    AND auth_mode != 'nil' ALIGN '10m' BY (auth_mode, webauth)
),
state_change AS (
  SELECT greptime_timestamp AS ts,
    auth_mode,
    webauth,
    count(*) RANGE '10m' FILL 0 AS state_change_count
  FROM ap_node_connection_state_duration_ns
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter
    AND auth_mode != 'nil' ALIGN '10m' BY (auth_mode, webauth)
),
all_relevant_combinations AS (
  SELECT ts,
    auth_mode,
    webauth
  FROM success
  UNION
  SELECT ts,
    auth_mode,
    webauth
  FROM failure
  UNION
  SELECT ts,
    auth_mode,
    webauth
  FROM state_change
),
scores_intermediate AS (
  SELECT k.ts,
    k.auth_mode,
    k.webauth,
    COALESCE(s.success_count, 0) AS success_count,
    COALESCE(s.duration_second, 0) AS duration_second,
    COALESCE(f.failure_count, 0) AS failure_count,
    COALESCE(sc.state_change_count, 0) AS state_change_count
  FROM all_relevant_combinations k
    LEFT JOIN success s ON k.ts = s.ts
    AND k.auth_mode = s.auth_mode
    AND k.webauth = s.webauth
    LEFT JOIN failure f ON k.ts = f.ts
    AND k.auth_mode = f.auth_mode
    AND k.webauth = f.webauth
    LEFT JOIN state_change sc ON k.ts = sc.ts
    AND k.auth_mode = sc.auth_mode
    AND k.webauth = sc.webauth
),
scores AS (
  SELECT si.ts,
    COALESCE(
      AVG(
        CASE
          WHEN (
            si.success_count is NULL
            OR si.success_count == 0
          )
          AND (
            si.failure_count is NULL
            OR si.failure_count == 0
          ) THEN 1
          ELSE si.success_count * 1.0 / NULLIF(si.success_count + si.failure_count, 0)
        END
      ),
      1
    ) AS successful_connect_rate,
    COALESCE(
      AVG(
        CASE
          WHEN (
            si.state_change_count is NULL
            OR si.state_change_count == 0
          )
          AND (
            si.failure_count is NULL
            OR si.failure_count == 0
          ) THEN 1
          ELSE si.state_change_count * 1.0 / NULLIF(si.state_change_count + si.failure_count, 0)
        END
      ),
      1
    ) AS successful_state_change_rate,
    COALESCE(
      AVG(
        CASE
          WHEN (
            si.success_count is NULL
            OR si.success_count == 0
          )
          AND (si.failure_count > 0) THEN 0
          WHEN si.duration_second <= 10 THEN 0.7 + (10 - si.duration_second) * 0.03
          WHEN si.duration_second <= 20 THEN 0.7 - (si.duration_second - 10) * 0.05
          ELSE 0.2 - LEAST((si.duration_second - 20) * 0.01, 0.15)
        END
      ),
      1
    ) AS successful_duration_score
  FROM scores_intermediate si
  GROUP BY si.ts
)
SELECT scores.ts,
  scores.successful_connect_rate AS connection_successful_connect_rate,
  scores.successful_state_change_rate AS connection_successful_state_change_rate,
  scores.successful_duration_score AS connection_successful_duration_score,
  (
    scores.successful_connect_rate * 0.4 + scores.successful_state_change_rate * 0.3 + scores.successful_duration_score * 0.3
  ) AS connection_qoe
FROM scores
WHERE $__timeFilter(scores.ts)
GROUP BY scores.ts
ORDER BY ts ASC;