WITH chan_util AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    AVG(greptime_value) RANGE '10m' AS chan_util
  FROM ap_radio_obss_chan_util
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL NULL
),
avg_chan_util AS (
  SELECT ts,
    AVG(chan_util) AS avg_chan_util
  FROM chan_util
  GROUP BY ts
),
interference_score AS (
  SELECT avg_chan_util.ts,
    1 - avg_chan_util.avg_chan_util / 100 AS interference_score
  FROM avg_chan_util
),
basi_rssi_value AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    AVG(greptime_value) RANGE '10m' AS basi_rssi
  FROM ap_radio_rx_rssi
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL NULL
),
basi_rssi_score AS (
  SELECT basi_rssi_value.ts,
    basi_rssi_value.radio,
    AVG(
      (
        CASE
          WHEN (
            100 * (60 - 8) * (60 - 8) - (60 - basi_rssi_value.basi_rssi) * (25 * (60 - 8) + 95 * (60  - basi_rssi_value.basi_rssi))
          ) / (60 - 8) / (60 - 8) < 0 THEN 0
          WHEN (
            100 * (60 - 8) * (60 - 8) - (60  - basi_rssi_value.basi_rssi) * (25 * (60 - 8) + 95 * (60  - basi_rssi_value.basi_rssi))
          ) / (60 - 8) / (60 - 8)  > 100 THEN 100
          ELSE (
            100 * (60 - 8) * (60 - 8) - (60  - basi_rssi_value.basi_rssi) * (25 * (60 - 8) + 95 * (60  - basi_rssi_value.basi_rssi))
          ) / (60 - 8) / (60 - 8)
        END
      ) / 100
    ) AS basi_rssi_score
  FROM basi_rssi_value
  GROUP BY basi_rssi_value.ts,
    basi_rssi_value.radio
),
rssi_radio_scores AS (
  SELECT basi_rssi_score.ts,
    basi_rssi_score.radio,
    AVG(basi_rssi_score.basi_rssi_score) AS rssi_radio_score
  FROM basi_rssi_score
  GROUP BY basi_rssi_score.ts,
    basi_rssi_score.radio
),
rssi_scores_2g AS (
  SELECT ts,
    AVG(rssi_radio_score) as score
  FROM rssi_radio_scores
  WHERE radio = 'wifi0'
  GROUP BY ts
),
rssi_scores_5g AS (
  SELECT ts,
    AVG(rssi_radio_score) as score
  FROM rssi_radio_scores
  WHERE radio = 'wifi1'
  GROUP BY ts
),
rssi_score AS (
  SELECT COALESCE(rssi_scores_2g.ts, rssi_scores_5g.ts) AS timestamp,
    (
      CASE
        WHEN rssi_scores_2g.score IS NULL
        AND rssi_scores_5g.score IS NOT NULL THEN rssi_scores_5g.score
        WHEN rssi_scores_5g.score IS NULL
        AND rssi_scores_2g.score IS NOT NULL THEN rssi_scores_2g.score
        ELSE (COALESCE(rssi_scores_2g.score, 0) * 0.7) + (COALESCE(rssi_scores_5g.score, 0) * 0.3)
      END
    ) AS rssi_score
  FROM rssi_scores_2g
    FULL JOIN rssi_scores_5g ON rssi_scores_2g.ts = rssi_scores_5g.ts
  WHERE COALESCE(rssi_scores_2g.ts, rssi_scores_5g.ts) IS NOT NULL
  ORDER BY COALESCE(rssi_scores_2g.ts, rssi_scores_5g.ts)
),
ap_radio_mcs_nss_symmetry AS (
  SELECT ts + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    AVG(
      ap_radio_mcs_symmetry * 0.5 + ap_radio_nss_symmetry * 0.5
    ) RANGE '10m' AS mcs_nss_symmetry
  FROM ap_radio_mcs_nss_qoes
  WHERE $__timeFilter(ts) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL NULL
),
link_score AS (
  SELECT ts,
    AVG(mcs_nss_symmetry) AS link_score
  FROM ap_radio_mcs_nss_symmetry
  GROUP BY ap_radio_mcs_nss_symmetry.ts
),
SELECT rssi_score.timestamp AS ts,
rssi_score.rssi_score AS coverage_rssi_score,
interference_score.interference_score AS coverage_interference_score,
(
  rssi_score.rssi_score + interference_score.interference_score
) / 2 AS coverage_qoe
FROM rssi_score
  JOIN interference_score ON rssi_score.timestamp = interference_score.ts
WHERE $__timeFilter(rssi_score.timestamp)
ORDER BY rssi_score.timestamp ASC;
