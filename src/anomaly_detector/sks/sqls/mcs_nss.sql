SELECT ts,
  radio,
  ap_mac,
  AVG(ap_radio_rx_mcs_efficiency_ratio) RANGE '10m' AS ap_radio_rx_mcs_efficiency_ratio,
  AVG(ap_radio_tx_mcs_efficiency_ratio) RANGE '10m' AS ap_radio_tx_mcs_efficiency_ratio,
  AVG(ap_radio_nss_symmetry) RANGE '10m' AS ap_radio_nss_symmetry,
  AVG(ap_radio_mcs_symmetry) RANGE '10m' AS ap_radio_mcs_symmetry
FROM ap_radio_mcs_nss_qoes
WHERE $__timeFilter(ts) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL NULL
ORDER BY ts ASC;