WITH tx_packet AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    radio,
    (max(greptime_value) - min(greptime_value)) RANGE '10m' AS tx_packet_total
  FROM ap_radio_tx_data_packets_total
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio) FILL PREV
),
tx_error AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    radio,
    (max(greptime_value) - min(greptime_value)) RANGE '10m' AS tx_error_total
  FROM ap_radio_tx_failures_total
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio) FILL PREV
),

crc_err_rate AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    (
      (MAX(greptime_value) - MIN(greptime_value)) / 600.0
    ) RANGE '10m' AS rx_crc_errors_rate
  FROM ap_radio_rx_crc_errors_total
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL PREV
),
phy_err_rate AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    (
      (MAX(greptime_value) - MIN(greptime_value)) / 600.0
    ) RANGE '10m' AS rx_phy_errors_total
  FROM ap_radio_rx_phy_errors_total
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL PREV
),
data_pkt_rate AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    (
      (MAX(greptime_value) - MIN(greptime_value)) / 600.0
    ) RANGE '10m' AS data_pkt_rate
  FROM ap_radio_rx_data_packets_total
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL PREV
),
mgmt_pkt_rate AS (
  SELECT greptime_timestamp + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    (
      (MAX(greptime_value) - MIN(greptime_value)) / 600.0
    ) RANGE '10m' AS mgmt_pkt_rate
  FROM ap_radio_rx_mgmt_frames_total
  WHERE $__timeFilter(greptime_timestamp) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL PREV
),
sum_crc_err_rate AS (
  SELECT ts,
    SUM(rx_crc_errors_rate) AS total_crc_err_rate
  FROM crc_err_rate
  GROUP BY ts
),
sum_phy_err_rate AS (
  SELECT ts,
    SUM(rx_phy_errors_total) AS total_phy_err_rate
  FROM phy_err_rate
  GROUP BY ts
),
sum_data_pkt_rate AS (
  SELECT ts,
    SUM(data_pkt_rate) AS total_data_pkt_rate
  FROM data_pkt_rate
  GROUP BY ts
),
sum_mgmt_pkt_rate AS (
  SELECT ts,
    SUM(mgmt_pkt_rate) AS total_mgmt_pkt_rate
  FROM mgmt_pkt_rate
  GROUP BY ts
),
qoe_air_interface_score AS (
  SELECT sum_phy_err_rate.ts,
    (
      CASE
        WHEN total_data_pkt_rate + total_mgmt_pkt_rate == 0 THEN 0
        ELSE 1 - (total_phy_err_rate + total_crc_err_rate) / (
          total_data_pkt_rate + total_mgmt_pkt_rate + total_phy_err_rate + total_crc_err_rate
        )
      END
    ) AS err_rate
  FROM sum_phy_err_rate
    JOIN sum_crc_err_rate ON sum_phy_err_rate.ts = sum_crc_err_rate.ts
    JOIN sum_data_pkt_rate ON sum_phy_err_rate.ts = sum_data_pkt_rate.ts
    JOIN sum_mgmt_pkt_rate ON sum_phy_err_rate.ts = sum_mgmt_pkt_rate.ts
),
ap_radio_mcs_nss_efficiency_ratio AS (
  SELECT ts + INTERVAL '10 minute' AS ts,
    radio,
    AVG(ap_radio_rx_mcs_efficiency_ratio) RANGE '10m' AS rx_mcs_efficiency_ratio,
    AVG(ap_radio_tx_mcs_efficiency_ratio) RANGE '10m' AS tx_mcs_efficiency_ratio,
    FROM ap_radio_mcs_nss_qoes
  WHERE $__timeFilter(ts) $__extraFilter ALIGN '10m' BY (radio) FILL NULL
),
ap_radio_mcs_nss_symmetry AS (
  SELECT ts + INTERVAL '10 minute' AS ts,
    ap_mac,
    radio,
    AVG(
      ap_radio_mcs_symmetry * 0.5 + ap_radio_nss_symmetry * 0.5
    ) RANGE '10m' AS mcs_nss_symmetry
  FROM ap_radio_mcs_nss_qoes
  WHERE $__timeFilter(ts) $__extraFilter ALIGN '10m' BY (radio, ap_mac) FILL NULL
),
radio_scores AS (
  SELECT tx_packet.ts AS ts,
    tx_packet.radio AS radio,
    AVG( rx_mcs_efficiency_ratio * 0.5 + tx_mcs_efficiency_ratio * 0.5) AS efficiency,
    AVG(
      tx_packet.tx_packet_total / (
        tx_error.tx_error_total + tx_packet.tx_packet_total
      )
    ) AS success_rate,
    AVG(mcs_nss_symmetry) AS mcs_nss_symmetry
  FROM tx_packet
    JOIN tx_error ON tx_packet.ts = tx_error.ts
    AND tx_packet.radio = tx_error.radio
    JOIN ap_radio_mcs_nss_efficiency_ratio ON tx_packet.ts = ap_radio_mcs_nss_efficiency_ratio.ts
    AND tx_packet.radio = ap_radio_mcs_nss_efficiency_ratio.radio
    JOIN ap_radio_mcs_nss_symmetry ON tx_packet.ts = ap_radio_mcs_nss_symmetry.ts
    AND tx_packet.radio = ap_radio_mcs_nss_symmetry.radio
  GROUP BY tx_packet.ts,
    tx_packet.radio
),
scores AS (
  SELECT radio_scores.ts,
    AVG(
      CASE
        WHEN radio_scores.efficiency > 1 THEN 1
        ELSE radio_scores.efficiency
      END
    ) AS efficiency,
    AVG(
      CASE
        WHEN radio_scores.success_rate < 0 THEN 0
        ELSE radio_scores.success_rate
      END
    ) AS success_rate,
    AVG(radio_scores.mcs_nss_symmetry) AS symmetry_score
  FROM radio_scores
  GROUP BY radio_scores.ts
)
SELECT scores.ts,
  scores.efficiency AS throughput_efficiency,
  scores.success_rate AS throughput_success_rate,
  scores.symmetry_score AS throughput_symmetry_score,
  qoe_air_interface_score.err_rate AS throughput_err_rate,
  (
    (
      scores.success_rate + scores.efficiency + scores.symmetry_score + qoe_air_interface_score.err_rate
    ) / 4
  ) AS throughput_qoe
FROM scores
  JOIN qoe_air_interface_score ON scores.ts = qoe_air_interface_score.ts
WHERE $__timeFilter(scores.ts)
ORDER BY ts ASC;
