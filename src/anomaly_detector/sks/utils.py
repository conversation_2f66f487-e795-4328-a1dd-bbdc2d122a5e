from dataclasses import dataclass


@dataclass
class McsInfo:
    """MCS (Modulation and Coding Scheme) information dataclass"""

    mcs_index: int
    spatial_streams: int
    bandwidth: float
    input_rate: float
    matched_rate: float
    rate_difference: float
    standard: str
    modulation: str
    coding_rate: str


def get_mcs_index(bandwidth: float, rate: float) -> McsInfo | None:
    """Return the closest MCS index based on bandwidth and rate.
    Supports 802.11n/ac/ax standards, bandwidths of 20/40/80/160MHz.

    Args:
        bandwidth (float): Bandwidth in MHz (20, 40, 80, 160)
        rate (float): Rate in kbps

    Returns:
        McsInfo: Dataclass containing MCS information

    """
    # If the rate is very large, it might be in kbps, convert to Mbps
    rate_mbps = rate / 1000

    # Rate table based on actual data from MCS Index spreadsheet
    # Format: {bandwidth: [(mcs, spatial_streams, rate_mbps, standard, modulation, coding_rate), ...]}
    # Using 0.8µs GI values for consistency
    rate_table = {
        20: [
            # OFDM (Prior 11ax) - 1 Spatial Stream
            (0, 1, 6.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 1, 13.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 1, 19.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 1, 26.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 1, 39.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 1, 52.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 1, 58.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 1, 65.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 1, 78.0, "802.11n/ac", "256-QAM", "3/4"),
            # OFDM (Prior 11ax) - 2 Spatial Streams
            (0, 2, 13.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 2, 26.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 2, 39.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 2, 52.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 2, 78.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 2, 104.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 2, 117.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 2, 130.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 2, 156.0, "802.11n/ac", "256-QAM", "3/4"),
            # OFDM (Prior 11ax) - 3 Spatial Streams
            (0, 3, 19.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 3, 39.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 3, 58.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 3, 78.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 3, 117.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 3, 156.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 3, 175.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 3, 195.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 3, 234.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 3, 260.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 4 Spatial Streams
            (0, 4, 26.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 4, 52.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 4, 78.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 4, 104.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 4, 156.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 4, 208.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 4, 234.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 4, 260.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 4, 312.0, "802.11n/ac", "256-QAM", "3/4"),
            # 802.11ax - 1 Spatial Stream
            (0, 1, 8.6, "802.11ax", "BPSK", "1/2"),
            (1, 1, 17.2, "802.11ax", "QPSK", "1/2"),
            (2, 1, 25.8, "802.11ax", "QPSK", "3/4"),
            (3, 1, 34.4, "802.11ax", "16-QAM", "1/2"),
            (4, 1, 51.6, "802.11ax", "16-QAM", "3/4"),
            (5, 1, 68.8, "802.11ax", "64-QAM", "2/3"),
            (6, 1, 77.4, "802.11ax", "64-QAM", "3/4"),
            (7, 1, 86.0, "802.11ax", "64-QAM", "5/6"),
            (8, 1, 103.2, "802.11ax", "256-QAM", "3/4"),
            (9, 1, 114.7, "802.11ax", "256-QAM", "5/6"),
            (10, 1, 129.0, "802.11ax", "1024-QAM", "3/4"),
            (11, 1, 143.4, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 2 Spatial Streams
            (0, 2, 17.2, "802.11ax", "BPSK", "1/2"),
            (1, 2, 34.4, "802.11ax", "QPSK", "1/2"),
            (2, 2, 51.6, "802.11ax", "QPSK", "3/4"),
            (3, 2, 68.8, "802.11ax", "16-QAM", "1/2"),
            (4, 2, 103.2, "802.11ax", "16-QAM", "3/4"),
            (5, 2, 137.6, "802.11ax", "64-QAM", "2/3"),
            (6, 2, 154.9, "802.11ax", "64-QAM", "3/4"),
            (7, 2, 172.1, "802.11ax", "64-QAM", "5/6"),
            (8, 2, 206.5, "802.11ax", "256-QAM", "3/4"),
            (9, 2, 229.4, "802.11ax", "256-QAM", "5/6"),
            (10, 2, 258.1, "802.11ax", "1024-QAM", "3/4"),
            (11, 2, 286.8, "802.11ax", "1024-QAM", "5/6"),
        ],
        40: [
            # OFDM (Prior 11ax) - 1 Spatial Stream
            (0, 1, 13.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 1, 27.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 1, 40.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 1, 54.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 1, 81.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 1, 108.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 1, 121.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 1, 135.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 1, 162.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 1, 180.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 2 Spatial Streams
            (0, 2, 27.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 2, 54.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 2, 81.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 2, 108.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 2, 162.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 2, 216.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 2, 243.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 2, 270.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 2, 324.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 2, 360.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 3 Spatial Streams
            (0, 3, 40.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 3, 81.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 3, 121.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 3, 162.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 3, 243.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 3, 324.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 3, 364.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 3, 405.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 3, 486.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 3, 540.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 4 Spatial Streams
            (0, 4, 54.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 4, 108.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 4, 162.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 4, 216.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 4, 324.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 4, 432.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 4, 486.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 4, 540.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 4, 648.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 4, 720.0, "802.11n/ac", "256-QAM", "5/6"),
            # 802.11ax - 1 Spatial Stream
            (0, 1, 17.2, "802.11ax", "BPSK", "1/2"),
            (1, 1, 34.4, "802.11ax", "QPSK", "1/2"),
            (2, 1, 51.6, "802.11ax", "QPSK", "3/4"),
            (3, 1, 68.8, "802.11ax", "16-QAM", "1/2"),
            (4, 1, 103.2, "802.11ax", "16-QAM", "3/4"),
            (5, 1, 137.6, "802.11ax", "64-QAM", "2/3"),
            (6, 1, 154.9, "802.11ax", "64-QAM", "3/4"),
            (7, 1, 172.1, "802.11ax", "64-QAM", "5/6"),
            (8, 1, 206.5, "802.11ax", "256-QAM", "3/4"),
            (9, 1, 229.4, "802.11ax", "256-QAM", "5/6"),
            (10, 1, 258.1, "802.11ax", "1024-QAM", "3/4"),
            (11, 1, 286.8, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 2 Spatial Streams
            (0, 2, 34.4, "802.11ax", "BPSK", "1/2"),
            (1, 2, 68.8, "802.11ax", "QPSK", "1/2"),
            (2, 2, 103.2, "802.11ax", "QPSK", "3/4"),
            (3, 2, 137.6, "802.11ax", "16-QAM", "1/2"),
            (4, 2, 206.5, "802.11ax", "16-QAM", "3/4"),
            (5, 2, 275.3, "802.11ax", "64-QAM", "2/3"),
            (6, 2, 309.7, "802.11ax", "64-QAM", "3/4"),
            (7, 2, 344.1, "802.11ax", "64-QAM", "5/6"),
            (8, 2, 412.9, "802.11ax", "256-QAM", "3/4"),
            (9, 2, 458.8, "802.11ax", "256-QAM", "5/6"),
            (10, 2, 516.2, "802.11ax", "1024-QAM", "3/4"),
            (11, 2, 573.5, "802.11ax", "1024-QAM", "5/6"),
        ],
        80: [
            # OFDM (Prior 11ax) - 1 Spatial Stream
            (0, 1, 29.3, "802.11n/ac", "BPSK", "1/2"),
            (1, 1, 58.5, "802.11n/ac", "QPSK", "1/2"),
            (2, 1, 87.8, "802.11n/ac", "QPSK", "3/4"),
            (3, 1, 117.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 1, 175.5, "802.11n/ac", "16-QAM", "3/4"),
            (5, 1, 234.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 1, 263.3, "802.11n/ac", "64-QAM", "3/4"),
            (7, 1, 292.5, "802.11n/ac", "64-QAM", "5/6"),
            (8, 1, 351.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 1, 390.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 2 Spatial Streams
            (0, 2, 58.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 2, 117.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 2, 175.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 2, 234.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 2, 351.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 2, 468.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 2, 526.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 2, 585.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 2, 702.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 2, 780.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 3 Spatial Streams
            (0, 3, 87.8, "802.11n/ac", "BPSK", "1/2"),
            (1, 3, 175.5, "802.11n/ac", "QPSK", "1/2"),
            (2, 3, 263.3, "802.11n/ac", "QPSK", "3/4"),
            (3, 3, 351.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 3, 526.5, "802.11n/ac", "16-QAM", "3/4"),
            (5, 3, 702.0, "802.11n/ac", "64-QAM", "2/3"),
            (7, 3, 877.5, "802.11n/ac", "64-QAM", "5/6"),
            (8, 3, 1053.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 3, 1170.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 4 Spatial Streams
            (0, 4, 117.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 4, 234.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 4, 351.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 4, 468.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 4, 702.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 4, 936.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 4, 1053.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 4, 1170.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 4, 1404.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 4, 1560.0, "802.11n/ac", "256-QAM", "5/6"),
            # 802.11ax - 1 Spatial Stream
            (0, 1, 36.0, "802.11ax", "BPSK", "1/2"),
            (1, 1, 72.1, "802.11ax", "QPSK", "1/2"),
            (2, 1, 108.1, "802.11ax", "QPSK", "3/4"),
            (3, 1, 144.1, "802.11ax", "16-QAM", "1/2"),
            (4, 1, 216.2, "802.11ax", "16-QAM", "3/4"),
            (5, 1, 288.2, "802.11ax", "64-QAM", "2/3"),
            (6, 1, 324.3, "802.11ax", "64-QAM", "3/4"),
            (7, 1, 360.3, "802.11ax", "64-QAM", "5/6"),
            (8, 1, 432.4, "802.11ax", "256-QAM", "3/4"),
            (9, 1, 480.4, "802.11ax", "256-QAM", "5/6"),
            (10, 1, 540.4, "802.11ax", "1024-QAM", "3/4"),
            (11, 1, 600.5, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 2 Spatial Streams
            (0, 2, 72.1, "802.11ax", "BPSK", "1/2"),
            (1, 2, 144.1, "802.11ax", "QPSK", "1/2"),
            (2, 2, 216.2, "802.11ax", "QPSK", "3/4"),
            (3, 2, 288.2, "802.11ax", "16-QAM", "1/2"),
            (4, 2, 432.4, "802.11ax", "16-QAM", "3/4"),
            (5, 2, 576.5, "802.11ax", "64-QAM", "2/3"),
            (6, 2, 648.5, "802.11ax", "64-QAM", "3/4"),
            (7, 2, 720.6, "802.11ax", "64-QAM", "5/6"),
            (8, 2, 864.7, "802.11ax", "256-QAM", "3/4"),
            (9, 2, 960.8, "802.11ax", "256-QAM", "5/6"),
            (10, 2, 1080.9, "802.11ax", "1024-QAM", "3/4"),
            (11, 2, 1201.0, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 3 Spatial Streams
            (0, 3, 108.1, "802.11ax", "BPSK", "1/2"),
            (1, 3, 216.2, "802.11ax", "QPSK", "1/2"),
            (2, 3, 324.3, "802.11ax", "QPSK", "3/4"),
            (3, 3, 432.4, "802.11ax", "16-QAM", "1/2"),
            (4, 3, 648.5, "802.11ax", "16-QAM", "3/4"),
            (5, 3, 864.7, "802.11ax", "64-QAM", "2/3"),
            (6, 3, 972.8, "802.11ax", "64-QAM", "3/4"),
            (7, 3, 1080.9, "802.11ax", "64-QAM", "5/6"),
            (8, 3, 1297.1, "802.11ax", "256-QAM", "3/4"),
            (9, 3, 1441.2, "802.11ax", "256-QAM", "5/6"),
            (10, 3, 1621.3, "802.11ax", "1024-QAM", "3/4"),
            (11, 3, 1801.5, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 4 Spatial Streams
            (0, 4, 144.1, "802.11ax", "BPSK", "1/2"),
            (1, 4, 288.2, "802.11ax", "QPSK", "1/2"),
            (2, 4, 432.4, "802.11ax", "QPSK", "3/4"),
            (3, 4, 576.5, "802.11ax", "16-QAM", "1/2"),
            (4, 4, 864.7, "802.11ax", "16-QAM", "3/4"),
            (5, 4, 1152.9, "802.11ax", "64-QAM", "2/3"),
            (6, 4, 1297.1, "802.11ax", "64-QAM", "3/4"),
            (7, 4, 1441.2, "802.11ax", "64-QAM", "5/6"),
            (8, 4, 1729.4, "802.11ax", "256-QAM", "3/4"),
            (9, 4, 1921.6, "802.11ax", "256-QAM", "5/6"),
            (10, 4, 2161.8, "802.11ax", "1024-QAM", "3/4"),
            (11, 4, 2402.0, "802.11ax", "1024-QAM", "5/6"),
        ],
        160: [
            # OFDM (Prior 11ax) - 1 Spatial Stream
            (0, 1, 58.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 1, 117.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 1, 175.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 1, 234.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 1, 351.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 1, 468.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 1, 526.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 1, 585.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 1, 702.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 1, 780.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 2 Spatial Streams
            (0, 2, 117.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 2, 234.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 2, 351.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 2, 468.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 2, 702.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 2, 936.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 2, 1053.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 2, 1170.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 2, 1404.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 2, 1560.0, "802.11n/ac", "256-QAM", "5/6"),
            # OFDM (Prior 11ax) - 3 Spatial Streams
            (0, 3, 175.5, "802.11n/ac", "BPSK", "1/2"),
            (1, 3, 351.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 3, 526.5, "802.11n/ac", "QPSK", "3/4"),
            (3, 3, 702.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 3, 1053.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 3, 1404.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 3, 1579.5, "802.11n/ac", "64-QAM", "3/4"),
            (7, 3, 1755.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 3, 2106.0, "802.11n/ac", "256-QAM", "3/4"),
            # OFDM (Prior 11ax) - 4 Spatial Streams
            (0, 4, 234.0, "802.11n/ac", "BPSK", "1/2"),
            (1, 4, 468.0, "802.11n/ac", "QPSK", "1/2"),
            (2, 4, 702.0, "802.11n/ac", "QPSK", "3/4"),
            (3, 4, 936.0, "802.11n/ac", "16-QAM", "1/2"),
            (4, 4, 1404.0, "802.11n/ac", "16-QAM", "3/4"),
            (5, 4, 1872.0, "802.11n/ac", "64-QAM", "2/3"),
            (6, 4, 2106.0, "802.11n/ac", "64-QAM", "3/4"),
            (7, 4, 2340.0, "802.11n/ac", "64-QAM", "5/6"),
            (8, 4, 2808.0, "802.11n/ac", "256-QAM", "3/4"),
            (9, 4, 3120.0, "802.11n/ac", "256-QAM", "5/6"),
            # 802.11ax - 1 Spatial Stream
            (0, 1, 72.1, "802.11ax", "BPSK", "1/2"),
            (1, 1, 144.1, "802.11ax", "QPSK", "1/2"),
            (2, 1, 216.2, "802.11ax", "QPSK", "3/4"),
            (3, 1, 288.2, "802.11ax", "16-QAM", "1/2"),
            (4, 1, 432.4, "802.11ax", "16-QAM", "3/4"),
            (5, 1, 576.5, "802.11ax", "64-QAM", "2/3"),
            (6, 1, 648.5, "802.11ax", "64-QAM", "3/4"),
            (7, 1, 720.6, "802.11ax", "64-QAM", "5/6"),
            (8, 1, 864.7, "802.11ax", "256-QAM", "3/4"),
            (9, 1, 960.8, "802.11ax", "256-QAM", "5/6"),
            (10, 1, 1080.9, "802.11ax", "1024-QAM", "3/4"),
            (11, 1, 1201.0, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 2 Spatial Streams
            (0, 2, 144.1, "802.11ax", "BPSK", "1/2"),
            (1, 2, 288.2, "802.11ax", "QPSK", "1/2"),
            (2, 2, 432.4, "802.11ax", "QPSK", "3/4"),
            (3, 2, 576.5, "802.11ax", "16-QAM", "1/2"),
            (4, 2, 864.7, "802.11ax", "16-QAM", "3/4"),
            (5, 2, 1152.9, "802.11ax", "64-QAM", "2/3"),
            (6, 2, 1297.1, "802.11ax", "64-QAM", "3/4"),
            (7, 2, 1441.2, "802.11ax", "64-QAM", "5/6"),
            (8, 2, 1729.4, "802.11ax", "256-QAM", "3/4"),
            (9, 2, 1921.6, "802.11ax", "256-QAM", "5/6"),
            (10, 2, 2161.8, "802.11ax", "1024-QAM", "3/4"),
            (11, 2, 2402.0, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 3 Spatial Streams
            (0, 3, 216.2, "802.11ax", "BPSK", "1/2"),
            (1, 3, 432.4, "802.11ax", "QPSK", "1/2"),
            (2, 3, 648.5, "802.11ax", "QPSK", "3/4"),
            (3, 3, 864.7, "802.11ax", "16-QAM", "1/2"),
            (4, 3, 1297.1, "802.11ax", "16-QAM", "3/4"),
            (5, 3, 1729.4, "802.11ax", "64-QAM", "2/3"),
            (6, 3, 1945.6, "802.11ax", "64-QAM", "3/4"),
            (7, 3, 2161.8, "802.11ax", "64-QAM", "5/6"),
            (8, 3, 2594.1, "802.11ax", "256-QAM", "3/4"),
            (9, 3, 2882.4, "802.11ax", "256-QAM", "5/6"),
            (10, 3, 3242.7, "802.11ax", "1024-QAM", "3/4"),
            (11, 3, 3603.0, "802.11ax", "1024-QAM", "5/6"),
            # 802.11ax - 4 Spatial Streams
            (0, 4, 288.2, "802.11ax", "BPSK", "1/2"),
            (1, 4, 576.5, "802.11ax", "QPSK", "1/2"),
            (2, 4, 864.7, "802.11ax", "QPSK", "3/4"),
            (3, 4, 1152.9, "802.11ax", "16-QAM", "1/2"),
            (4, 4, 1729.4, "802.11ax", "16-QAM", "3/4"),
            (5, 4, 2305.9, "802.11ax", "64-QAM", "2/3"),
            (6, 4, 2594.1, "802.11ax", "64-QAM", "3/4"),
            (7, 4, 2882.4, "802.11ax", "64-QAM", "5/6"),
            (8, 4, 3458.8, "802.11ax", "256-QAM", "3/4"),
            (9, 4, 3843.2, "802.11ax", "256-QAM", "5/6"),
            (10, 4, 4323.6, "802.11ax", "1024-QAM", "3/4"),
            (11, 4, 4804.0, "802.11ax", "1024-QAM", "5/6"),
        ],
    }

    # Check if the bandwidth is supported
    if bandwidth not in rate_table:
        return None

    # Find the closest MCS configuration
    rates = rate_table[bandwidth]
    best_match = None
    min_diff = float("inf")

    for mcs, ss, expected_rate, standard, modulation, coding_rate in rates:
        diff = abs(rate_mbps - expected_rate)
        if diff < min_diff:
            min_diff = diff
            best_match = McsInfo(
                mcs_index=mcs,
                spatial_streams=ss,
                bandwidth=bandwidth,
                input_rate=rate_mbps,
                matched_rate=expected_rate,
                rate_difference=diff,
                standard=standard,
                modulation=modulation,
                coding_rate=coding_rate,
            )

    return best_match
