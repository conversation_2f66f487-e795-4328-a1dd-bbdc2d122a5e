def prom_metric_name(anomaly_detector_id: str, name: str):
    return f"log_anomaly:{name}:{anomaly_detector_id}".replace("-", "_")


def metric_str_to_column_tuple(name: str):
    split = name.split(":")
    if len(split) >= 2:
        marker_type = split[0]
        marker_name = ":".join(split[1:])
        return marker_type, marker_name
    return name, None


def column_tuple_to_str(col: tuple[str, str | None]):
    marker_type, name = col
    if name is None:
        return marker_type
    return f"{marker_type}:{name}"


def measurement_name(anomaly_detector_id: str):
    return f"rising_edges_correlation_{anomaly_detector_id}"
