import os
from dataclasses import dataclass
from functools import cached_property, lru_cache

import s3path
from pydantic import Field, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict


class SagemakerSettings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="SAGEMAKER_")
    aws_region: str = ""
    access_key: str = ""
    secret_key: str = ""
    execution_role: str = ""
    bucket: str = ""
    s3_prefix: str = "logbert"

    @computed_field
    @cached_property
    def aws_path(self) -> s3path.S3Path:
        return s3path.S3Path(f"/{self.bucket}/{self.s3_prefix}")


class ConsulSettings(BaseSettings):
    consul_ip: str = "consul.service.ffwd"
    consul_port: int = 8500

    @property
    def url(self) -> str:
        return f"http://{self.consul_ip}:{self.consul_port}"


@dataclass
class ServiceConfig:
    service: str
    tag: str | None = None
    static_url: str | None = None
    env_var: str | None = None


greptime_service_name = os.environ.get("GREPTIME_SERVICE_NAME", "greptime")


class ServicesSettings(BaseSettings):
    loki_frontend: ServiceConfig = ServiceConfig(service="ffwd-loki", tag="query-frontend", env_var="LOKI_FRONTEND_URL")
    greptime: ServiceConfig = ServiceConfig(service=greptime_service_name, env_var="GREPTIME_URL")
    llamington: ServiceConfig = ServiceConfig(service="ffwd-llamington", env_var="LLAMINGTON_URL")
    mimir_proxy: ServiceConfig = ServiceConfig(service="mimir-proxy-http", env_var="MIMIR_PROXY_URL")
    metronome: ServiceConfig = ServiceConfig(service="ffwd-metronome", env_var="METRONOME_URL")
    qdrant: ServiceConfig = ServiceConfig(service="qdrant", env_var="QDRANT_URL")
    mimir_frontend: ServiceConfig = ServiceConfig(
        service="ffwd-mimir",
        tag="query-frontend",
        env_var="MIMIR_FRONTEND_URL",
    )
    s3: ServiceConfig = ServiceConfig(service="garage", env_var="S3_URL")
    ollama: ServiceConfig = ServiceConfig(service="ollama")


class LLMSettings(BaseSettings):
    openai_api_key: str = ""
    gemini_api_key: str = ""
    openai_base_url: str = ""
    google_vertex_credential: str = ""
    openrouter_api_key: str = ""
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    openai_embedding_base_url: str = ""
    openai_embedding_api_key: str = ""
    groq_api_key: str = ""
    groq_base_url: str = "https://api.groq.com/openai/v1"


class GrepTimeSettings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="GREPTIME_")
    db: str = "pythia"
    log_db: str = "logs"
    sks_db: str = ""


class NomadJobSettings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="NOMAD_")
    job_id: str = "pythia"
    alloc_id: str = "local"

    @computed_field
    @cached_property
    def name(self) -> str:
        return f"{self.job_id}-{self.alloc_id}"


class PostgresSettings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="POSTGRES_")
    host: str = "localhost"
    port: int = 5432
    user: str = "root"
    password: str = Field(default="", alias="POSTGRES_PASS")
    name: str = "pythia"


class S3Settings(BaseSettings):
    bucket: str = Field(default="", alias="SERVICES_BUCKET")
    username: str = Field(default="", alias="S3_USER")
    password: str = Field(default="", alias="S3_PASS")
    region: str = "us-east-1"

    @computed_field
    @cached_property
    def base_path(self) -> s3path.S3Path:
        return s3path.S3Path(f"/{self.bucket}/pythia")


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", case_sensitive=False, extra="ignore")

    port: int = 3000
    mimir_enabled: bool = False
    is_sks_env: bool = False
    greptime_logs_enabled: bool = False
    disable_scheduler: bool = False
    nomad_alloc_id: str = "local"
    job_threads: int = 20
    use_thread_executor: bool = False
    app_name: str = "pythia"

    log_level: str = "INFO"

    consul: ConsulSettings = ConsulSettings()

    s3: S3Settings = S3Settings()
    llm: LLMSettings = LLMSettings()
    postgres: PostgresSettings = PostgresSettings()
    nomad: NomadJobSettings = NomadJobSettings()
    sagemaker: SagemakerSettings = SagemakerSettings()
    greptime: GrepTimeSettings = GrepTimeSettings()
    services: ServicesSettings = ServicesSettings()


@lru_cache
def get_settings():
    return Settings()


TIMEZONE = os.environ.get("TIMEZONE", "Asia/Tokyo")
