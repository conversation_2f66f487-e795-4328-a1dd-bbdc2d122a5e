DATE_FORMAT = "%Y-%m-%d"
DATETIME_FORMAT = "%Y-%m-%d-%H-%M"
TIMESTAMP_KEY = '["timestamp"]'
ORIGINAL_TIMESTAMP_KEY = "timestamp"
TEMPLATE_KEY = '["template_id"]'
ORIGINAL_TEMPLATE_KEY = "template_id"
LOG_SEQ_ID_KEY = '["_reserved_log_seq_id"]'
ORIGINAL_LOG_SEQ_ID_KEY = "_reserved_log_seq_id"
SPECIAL_KEYS = [TEMPLATE_KEY, LOG_SEQ_ID_KEY, TIMESTAMP_KEY]
ORIGINAL_SPECIAL_KEYS = [ORIGINAL_TEMPLATE_KEY, ORIGINAL_LOG_SEQ_ID_KEY, ORIGINAL_TIMESTAMP_KEY]

DF_TIMESTAMP_COL = "timestamp"

PANEL_METRIC_NAME_MAPPING = {
    "log_structure": "log_structure",
    "sequence_pattern": "sequence_pattern",
    "numeric_fields": "numeric_clusters",
    "discrete_fields": "discrete_values",
    "notable_fields": "prominent_fields",
    "log_rate": "log_rate",
}

PARTIAL_CONFIG_KEYWORDS = {
    "config",
    "updat",
    "modif",
    "chang",
    "delete",
    "remov",
    "commit",
    "save",
    "activat",
    "enabl",
    "disabl",
    "provision",
    "deploy",
    "initializ",
    "initialis",
    "install",
    "stop",
    "start",
    "replac",
    "assign",
    "allocat",
    "resize",
    "creat",
    "alter",
    "grant",
    "revok",
    "rewrit",
    "launch",
    "adjustment",
    "tweak",
    "customize",
    "migrate",
    "upgrade",
    "patch",
    "rollout",
    "setup",
    "schedul",
    "maintenance",
    "backup",
    "restore",
    "directiv",
    "polic",
    "rule",
    "preferences",
    "constraints",
    "restrictions",
}

WHOLE_CONFIG_KEYWORDS = {
    "fix",
    "hotfix",
    "set",
    "adapt",
    "tune",
    "tuning",
    "setting",
    "recover",
    "edit",
    "editing",
    "edited",
    "add",
    "adding",
    "added",
    "apply",
    "rename",
    "renaming",
    "move",
    "moved",
    "moving",
    "define",
    "defined",
    "defining",
    "unset",
    "scale",
    "scaling",
    "scaled",
    "resizing",
    "drop",
    "dropped",
    "dropping",
    "binding",
}

PARTIAL_ERROR_KEYWORDS = {
    "abort",
    "crash",
    "warn",
    "fail",
    "fault",
    "unhandle",
    "alert",
    "refuse",
    "authorize",
    "authorise",
    "suspect",
    "terminat",
    "exploit",
    "bypass",
    "exhaust",
    "throttl",
    "quarantine",
    "unavailable",
    "invalid",
    "conflict",
    "spoof",
}

WHOLE_ERROR_KEYWORDS = {
    "err",
    "error",
    "timeout",
    "exception",
    "expired",
    "critical",
    "crit",
    "emergency",
    "problem",
    "issue",
    "hang",
    "forbid",
    "forbidden",
    "corrupt",
    "malformed",
    "dead",
    "lock",
    "locking",
    "freeze",
    "frozen",
    "denied",
    "deny",
    "suspicious",
    "malware",
    "virus",
    "intrusion",
    "attack",
    "full",
    "high",
    "slow",
    "unreachable",
    "nomem",
    "overflow",
    "dump",
    "segmentation",
    "unresponsive",
    "stale",
    "blacklist",
    "desync",
    "shut",
    "down",
    "patch",
    "low",
    "rollback",
    "offline",
}
