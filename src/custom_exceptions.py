class ServiceError(Exception):
    """issue contacting one of the services for which a URL was provided,
    we'll clear the URL cache for those services and try again
    """

    def __init__(self, message: str, *, service: str | None = None, tag: str | None = None):
        super().__init__(message)
        self.service = service
        self.tag = tag

    def __str__(self):
        return f"{super().__str__()} (service={self.service}, tag={self.tag})"


class RecoverableError(Exception):
    """will try again later"""


class TableNotFoundError(Exception):
    pass
