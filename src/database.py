import orjson
import structlog
from asyncpg import Connection, Pool, create_pool

from config import get_settings

logger = structlog.get_logger(__name__)


async def _init_conn(conn: Connection):
    await conn.set_type_codec(
        "jsonb",
        encoder=lambda v: orjson.dumps(v).decode(),
        decoder=orjson.loads,
        schema="pg_catalog",
    )


async def get_db_connection_pool() -> Pool:
    settings = get_settings()
    pg_setting = settings.postgres
    logger.debug("Connecting to database", pg_setting=pg_setting)
    return await create_pool(
        database=pg_setting.name,
        user=pg_setting.user,
        password=pg_setting.password,
        host=pg_setting.host,
        port=pg_setting.port,
        init=_init_conn,
        min_size=2,
    )


def parse_command_complete(msg: str):
    cmd, count = msg.split(" ")
    try:
        count = int(count)
    except ValueError:
        count = 0
    return {cmd: count}


def is_updated(msg: str):
    p = parse_command_complete(msg)
    return p.get("UPDATE", 0) > 0
