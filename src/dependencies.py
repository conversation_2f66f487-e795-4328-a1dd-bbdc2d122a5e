from collections.abc import AsyncGenerator
from typing import Annotated, cast

import httpx
from asyncpg import Connection, Pool
from fastapi import Request
from fastapi.params import Depends

from config import Settings, get_settings
from greptime import AsyncGreptimeClient
from services import ServicesRegistry


async def db_conn(request: Request) -> AsyncGenerator[Connection, None]:
    db_pool = cast(Pool, request.state.db_pool)
    async with db_pool.acquire() as conn:
        yield conn


async def db_txn(request: Request) -> AsyncGenerator[Connection, None]:
    db_pool = cast(Pool, request.state.db_pool)
    async with db_pool.acquire() as conn, conn.transaction():
        yield conn


async def get_svc_registry(request: Request) -> ServicesRegistry:
    return cast(ServicesRegistry, request.state.service_registry)


async def get_async_greptime(
    request: Request,
    settings: Annotated[Settings, Depends(get_settings)],
) -> AsyncGreptimeClient:
    svc_registry = cast(ServicesRegistry, request.state.service_registry)
    greptime_url = svc_registry.service("greptime").url()
    return AsyncGreptimeClient(greptime_url, settings.greptime.db)


async def get_async_greptime_promql_client(
    svc_registry: Annotated[ServicesRegistry, Depends(get_svc_registry)],
) -> httpx.AsyncClient:
    return httpx.AsyncClient(base_url=f"{svc_registry.service('greptime').url()}/v1/prometheus/api/v1")


async def get_sks_async_greptime(request: Request) -> AsyncGreptimeClient:
    svc_registry = cast(ServicesRegistry, request.state.service_registry)
    greptime_url = svc_registry.service("greptime").url()
    return AsyncGreptimeClient(greptime_url, "sks")
