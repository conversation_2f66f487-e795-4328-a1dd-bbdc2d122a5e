import re
from collections import defaultdict
from collections.abc import Iterable
from typing import Any, cast

import httpx
import numpy as np
import orjson
import structlog
from httpx import Response
from pendulum import DateTime
from pypika import Criterion, Order, Table

from custom_exceptions import TableNotFoundError

GREPTIME_TABLE_NOT_FOUND_ERROR_CODE = 4001


def greptime_sql_response_metrics(
    response,
    timestamp_field: str = "greptime_timestamp",
    value_field: str = "greptime_value",
    timestamp_unit: str = "ms",
    exclude_all_labels: bool = False,
    exclude_fields: list[str] | None = None,
    label_fields: list[str] | None = None,
    end_time: DateTime | None = None,
    convert_value_str: bool = True,
) -> list[dict[str, Any]]:
    if not response:
        return []
    exclude_keys = [timestamp_field, "inference", value_field]
    if exclude_fields:
        exclude_keys.extend(exclude_fields)

    result = defaultdict(list)

    for item in response:
        if label_fields:
            labels = {k: v for k, v in item.items() if k in label_fields}
        elif exclude_all_labels:
            labels = {}
        else:
            labels = {k: v for k, v in item.items() if k not in exclude_keys}
        ts = item[timestamp_field]
        if timestamp_unit == "ms":
            ts = ts / 1000
        elif timestamp_unit == "ns":
            ts = ts / 1000000000
        if end_time and ts > end_time.timestamp():
            continue
        value = item.get(value_field, None)
        if value is None:
            continue
        value = [ts, str(value) if convert_value_str else value]
        key = orjson.dumps(labels, option=orjson.OPT_SORT_KEYS)
        result[key].append(value)

    return [{"labels": orjson.loads(k), "values": sorted(v, key=lambda x: x[0])} for k, v in result.items()]


def greptime_timestamp_sql(start: DateTime, end: DateTime, sql: str, extra_filters: dict | None = None):
    start_iso = start.timestamp() * 1000
    end_iso = end.timestamp() * 1000
    if extra_filters:
        extra_where_clauses = "AND " + " AND ".join([f"{k}='{v}'" for k, v in (extra_filters or {}).items()])
    else:
        extra_where_clauses = ""
    q = re.sub(
        r"\$__timeFilter\((.*?)\)",
        f"\\1 BETWEEN {start_iso}::TimestampMillisecond AND {end_iso}::TimestampMillisecond",
        sql,
    )
    return re.sub(r"\$__extraFilter", extra_where_clauses, q)


def greptime_prom_response_metrics(
    response,
    exclude_columns: list[str] | None = None,
    *,
    timestamp_column="greptime_timestamp",
    value_column="greptime_value",
):
    if not response:
        return []
    exclude_columns = exclude_columns or []

    result = defaultdict(list)

    for item in response:
        labels = {k: v for k, v in item.items() if k not in exclude_columns}
        value = [item[timestamp_column], item[value_column]]
        key = orjson.dumps(labels, option=orjson.OPT_SORT_KEYS)
        result[key].append(value)

    return [{"labels": orjson.loads(k), "values": v} for k, v in result.items()]


def build_greptime_log_sql(
    table: Table,
    start: DateTime | None = None,
    end: DateTime | None = None,
    conditions: list | None = None,
    limit: int = 1000,
    timestamp_field: str = "ts",
):
    c = conditions or []
    if start:
        c.append(table[timestamp_field] >= start)
    if end:
        c.append(table[timestamp_field] <= end)
    return table.select("*").where(Criterion.all(c)).orderby(table[timestamp_field], order=Order.desc).limit(limit)


def _parse_greptime_response(response: dict[str, Any]) -> list[Any]:
    results = []

    for output in response.get("output", []):
        # If no records in output, add the raw output
        if "records" not in output:
            results.append(output)
            continue

        records = output["records"]
        schema = records["schema"]["column_schemas"]
        column_names = [col_schema["name"] for col_schema in schema]
        rows = records["rows"]

        results.append([dict(zip(column_names, row, strict=False)) for row in rows])

    return results


class GreptimeClient:
    def __init__(self, url: str, db: str, async_mode: bool = False, timeout=120):
        self._headers = {"X-Greptime-DB-Name": db}
        self._url = url
        self._async_mode = async_mode
        self._client: httpx.Client | httpx.AsyncClient = (
            httpx.AsyncClient(base_url=url, headers=self._headers, timeout=timeout)
            if async_mode
            else httpx.Client(base_url=url, headers=self._headers, timeout=timeout)
        )
        self._logger = structlog.get_logger("greptime_client", url=url, db=db)

    @property
    def is_async(self) -> bool:
        return self._async_mode

    async def aclose(self) -> None:
        if self.is_async:
            await cast(httpx.AsyncClient, self._client).aclose()

    def close(self) -> None:
        if not self.is_async:
            cast(httpx.Client, self._client).close()

    async def execute(self, query: Any) -> list[dict[str, Any]]:
        if not self.is_async:
            raise ValueError("This method can only be called in async mode")

        sql = str(query)
        response = await cast(httpx.AsyncClient, self._client).post("/v1/sql", data={"sql": sql})
        res = response.json()
        if not response.is_success:
            self._logger.warning(
                "greptime_execute_error",
                sql=sql,
                status_code=response.status_code,
                response=response.text,
            )
            if res.get("code") and res["code"] == GREPTIME_TABLE_NOT_FOUND_ERROR_CODE:
                raise TableNotFoundError(res["error"])
        response.raise_for_status()

        parsed_results = _parse_greptime_response(response.json())
        return parsed_results[0] if parsed_results else []

    def execute_sync(self, query: Any) -> list[dict[str, Any]]:
        if self.is_async:
            raise ValueError("This method can only be called in sync mode")

        sql = str(query)
        response = cast(httpx.Client, self._client).post("/v1/sql", data={"sql": sql})
        res = response.json()
        if not response.is_success:
            self._logger.warning(
                "greptime_execute_error",
                sql=sql,
                status_code=response.status_code,
                response=response.text,
            )
            if res.get("code") and res["code"] == GREPTIME_TABLE_NOT_FOUND_ERROR_CODE:
                raise TableNotFoundError(res["error"])
        response.raise_for_status()

        parsed_results = _parse_greptime_response(response.json())
        return parsed_results[0] if parsed_results else []

    async def batch_execute(self, queries: Iterable[Any]) -> list[Any]:
        if not self.is_async:
            raise ValueError("This method can only be called in async mode")

        sql_statements = [str(query) for query in queries]
        response = await cast(httpx.AsyncClient, self._client).post("/v1/sql", data={"sql": ";".join(sql_statements)})
        response.raise_for_status()

        return _parse_greptime_response(response.json())

    def batch_execute_sync(self, queries: Iterable[Any]) -> list[Any]:
        if self.is_async:
            raise ValueError("This method can only be called in sync mode")

        sql_statements = [str(query) for query in queries]
        response = cast(httpx.Client, self._client).post("/v1/sql", data={"sql": ";".join(sql_statements)})
        response.raise_for_status()

        return _parse_greptime_response(response.json())

    async def push_influxdb(
        self,
        table_name: str,
        points: list[dict[str, str | float | int]],
        timestamp_field: str = "timestamp",
    ) -> Response:
        if not self.is_async:
            raise ValueError("This method can only be called in async mode")

        data = self._prepare_influxdb_data(table_name, points, timestamp_field)
        response = await cast(httpx.AsyncClient, self._client).post(
            "/v1/influxdb/api/v2/write?",
            content=data,
            params={"precision": "ms"},
        )
        response.raise_for_status()

        return response

    def push_influxdb_sync(
        self,
        table_name: str,
        points: list[dict[str, str | float | int]],
        timestamp_field: str = "timestamp",
    ) -> Response:
        if self.is_async:
            raise ValueError("This method can only be called in sync mode")

        data = self._prepare_influxdb_data(table_name, points, timestamp_field)
        response = cast(httpx.Client, self._client).post(
            "/v1/influxdb/api/v2/write?",
            content=data,
            params={"precision": "ms"},
        )
        response.raise_for_status()

        return response

    def _prepare_influxdb_data(
        self,
        table_name: str,
        points: list[dict[str, str | float | int]],
        timestamp_field: str,
    ) -> str:
        lines = []

        for point in points:
            # Skip points without timestamp
            if timestamp_field not in point:
                continue

            ts = point[timestamp_field]

            # Separate string values (labels) from numeric values
            labels = [
                f"{key}={value}" for key, value in point.items() if key != timestamp_field and isinstance(value, str)
            ]

            values = [
                f"{key}={value:f}"
                for key, value in point.items()
                if key != timestamp_field and not isinstance(value, str) and not np.isnan(value)
            ]

            # Build the line protocol string
            line_parts = [table_name]

            if labels:
                line_parts[0] += f",{','.join(labels)}"

            if values:
                line_parts.append(",".join(values))

            line_parts.append(str(int(ts)))

            lines.append(" ".join(line_parts))

        # Join all lines
        return "\n".join(lines)


class AsyncGreptimeClient:
    def __init__(self, url: str, db: str, timeout=120):
        self._client = GreptimeClient(url, db, async_mode=True, timeout=timeout)

    async def close(self) -> None:
        await self._client.aclose()

    async def execute(self, query: Any) -> list[dict[str, Any]]:
        return await self._client.execute(query)

    async def batch_execute(self, queries: Iterable[Any]) -> list[Any]:
        return await self._client.batch_execute(queries)

    async def push_influxdb(
        self,
        table_name: str,
        points: list[dict[str, str | float | int]],
        timestamp_field: str = "timestamp",
    ) -> Response:
        return await self._client.push_influxdb(table_name, points, timestamp_field)


class SyncGreptimeClient:
    def __init__(self, url: str, db: str, timeout=60):
        self._client = GreptimeClient(url, db, async_mode=False, timeout=timeout)

    def close(self) -> None:
        self._client.close()

    def execute(self, query: Any) -> list[dict[str, Any]]:
        return self._client.execute_sync(query)

    def batch_execute(self, queries: list[Any]) -> list[Any]:
        return self._client.batch_execute_sync(queries)

    def push_influxdb(
        self,
        table_name: str,
        points: list[dict[str, str | float | int]],
        timestamp_field: str = "timestamp",
    ) -> Response:
        return self._client.push_influxdb_sync(table_name, points, timestamp_field)


def retrieve_original_logs_from_greptime(
    greptime_client: SyncGreptimeClient,
    greptime_table: str,
    flow_id: str,
    start: DateTime,
    end: DateTime,
    log_seq_ids: list[str],
):
    table = Table(greptime_table)
    conditions = [
        table.flow_id == flow_id,
        table.seq_id.isin(log_seq_ids),
    ]
    sql = build_greptime_log_sql(table, start, end, conditions)
    return greptime_client.execute(sql)
