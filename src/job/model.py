from enum import Str<PERSON><PERSON>
from typing import Annotated
from uuid import UUID

import typing_extensions
from pydantic import BaseModel, BeforeValidator, ConfigDict, Field, PlainSerializer
from pydantic_extra_types.pendulum_dt import DateTime
from pypika import Table

from utils import to_kebab

UTCISODatetime = Annotated[DateTime, PlainSerializer(lambda x: x.in_tz("UTC").to_iso8601_string())]


class BaseKebabModel(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,
        alias_generator=to_kebab,
        extra="allow",
        populate_by_name=True,
    )

    def kebab_dump(self, **kwargs):
        return self.model_dump(by_alias=True, exclude_none=True, **kwargs)


class RegexExcludedField(typing_extensions.TypedDict):
    regex: str


class AnomalyDetectorLogFlow(BaseModel):
    model_config = ConfigDict(alias_generator=to_kebab)
    flow_id: str
    log_rate_metric_name: str | None = None
    log_rate_prophecy_id: str | None = None


class AnomalyDetectorMetricFlowProphecy(BaseModel):
    model_config = ConfigDict(alias_generator=to_kebab)
    prophecy_id: str
    metric_name: str
    query: str


class AnomalyDetectorMetricFlow(BaseModel):
    model_config = ConfigDict(alias_generator=to_kebab)
    flow_id: str
    prophecies: list[AnomalyDetectorMetricFlowProphecy] = Field(default_factory=list)


class BaseAnomalyDetectorConfig(BaseKebabModel):
    initial_trained_date: UTCISODatetime | None = None
    flow_id: str | None = None  # deprecated
    log_rate_metric: str | None = None  # deprecated
    prophecy_id: str | None = None  # deprecated
    metric_flows: list[AnomalyDetectorMetricFlow] = Field(default_factory=list)
    log_flows: list[AnomalyDetectorLogFlow] = Field(default_factory=list)
    scheduled_train_date: UTCISODatetime | None = None
    skip_public_holidays: bool = False
    trained: bool = False
    trained_date: UTCISODatetime | None = None
    inference_interval_minutes: int | None = None
    full_inference: bool | None = None
    retrained_date: UTCISODatetime | None = None
    skip_log_volume_profile: bool = False
    express_training: bool = False
    excluded_fields: list[str | RegexExcludedField] = []
    deleted: bool | None = None


class AnomalyDetectorConfig(BaseAnomalyDetectorConfig):
    pass


class GreptimeAnomalyDetectorConfig(BaseAnomalyDetectorConfig):
    is_greptime: bool = True
    db: str | None = None


class BaseProphecyConfig(BaseKebabModel):
    predicted: UTCISODatetime | None = None
    trained: bool = False


class ProphecyConfig(BaseProphecyConfig):
    query: str
    multi_series: bool = False


class GreptimeProphecyConfig(BaseProphecyConfig):
    sql: str
    timestamp_column: str
    value_column: str
    multi_series: bool = False
    extra_filters: dict | None = None


class SKSMcsNssConfig(BaseKebabModel):
    pass


class JobStatus(StrEnum):
    pending = "pending"
    running = "running"
    failed = "failed"
    completed = "completed"


class JobType(StrEnum):
    log_anomaly = "log-anomaly"
    prophecy = "prophecy"
    sks_mcs_nss = "sks-mcs-nss"


class JobModel(BaseModel):
    model_config = ConfigDict(from_attributes=True, use_enum_values=True)
    id: UUID | None = None
    tenant_id: Annotated[str, BeforeValidator(str)] = ""
    job_source_id: Annotated[str | None, BeforeValidator(str)] = None
    type: JobType | None = None
    config: (
        AnomalyDetectorConfig
        | ProphecyConfig
        | GreptimeAnomalyDetectorConfig
        | GreptimeProphecyConfig
        | SKSMcsNssConfig
        | None
    )
    status: JobStatus = JobStatus.pending
    errors: list[dict] | None = None
    next_run: UTCISODatetime | None = None
    run_started: UTCISODatetime | None = None
    alloc: str | None = None
    attempts: int = 0
    cost: int = 100
    created: UTCISODatetime | None = None


table = Table("jobs")
