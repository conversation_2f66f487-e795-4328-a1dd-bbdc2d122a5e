from collections.abc import Callable

from pendulum import Duration

from job.model import JobModel


class BaseJob:
    job_record: JobModel

    async def prepare_args(self) -> tuple | dict:
        return ()

    def run(self) -> Callable:
        raise NotImplementedError

    async def on_done(self):
        return

    async def on_failed(self):
        pass

    async def on_success(self):
        pass

    async def on_cancel(self):
        pass

    def invalidate_cache(self):
        pass

    def maximum_duration(self) -> Duration:
        raise NotImplementedError

    def __hash__(self):
        return hash(self.job_record.id)

    def __eq__(self, other):
        return self.job_record.id == other.job_record.id

    def __repr__(self):
        return f"{self.__class__.__name__}({self.job_record.type})#{self.job_record.id}"
