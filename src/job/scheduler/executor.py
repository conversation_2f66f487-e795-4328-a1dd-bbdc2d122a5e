from __future__ import annotations

import pickle
import subprocess
import sys
from abc import ABCMeta, abstractmethod
from io import Bytes<PERSON>
from subprocess import CalledProcessError, CompletedProcess
from typing import TYPE_CHECKING, Any, cast

import anyio
from anyio import CapacityLimiter, create_task_group, to_thread

from logger import config_structlog

if TYPE_CHECKING:
    from collections.abc import AsyncIterable, Callable

    from job.scheduler.abc import BaseJob


class JobExecutor(metaclass=ABCMeta):
    async def start(self) -> None:
        pass

    @abstractmethod
    async def run_job(self, func: Callable[..., Any], job: BaseJob) -> Any:
        """Run the given job by calling the given function.

        :param func: the function to call
        :param job: the associated job
        :return: the return value of ``func`` (potentially awaiting on the returned
            aawaitable, if any)
        """


class ThreadPoolJobExecutor(JobExecutor):
    _limiter: CapacityLimiter
    max_workers: int = 40

    async def start(self) -> None:
        self._limiter = CapacityLimiter(self.max_workers)

    async def run_job(self, func: Callable[..., Any], _job: BaseJob) -> Any:
        return await to_thread.run_sync(func, cancellable=True, limiter=self._limiter)


class ProcessPoolJobExecutor(JobExecutor):
    """Executes functions in a process pool."""

    # _limiter: CapacityLimiter
    # max_workers: int = 1

    async def start(self) -> None:
        pass

    async def run_job(self, func: Callable[..., Any], _job: BaseJob) -> Any:
        request = pickle.dumps(("run", func), protocol=pickle.HIGHEST_PROTOCOL)

        async def drain_stream(stream: AsyncIterable[bytes], index: int) -> None:
            buffer = BytesIO()
            async for chunk in stream:
                buffer.write(chunk)
                sys.stderr.buffer.write(chunk)

            stream_contents[index] = buffer.getvalue()

        command = [sys.executable, "-u", "-m", __name__]

        async with await anyio.open_process(
            command,
            stdin=subprocess.PIPE,
            stdout=None,
            stderr=subprocess.PIPE,
        ) as process:
            stream_contents: list[bytes | None] = [None, None]
            async with create_task_group() as tg:
                tg.start_soon(drain_stream, process.stderr, 1)
                assert process.stdin is not None, "Process stdin should not be None"
                await process.stdin.send(request)
                await process.stdin.aclose()
                await process.wait()

        stdout_content, stderr_content = stream_contents
        if process.returncode != 0:
            raise CalledProcessError(cast(int, process.returncode), command, stdout_content, stderr_content)
        return CompletedProcess(command, cast(int, process.returncode), stdout_content, stderr_content)


def run_job():
    """Running in a subprocess."""
    stdin_data = sys.stdin.buffer.read()
    input = pickle.loads(stdin_data)
    _, func, *args = input
    return func(*args)


if __name__ == "__main__":
    config_structlog("stderr")  # output all logs to stderr
    run_job()
