from collections.abc import Callable

import pendulum
import structlog
from asyncpg import Pool

from config import Settings
from job.model import JobModel
from job.scheduler.abc import BaseJob
from services import ServicesRegistry


class JobStore:
    def __init__(
        self,
        id: str,
        settings: Settings,
        services_registry: ServicesRegistry,
        conn_pool: Pool,
    ):
        self.id = id
        self.job_classes: dict[str, Callable] = {}
        self._settings = settings
        self._services_registry = services_registry
        self._conn_pool = conn_pool
        self.logger = structlog.get_logger("JobStore", id=self.id)

    def add_job_type(self, job_type: str, job_factory: Callable):
        self.job_classes[job_type] = job_factory

    async def _acquire_next_job(self, capacity: int):
        sql = """
            WITH next_jobs AS (
              SELECT *
              FROM jobs
              WHERE id = '303da25c-b229-4db0-aaf1-4c6d238fe3c6'
                AND cost <= $1
                AND next_run IS NOT NULL
                AND next_run < NOW()
              ORDER BY attempts ASC, next_run ASC, id ASC
              LIMIT 1
              FOR UPDATE SKIP LOCKED
            )
            UPDATE jobs
            SET status = 'running',
                attempts = jobs.attempts + 1,
                alloc = $2,
                run_started = NOW()
            FROM next_jobs
            WHERE next_jobs.id = jobs.id
            RETURNING jobs.*;
        """
        async with self._conn_pool.acquire() as conn, conn.transaction():
            record = await conn.fetch(sql, capacity, self.id)
            if record:
                return record[0]
            return None

    async def fail_job(self, job: BaseJob, error):
        async with self._conn_pool.acquire() as conn:
            await conn.execute(
                """
                    UPDATE jobs
                    SET
                        status = 'failed',
                        errors = CASE
                          WHEN jsonb_array_length(errors) < 30 THEN errors || $2
                          ELSE errors
                        END,
                        run_started = NULL
                    WHERE id = $1
                """,
                job.job_record.id,
                {"ts": pendulum.now().to_iso8601_string(), "cause": error},
            )

    async def retry_job(self, job: BaseJob, error=None):
        async with self._conn_pool.acquire() as conn:
            await conn.execute(
                """
                    UPDATE jobs
                    SET
                        status = 'pending',
                        errors = CASE
                          WHEN jsonb_array_length(errors) < 30 THEN errors || $2
                          ELSE errors
                        END,
                        run_started = NULL
                    WHERE id = $1 AND status = 'running'
                """,
                job.job_record.id,
                {"ts": pendulum.now().to_iso8601_string(), "cause": error},
            )

    async def pull_next_job(self, capacity: int) -> BaseJob | None:
        self.logger.info("Pulling next job", capacity=capacity)
        if capacity <= 0:
            return None
        record = await self._acquire_next_job(capacity)
        if record:
            job_record = JobModel(**record)
            if job_record.type in self.job_classes:
                return self.job_classes[job_record.type](
                    job_record,
                    self._settings,
                    self._services_registry,
                    self._conn_pool,
                )
        return None

    async def set_job_expired_at(self, job: BaseJob, expired_at: pendulum.DateTime):
        async with self._conn_pool.acquire() as conn:
            await conn.execute(
                """
                    UPDATE jobs
                    SET expired_at = $2
                    WHERE id = $1
                """,
                job.job_record.id,
                expired_at.to_iso8601_string(),
            )

    async def cleanup_jobs(self):
        async with self._conn_pool.acquire() as conn:
            # TODO: add expired_at back
            await conn.execute(
                """
                    UPDATE jobs
                    SET status = 'pending', run_started = NULL, alloc = NULL,
                        errors = CASE
                          WHEN jsonb_array_length(errors) < 30 THEN errors || $1
                          ELSE errors
                        END
                    WHERE status = 'running' AND run_started + interval '6 hours' < NOW()
                """,
                {"ts": pendulum.now().to_iso8601_string(), "cause": "expired"},
            )
