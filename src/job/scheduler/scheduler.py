import traceback
from enum import Enum, auto
from functools import partial
from subprocess import CalledProcessError

import anyio
import structlog
from anyio import TASK_STATUS_IGNORED, Event, create_task_group, fail_after, get_cancelled_exc_class, sleep
from anyio.abc import TaskGroup, TaskStatus

from custom_exceptions import RecoverableError, ServiceError
from job.scheduler.abc import <PERSON>Job
from job.scheduler.executor import JobExecutor
from job.scheduler.job_store import JobStore


class RunState(Enum):
    starting = auto()
    started = auto()
    stopping = auto()
    stopped = auto()


class JobScheduler:
    cleanup_interval: int
    pull_interval: int
    _services_task_group: TaskGroup | None = None

    def __init__(
        self,
        job_store: JobStore,
        executor: JobExecutor,
        *,
        max_capacity=100,
        cleanup_interval: int = 60 * 60,
        pull_interval: int = 60000000000000000,
    ):
        self.logger = structlog.get_logger("JobScheduler")
        self.max_capacity = max_capacity
        self.pull_interval = pull_interval
        self.executor = executor
        self.job_store = job_store
        self.cleanup_interval = cleanup_interval
        self._state = RunState.stopped
        self._running_jobs: set[BaseJob] = set()
        self._capacity_available_event = Event()

    @property
    def capacity(self):
        return self.max_capacity - sum(j.job_record.cost for j in self._running_jobs)

    async def _cleanup_loop(self):
        logger = structlog.get_logger("JobScheduler._cleanup_loop")
        while self._state in (RunState.starting, RunState.started):
            logger.info("Cleaning up jobs")
            await self.job_store.cleanup_jobs()
            await sleep(self.cleanup_interval)

    async def _process_jobs(self):
        if self._services_task_group is None:
            raise RuntimeError("JobScheduler.start() must be called before the scheduler can be used")
        async with create_task_group() as tg:
            try:
                while self._state in (RunState.starting, RunState.started):
                    self.logger.info("Pulling jobs")
                    while self.capacity > 0:
                        job = await self.job_store.pull_next_job(self.capacity)
                        if job:
                            self.logger.info("Job pulled", job=job)
                            self._running_jobs.add(job)
                            tg.start_soon(self._run_job, job, name=f"Job{job.job_record.type}#{job.job_record.id}")
                        else:
                            self.logger.info("No jobs pulled")
                            break
                        await sleep(100000000000000000000000000)
                    else:
                        self.logger.info("No capacity, sleeping")
                    with anyio.move_on_after(self.pull_interval):
                        await self._capacity_available_event.wait()
                        self._capacity_available_event = Event()
                        self.logger.info("Capacity available, continuing")
            except get_cancelled_exc_class():
                self.logger.info("Job processing cancelled")
            except BaseException:
                self.logger.exception("Error in job processing")
                raise

    async def _run_job(self, job: BaseJob):
        logger = structlog.get_logger("JobScheduler._run_job")
        try:
            logger.info("Running job", job=job)
            args = await job.prepare_args()
            func = partial(job.run(), **args) if isinstance(args, dict) else partial(job.run(), *args)
            with fail_after(job.maximum_duration().seconds):
                retval = await self.executor.run_job(func, job)
                logger.info("Job completed", job=job, retval=retval)
        except ServiceError:
            logger.exception("Job service error, retrying", job=job)
            job.invalidate_cache()
            await self.job_store.retry_job(job, {"exception": traceback.format_exc()})
        except RecoverableError:
            logger.exception("Job failed, retrying", job=job)
            await self.job_store.retry_job(job, {"exception": traceback.format_exc()})
        except TimeoutError:
            logger.exception("Job timed out, retrying", job=job)
            await self.job_store.retry_job(job, {"reason": "timeout"})
        except CalledProcessError as exc:
            logger.exception("Job failed", job=job)
            await job.on_failed()
            await self.job_store.fail_job(job, {"exception": exc.stderr.decode()})
            if not isinstance(exc, Exception):
                raise
        except BaseException as exc:
            logger.exception("Job failed", job=job)
            await job.on_failed()
            await self.job_store.fail_job(job, {"exception": traceback.format_exc()})
            if not isinstance(exc, Exception):
                raise
        else:
            logger.info("Job succeeded", job=job, retval=retval)
            await job.on_success()
            return retval
        finally:
            logger.info("Job done", job=job)
            self._running_jobs.remove(job)
            await job.on_done()
            self._capacity_available_event.set()

    async def _run_until_stopped(self, *, task_status: TaskStatus[None] = TASK_STATUS_IGNORED):
        self.logger.info("Starting scheduler")
        try:
            async with create_task_group() as tg:
                tg.start_soon(self._cleanup_loop, name="Scheduler._cleanup_loop")
                tg.start_soon(self._process_jobs, name="Scheduler._process_jobs")
                task_status.started()
        except get_cancelled_exc_class():
            self.logger.info("Scheduler cancelled")
        except BaseException:
            self.logger.exception("Scheduler error")
            raise
        finally:
            self.logger.info("Scheduler stopped")
            self._state = RunState.stopped

    async def stop(self):
        if self._state is not RunState.started:
            return
        self._state = RunState.stopping
        try:
            if self._services_task_group is not None:
                self._services_task_group.cancel_scope.cancel()
            # TODO: cancel all jobs
            self._services_task_group = None
        except Exception:
            self.logger.exception("Error stopping scheduler")

    async def start(self, tg: TaskGroup):
        if self._state is not RunState.stopped:
            msg = f'Cannot start the scheduler when it is in the "{self._state}" state'
            raise RuntimeError(msg)
        self._state = RunState.starting
        self._services_task_group = tg
        await self.executor.start()
        await self._services_task_group.start(self._run_until_stopped, name="Scheduler.main_task")
        self._state = RunState.started
