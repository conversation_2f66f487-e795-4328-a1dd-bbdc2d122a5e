from asyncpg import Connection
from pypika import Criterion, PostgreSQLQuery as Q

from .model import table as jobs


async def job_exists(conn: Connection, job_source_id: str, conditions: list | None = None):
    _conditions = [(jobs.job_source_id == job_source_id)]
    if conditions:
        _conditions.extend(conditions)
    q = str(Q.from_(jobs).select("id").where(Criterion.all(_conditions)).limit(1))
    return bool(await conn.fetch(q))
