from string import Formatter

from llm.providers import BaseProvider
from llm.utils import NormalLLMLogGroup, parse_res_to_normal_log_groups
from vector_db.vector_search import VectorSearch


class BaselineContext:
    def __init__(
        self,
        vector_search: VectorSearch,
        samples: list[str] | None = None,
        log_groups: list[NormalLLMLogGroup] | None = None,
    ):
        self.vector_search = vector_search
        self.samples: list[str] = samples or []
        self.log_groups: list[NormalLLMLogGroup] = log_groups or []

    @staticmethod
    def load(vector_search: VectorSearch, file_content: list[str] | dict):
        if isinstance(file_content, list):  # legacy format
            return BaselineContext(vector_search=vector_search, samples=file_content)
        if "log_groups" in file_content:
            log_groups = file_content["log_groups"]
            return BaselineContext(
                vector_search=vector_search,
                log_groups=[NormalLLMLogGroup(**group) for group in log_groups],
            )
        raise ValueError("Invalid file content format. Expected list or dict with 'log_groups' key.")

    def dump(self):
        return {"log_groups": self.log_groups}

    def save_to_vectordb(self, date: str):
        log_samples = self.samples
        if self.log_groups:
            log_samples = []
            for group in self.log_groups:
                log_samples.extend(group.samples)
        for i in range(0, len(log_samples), 8):
            batch = log_samples[i : i + 8]
            self.vector_search.save_logs("\n".join(batch), date)

    def to_prompt(self, per_group_samples: int = 5):
        if not self.log_groups:
            return "\n".join(self.samples)
        return "\n".join([group.to_prompt(per_group_samples) for group in self.log_groups])


LOG_GROUPING_TEMPLATE = """
You are an expert monitoring log analyzer. I have a collection of log entries that need to be categorized into meaningful groups.

I'll provide you with a numbered list of log entries. Your task is to:

1. Analyze the logs and identify natural groupings based on patterns, message types, and content
2. You can create a maximum of 10 distinct groups that effectively categorize all logs
3. Provide a clear title and description for each group
4. List which log entries (by their original number) belong in each group
5. If a log entry does not fit into any group, it should be included in a separate group called "OTHER". only use this group if you have no other choice

NOTE: you must go through and understand all logs first, then only after that, come up with the natural groupings names

## FORMAT REQUIREMENTS

You must present your analysis in the exact XML-like format below:

<group>
<title>TITLE OF GROUP 1</title>
<description>Concise description of what these logs represent and why they're grouped</description>
<samples>
NUMBER
NUMBER
NUMBER
</samples>
</group>

<group>
<title>TITLE OF GROUP 2</title>
<description>Concise description of what these logs represent and why they're grouped</description>
<samples>
NUMBER
NUMBER
</samples>
</group>

Important:
- Use only the exact format shown above
- Include every log entry number in exactly one group
- Make group titles concise but descriptive
- In <samples>, list only the line numbers, one per line

## LOG ENTRIES:

```
{log_lines}
```

## INSTRUCTIONS:

Make sure you follow the format requirements strictly, it's a XML format. There should be multiple groups, each with a title, description, and samples.

```
<group>
<title>TITLE OF GROUP 1</title>
<description>Concise description of what these logs represent and why they're grouped</description>
<samples>
NUMBER
NUMBER
NUMBER
</samples>
</group>
```

"""
SYSTEM_INSTRUCTIONS = """
you are an site reliability GPT, responsible for professional monitoring of infrastructures.
You are monitoring logs. Respond in concise, minimal wordings, direct to the point manner.
"""
RETRY_INSTRUCTION = """
Prompt:
{prompt}
Completion:
{completion}

Above, the Completion did not satisfy the constraints given in the Prompt.
Please try again
"""


class LLMLogGrouping:
    def __init__(self, provider: BaseProvider):
        self.provider = provider
        self.formatter = Formatter()

    def _parse_result(self, logs: list[str], res: str) -> list[NormalLLMLogGroup]:
        return parse_res_to_normal_log_groups(logs, res)

    def _token_limit(self, logs: list[str]):
        model = self.provider.model
        remaining_tokens = model.token_limit
        remaining_tokens -= model.calculate_token(SYSTEM_INSTRUCTIONS + LOG_GROUPING_TEMPLATE + RETRY_INSTRUCTION)
        log_count = 0
        for log in logs:
            remaining_tokens -= model.calculate_token(log)
            log_count += 1
            if remaining_tokens < 0:
                return logs[:log_count]
        return logs

    def _retry(self, prompt: str, completion: str, logs: list[str]):
        retry_prompt = self.formatter.format(RETRY_INSTRUCTION, prompt=prompt, completion=completion)
        response = self.provider.invoke(retry_prompt, add_no_think_instruction=True)
        return self._parse_result(logs, response)

    def invoke(self, logs: list[str]) -> list[NormalLLMLogGroup]:
        limited_logs = self._token_limit(logs)
        prompt = self.formatter.format(
            LOG_GROUPING_TEMPLATE,
            log_lines="\n".join(f"{i}. {line}" for i, line in enumerate(limited_logs)),
        )
        response = self.provider.invoke(prompt, temperature=0.2, add_no_think_instruction=True)
        parsed_result = self._parse_result(logs, response)
        if len(parsed_result):
            return parsed_result
        new_result = self._retry(prompt, response, logs)
        if len(new_result):
            return new_result
        return [NormalLLMLogGroup(samples=limited_logs)]
