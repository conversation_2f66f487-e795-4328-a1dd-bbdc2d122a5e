import random
import re
from collections.abc import Callable
from copy import deepcopy
from dataclasses import dataclass, field
from string import Formatter
from typing import Any, NotRequired, TypedDict

import structlog

from llm.baseline_context import BaselineContext
from llm.past_suppression import PastSuppression
from llm.providers import BaseProvider
from llm.utils import try_parse_int, try_parse_log_groups
from vector_db.vector_search import VectorSearch


@dataclass
class ProblematicLLMLogGroup:
    description: str = ""
    title: str = ""
    rankings: list[int] = field(default_factory=list)
    samples: list[Any] = field(default_factory=list)
    samples_index: list[int] = field(default_factory=list)

    def __post_init__(self):
        # order by rankings
        if len(self.rankings) != len(self.samples_index):
            raise ValueError("rankings and samples_index must have the same length")
        sorted_pair = sorted(zip(self.rankings, self.samples_index, strict=False), reverse=True)
        if not sorted_pair:
            return
        rankings_tuple, samples_index_tuple = zip(*sorted_pair, strict=False)
        self.rankings = list(rankings_tuple)
        self.samples_index = list(samples_index_tuple)


class SortedSampleResponse(TypedDict):
    index: NotRequired[int]
    rank: int
    sample: NotRequired[Any]


def _default_sample_to_prompt(x):
    return x


class ProblematicLogGroups:
    def __init__(
        self,
        vector_search: VectorSearch,
        ranked_groups: list[ProblematicLLMLogGroup],
        sample_serializer=None,
        sample_to_prompt: Callable[[Any], str] | None = None,
    ):
        self.vector_search = vector_search
        self.original_ranked_groups = []
        self.ranked_groups = ranked_groups
        self.sample_to_prompt: Callable[[Any], str] = (
            _default_sample_to_prompt if sample_to_prompt is None else sample_to_prompt
        )
        self.sample_serializer = sample_serializer
        self.logger = structlog.get_logger("ProblematicLogGroups")

    def copy_original_groups(self):
        self.original_ranked_groups = deepcopy(self.ranked_groups)

    def sample_size(self):
        return sum(len(group.rankings) for group in self.ranked_groups)

    def llm_filter_out_existed_logs(self, past_suppression: PastSuppression):
        for group in self.ranked_groups:
            samples = group.samples
            logs = [self.sample_to_prompt(sample) for sample in samples if sample]
            past_log_indices = set()
            for i in range(0, len(logs), 5):
                batch = logs[i : i + 5]
                try:
                    res = past_suppression.existed_logs(batch)
                    for index, is_past in res.items():
                        if is_past:
                            past_log_indices.add(index + i)
                except Exception:
                    self.logger.exception("Failed to filter out existed logs")
                    continue
            kept_indices = [index for index, sample in enumerate(samples) if index not in past_log_indices]
            group.samples = [group.samples[index] for index in kept_indices]
            group.rankings = [group.rankings[index] for index in kept_indices]
            group.samples_index = [group.samples_index[index] for index in kept_indices]

    def shrink_group_samples(self, maximum: int, threshold: int = 40):
        for group in self.ranked_groups:
            samples = []
            rankings = []
            samples_index = []
            for rank, sample, index in zip(group.rankings, group.samples, group.samples_index, strict=False):
                if rank < threshold:
                    samples.append(sample)
                    rankings.append(rank)
                    samples_index.append(index)
            if len(samples) > maximum:
                indices = random.sample(range(len(samples)), maximum)
                group.samples = [samples[i] for i in indices]
                group.rankings = [rankings[i] for i in indices]
                group.samples_index = [samples_index[i] for i in indices]
            else:
                group.samples = samples
                group.rankings = rankings
                group.samples_index = samples_index

    def save_to_vectordb(self, current_date: str):
        logs = []
        for group in self.ranked_groups:
            samples = group.samples
            logs.extend([self.sample_to_prompt(sample) for sample in samples if sample])
        for i in range(0, len(logs), 8):
            batch = logs[i : i + 8]
            self.vector_search.save_logs("\n".join(batch), current_date)

    def per_group_samples(self, maximum: int, per_group: float, threshold: int = 40) -> list[Any]:
        samples = []
        minimum_sample = 3
        for group in self.ranked_groups:
            counter = 0
            total = len(group.samples)
            for rank, sample in zip(group.rankings, group.samples, strict=False):
                if rank < threshold:
                    samples.append(sample)
                    counter += 1
                if per_group >= 1:
                    if counter >= per_group:
                        break
                elif per_group < 1 and counter >= max(minimum_sample, total * per_group):
                    break
        if len(samples) > maximum:
            return random.sample(samples, maximum)
        return samples

    def sorted_samples(self, with_index=True, with_sample=True) -> list[SortedSampleResponse]:
        samples = []
        for group in self.ranked_groups:
            for i in range(len(group.rankings)):
                sample = group.samples[i] if len(group.samples) > i else None
                s = {"rank": group.rankings[i]}
                if with_index:
                    s["index"] = group.samples_index[i]
                if with_sample:
                    if sample is None:
                        continue
                    s["sample"] = sample
                samples.append(s)
        return sorted(samples, key=lambda x: x["rank"], reverse=True)

    def set_samples_based_on_index(self, samples: list[Any], sample_to_prompt=lambda x: x):
        for group in self.ranked_groups:
            group.samples = [samples[index] for index in group.samples_index if index < len(samples)]
        self.sample_to_prompt = sample_to_prompt
        self.copy_original_groups()

    @staticmethod
    def load(
        file_content: dict,
        sample_deserializer=None,
        *,
        vector_search: VectorSearch,
        sample_to_prompt=None,
        sample_serializer=None,
    ):
        groups = file_content["ranked_groups"]
        if sample_deserializer:
            for group in groups:
                group["samples"] = [sample_deserializer(sample) for sample in group["samples"] if sample]
        p = ProblematicLogGroups(
            vector_search=vector_search,
            ranked_groups=[ProblematicLLMLogGroup(**group) for group in groups],
            sample_to_prompt=sample_to_prompt,
            sample_serializer=sample_serializer,
        )
        p.copy_original_groups()
        return p

    def dump(self) -> dict:
        return {
            "ranked_groups": [
                {
                    "description": group.description,
                    "title": group.title,
                    "rankings": group.rankings,
                    "samples": group.samples
                    if self.sample_serializer is None
                    else [self.sample_serializer(sample) for sample in group.samples if sample],
                    "samples_index": group.samples_index,
                }
                for group in self.ranked_groups
            ],
        }

    def to_prompt(
        self,
        per_group: int,
        threshold: int = 40,
        sample_index_start_from: int | None = None,
        return_logs=False,
        use_original_groups=False,
    ) -> str | tuple[str, list[str]]:
        prompt = ""
        log_samples = []
        log_index = sample_index_start_from or 1
        ranked_groups = self.original_ranked_groups if use_original_groups else self.ranked_groups
        for group in ranked_groups:
            counter = 0
            samples_prompts = []
            for rank, sample in zip(group.rankings, group.samples, strict=False):
                if rank > threshold or not sample:
                    continue
                prefix = f"{log_index}. " if sample_index_start_from is not None else ""
                if self.sample_to_prompt:
                    samples_prompts.append(prefix + self.sample_to_prompt(sample))
                else:
                    samples_prompts.append(prefix + sample)
                counter += 1
                log_index += 1
                if counter >= per_group:
                    break
            if samples_prompts:
                samples_prompt = "\n".join(samples_prompts)
                log_samples += samples_prompts
                prompt += f"""
<group>
    <title>{group.title}</title>
    <description>{group.description}</description>
    <samples>
{samples_prompt}
    </samples>
</group>
"""
        if return_logs:
            return prompt, log_samples
        return prompt


CONTEXTUAL_FILTER_TEMPLATE = """
You are a site reliability GPT, responsible for professional monitoring of infrastructures.
You are monitoring logs and analyzing them for patterns and anomalies.

## Analysis Task

You are presented with the following set of log lines that are randomly sampled from {total_lines} log lines.
Your objective is to:

1. Analyze the logs and identify natural groupings based on patterns, message types, and content
2. Create meaningful new groups for logs that show distinct patterns not covered by existing groups
3. Each group must have a specific, descriptive title that reflects the actual content of the logs
4. DO NOT use generic groups like "Other", "Miscellaneous", or "Unknown" - create specific groups instead
5. Rank EVERY log line by severity (from 1 to 100, where 1 is most severe and 100 is least severe).
6.  If the end of a log has "increased" or "decreased", it indicates that this type of log occurs more or less frequently than the baseline.
    - for example, if a "error" log line has "decreased", it's a good sign and should be given a higher severity score (81-100)
7. Select {sample_size} most significant problematic log lines in total
8. If there are many similar logs belonging to the same group, select the top 5 representative log lines from that group.

IMPORTANT:
- Create as many groups as needed to properly categorize the logs
- Each group should represent a distinct pattern or behaviour
- Logs with similar patterns, components, or error types should be grouped together
- If you see a unique issue that doesn't fit with others, create a specific group for it

NOTE: this network has frequent configuration changes performed by network operators, which is considered normal

## Selection Criteria

- Selected log lines must not be repetitive
- Selected lines must not overlap on their potential root-cause
- Prioritize logs that can cause system issues or provide clues to fixing system issues

## Severity Ranking

1. If a log has a severity field, use it to determine the severity score. Generally map these severity levels as follows
  * "critical" or "emergency" → 1-5 range
  * "alert" → 5-10 range
  * "err" or "error" → 10-40 range
  * "warning" → 40-60 range
  * "notice" → 60-80 range
  * "info" → 80-100 range

2. if not present, use the content of the log to determine the severity score
Rank EVERY log line between 1 to 100:

- Rank 1-20: Critical issues that require immediate attention (system failures, security breaches)
- Rank 21-40: Serious issues that may impact service (performance degradation, resource exhaustion)
- Rank 41-60: Moderate issues that should be investigated (unexpected behaviours, warnings)
- Rank 61-80: Minor issues or unusual patterns (non-critical errors, slight deviations)
- Rank 81-100: Normal operation or informational logs (expected behaviour, routine events)

## Response Format

Your response must be a list with each item on a new line, with the format: `[<index>] -> [<ranking>]` And groups
The index and ranking should be wrapped with square brackets.

Do not add any other tags.
For each group it should be like this: Do not use original log lines, only use the index from the log lines

```
<group>
<title>SPECIFIC AND DESCRIPTIVE TITLE</title>
<description>Detailed description of what these logs represent, the pattern they follow, and why they're grouped together</description>
<samples>
[1] -> [45]
[2] -> [87]
[3] -> [12]
...include ALL log lines in this group with their rankings
</samples>
</group>
```

[120] -> [2] means the 120th line is selected and assigned with the ranking 2 (high severity).
[5] -> [80] means the 5th line is selected and assigned with the ranking 80 (low severity).
You must return exactly {sample_size} samples with the index and ranking.

## Log Lines to Analyze

```
{log_lines}
```

"""

CONTEXTUAL_FILTER_WITH_BASELINE_TEMPLATE = [
    """
You are a site reliability GPT, responsible for professional monitoring of infrastructures.
You are monitoring logs and analyzing them for patterns and anomalies.

## Context

You are provided with normal log patterns that are already organized into logical groups.
Each group has a title, description, and sample log entries that represent normal system behaviour.
Below are Training Data that represent NORMAL system behaviour. Study these carefully - they define what is considered normal in this environment.
IMPORTANT: These normal log groups may contain errors, warnings, and concerning messages that are EXPECTED and ACCEPTABLE. Your job is to learn these patterns as normal.

### Training Data - Normal Log Groups

```
{training_groups}
```

Analyze these baseline logs and identify the key patterns, error types, and message structures that define "normal" in this environment.
""",
    """
You are presented with the following set of log lines.

Your objective is to:
1. Classify logs according to the established groups from your training data when appropriate
2. Create meaningful new groups for logs that show distinct patterns not covered by existing groups
3. Each group must have a specific, descriptive title that reflects the actual content of the logs
4. DO NOT use generic groups like "Other", "Miscellaneous", or "Unknown" - create specific groups instead
5. Rank EVERY log line by severity (from 1 to 100, where 1 is most severe and 100 is least severe).
6. MAKE SURE you output {line_count} log lines AND the severity in total
6. If a log matches patterns in the training data, it should be considered normal and given a lower severity score (81-100)
7. If the end of a log has "increased" or "decreased", it indicates that this type of log occurs more or less frequently than the baseline.
    - for example, if a "error" log line has "decreased", it's a good sign and should be given a higher severity score (81-100)

IMPORTANT:
- Create as many groups as needed to properly categorize the logs
- Each group should represent a distinct pattern or behaviour
- Logs with similar patterns, components, or error types should be grouped together
- If you see a unique issue that doesn't fit with others, create a specific group for it

NOTE: This network has frequent configuration changes performed by network operators, which is considered normal

## Severity Ranking

1. If a log has a severity field, use it to determine the severity score. Generally map these severity levels as follows
  * "critical" or "emergency" → 1-5 range
  * "alert" → 5-10 range
  * "err" or "error" → 10-40 range
  * "warning" → 40-60 range
  * "notice" → 60-80 range
  * "info" → 80-100 range
2. if not present, use the content of the log to determine the severity score
Rank EVERY log line between 1 to 100:
- Rank 1-20: Critical issues that require immediate attention (system failures, security breaches)
- Rank 21-40: Serious issues that may impact service (performance degradation, resource exhaustion)
- Rank 41-60: Moderate issues that should be investigated (unexpected behaviours, warnings)
- Rank 61-80: Minor issues or unusual patterns (non-critical errors, slight deviations)
- Rank 81-100: Normal operation or informational logs (expected behaviour, routine events)

## Response Format
Classify all logs into appropriate groups and include EVERY log line with its severity ranking.
For each group it should be like this: Do not use original log lines, only use the index from the log lines

```
<group>
<title>SPECIFIC AND DESCRIPTIVE TITLE</title>
<description>Detailed description of what these logs represent, the pattern they follow, and why they're grouped together</description>
<samples>
[1] -> [45]
[2] -> [87]
[3] -> [12]
...include ALL log lines in this group with their rankings
</samples>
</group>
```

After you've completed this analysis, I will ask you to select the {sample_size} most significant problematic log lines in the next round.

## Log Lines to Analyze

```
{log_lines}
```

    """
    """
Based on your previous analysis, now select the {sample_size} most significant problematic log lines from all the logs you've analyzed.

## Selection Criteria
- Selected log lines must represent the most severe issues (lowest severity scores)
- Selected log lines must not be repetitive
- Selected lines must not overlap on their potential root-cause
- Prioritize logs that can cause system issues or provide clues to fixing system issues
- Ensure selection covers different problem types rather than multiple instances of the same issue
- If multiple logs show the same issue, select the one that best represents the problem

## Response Format
Your response must be a list with each item on a new line, with the format: `[<index>] -> [<ranking>]`
The index and ranking should be wrapped with square brackets.

For each group of selected samples it should be like this:

```
<group>
<title>SPECIFIC AND DESCRIPTIVE TITLE</title>
<description>Detailed description of what these logs represent and why they're significant</description>
<samples>
[60] -> [2]
[5] -> [80]
[6] -> [4]
</samples>
</group>
```

You must return exactly {sample_size} samples with the index and ranking.
Ensure these samples represent the most important issues across all groups.
""",
    """
Please review your output more carefully to ensure you
1. returned the correct number of samples
2. they are properly formatted.
3. you didn't miss any important log lines with lower severity scores.
4. the logs are ranked with correct severity scores

And re-answer the question again with the same format as above.
""",
]

SYSTEM_INSTRUCTIONS = """
you are an site reliability GPT, responsible for professional monitoring of infrastructures.
You are monitoring logs. Respond in concise, minimal wordings, direct to the point manner.
"""

RETRY_INSTRUCTIONS = """
Prompt:
{prompt}
Completion:
{completion}

Above, the Completion did not satisfy the constraints given in the Prompt.
Please try again
"""


class ContextualRanking:
    def __init__(self, provider: BaseProvider, vector_search: VectorSearch):
        self.provider = provider
        self.vector_search = vector_search
        self.formatter = Formatter()
        self.regex = re.compile(r"\[(\d+)\]\s*->\s*\[(\d+)\]", re.IGNORECASE)

    def _parse_ranking_result(self, result: str) -> list[tuple[int, int]]:
        """[10] -> [5]"""
        res = []
        for index, rank in self.regex.findall(result):
            parsed_index = try_parse_int(index)
            parsed_rank = try_parse_int(rank)
            if parsed_index is not None and parsed_rank is not None:
                res.append((parsed_index, parsed_rank))
        return res

    def _parse_result(self, res: str) -> ProblematicLogGroups:
        groups: list[ProblematicLLMLogGroup] = []
        try:
            parsed_groups = try_parse_log_groups(res)
            for group in parsed_groups:
                samples_text = group["samples_text"]
                index_rankings = self._parse_ranking_result(samples_text)
                groups.append(
                    ProblematicLLMLogGroup(
                        rankings=[rank for _, rank in index_rankings],
                        samples_index=[index for index, _ in index_rankings],
                        description=group["description"],
                        title=group["title"],
                    ),
                )
            return ProblematicLogGroups(ranked_groups=groups, vector_search=self.vector_search)
        except Exception:
            return ProblematicLogGroups(ranked_groups=[], vector_search=self.vector_search)

    def _token_limit(self, used_token: int, logs: list[str]):
        model = self.provider.model
        remaining_tokens = model.token_limit - used_token
        for i, log in enumerate(logs):
            remaining_tokens -= model.calculate_token(log)
            if remaining_tokens < 0:
                return logs[: i + 1]
        return logs

    def _retry(self, prompt: str, completion: str):
        retry_prompt = self.formatter.format(RETRY_INSTRUCTIONS, prompt=prompt, completion=completion)
        response = self.provider.invoke(retry_prompt)
        return self._parse_result(response)

    def _legacy_template_prompt(self, total_lines: int, sample_size: int, logs: list[str]):
        model = self.provider.model
        used_token = model.calculate_token(SYSTEM_INSTRUCTIONS + CONTEXTUAL_FILTER_TEMPLATE + RETRY_INSTRUCTIONS)
        limited_logs = self._token_limit(int(used_token), logs)
        prompt = self.formatter.format(
            CONTEXTUAL_FILTER_TEMPLATE,
            total_lines=total_lines,
            sample_size=sample_size + 5,  # llm usually return less than expected
            log_lines="\n".join((f"{i}. {line}" for i, line in enumerate(limited_logs))),
        )
        return limited_logs, prompt

    def _baseline_template_prompt(
        self,
        total_lines: int,
        sample_size: int,
        logs: list[str],
        baseline_context: BaselineContext,
    ):
        model = self.provider.model
        used_token = model.calculate_token(
            baseline_context.to_prompt()
            + SYSTEM_INSTRUCTIONS
            + "\n".join(CONTEXTUAL_FILTER_WITH_BASELINE_TEMPLATE)
            + RETRY_INSTRUCTIONS,
        )
        responses = []
        messages = [{"role": "system", "content": SYSTEM_INSTRUCTIONS}]
        limited_logs = self._token_limit(int(used_token), logs)
        for tpl in CONTEXTUAL_FILTER_WITH_BASELINE_TEMPLATE:
            prompt = self.formatter.format(
                tpl,
                total_lines=total_lines,
                sample_size=sample_size + 5,  # llm usually return less than expected
                training_groups=baseline_context.to_prompt(),
                line_count=len(limited_logs),
                log_lines="\n".join((f"{i}. {line}" for i, line in enumerate(limited_logs))),
            )
            if self.provider.model.thinking:
                prompt = f"{prompt} /no_think /nothink"
            messages.append({"role": "user", "content": prompt})
            res = self.provider.conversation(messages, temperature=0.2)
            responses.append(res)
            messages.append({"role": "assistant", "content": res})
        return self._parse_result(responses[-1])

    def invoke(
        self,
        *,
        total_lines: int,
        sample_size: int,
        logs: list[str],
        baseline_context: BaselineContext | None = None,
    ) -> ProblematicLogGroups:
        if baseline_context:
            return self._baseline_template_prompt(total_lines, sample_size, logs, baseline_context)
        limited_logs, prompt = self._legacy_template_prompt(total_lines, sample_size, logs)
        response = self.provider.invoke(prompt, temperature=0.2, add_no_think_instruction=True)
        return self._parse_result(response)
