from functools import cache

import httpx
import structlog
from tenacity import retry, stop_after_attempt, wait_fixed, wait_random

from custom_exceptions import ServiceError


class LLamingtonClient:
    def __init__(self, llamington_url: str, tenant_id: str, flow_id: str):
        self.tenant_id = tenant_id
        self.flow_id = flow_id
        self.client = httpx.Client(base_url=f"{llamington_url}/tenants/{tenant_id}", timeout=120)
        self.logger = structlog.get_logger(
            "LLamingtonClient",
            tenant_id=tenant_id,
            flow_id=flow_id,
        )

    @cache
    def get_knowledge_base(self, flow_id: str):
        try:
            kbs = self.client.get("/knowledge-bases", params={"flow-id": flow_id})
        except Exception as e:
            self.logger.warning("Failed to get knowledge base", flow_id=flow_id)
            raise ServiceError("Failed to get knowledge base") from e
        kbs = kbs.json()
        if not kbs or not kbs.get("knowledge-bases"):
            return None
        return kbs["knowledge-bases"][0]

    @retry(wait=wait_random(min=180, max=600), stop=stop_after_attempt(3))
    def create_thread(
        self,
        model: str,
        messages: list,
        *,
        temperature: float | None,
    ):
        kb = self.get_knowledge_base(self.flow_id)
        knowledge_base_id = kb["id"] if kb else None
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
            }
            if knowledge_base_id:
                payload["knowledge-base-id"] = knowledge_base_id
            run = self.client.put(
                "/threads/runs",
                json=payload,
            )
            return run.json()
        except Exception as e:
            self.logger.warning("Failed to create thread", model=model, messages=messages)
            raise ServiceError("Failed to create thread") from e

    @retry(wait=wait_random(min=180, max=600), stop=stop_after_attempt(3))
    def append_thread_message(self, thread_id: str, message: dict):
        try:
            run = self.client.put(f"/threads/{thread_id}/messages", json=message).json()
        except Exception as e:
            self.logger.warning("Failed to append message to thread", thread_id=thread_id, message=message)
            raise ServiceError("Failed to append message to thread") from e
        return run

    @retry(wait=wait_fixed(30), stop=stop_after_attempt(3))
    def list_thread_messages(self, thread_id: str):
        try:
            messages = self.client.get(f"/threads/{thread_id}/messages", params={"order": "desc"}).json()
        except Exception as e:
            self.logger.warning("Failed to list messages", thread_id=thread_id)
            raise ServiceError("Failed to list messages") from e
        return messages
