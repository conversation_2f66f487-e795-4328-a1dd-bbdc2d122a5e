import re
from datetime import date, datetime, timedelta, timezone
from zoneinfo import ZoneInfo

from pendulum import DateTime

from llm.providers import BaseProvider

SYSTEM_INSTRUCTION = """
you are a site reliability engineer, monitoring enterprise IT infrastructure logs.
"""

LOG_RATE_STRUCTURE = """
X-axis label is time in 24hr system, displaying past 24hours range. Y-axis is the log rate. Given green graph = "Upper Prediction Bound", blue graph = "Lower Prediction Bound", and orange graph = "Actual Log Rate".

Your task is to identify the 5 most highly irregular spikes or dips in the orange line (Actual Log Rate). Respond with a list of up to 5 timestamps in the following format:

[HH:mm] -> [spike/dip]

For example:
[02:25] -> [dip]
[12:05] -> [spike]
[19:50] -> [spike]

Rules:
1. Only provide the list of timestamps; do not include any explanations or additional text.
2. List a maximum of 5 timestamps, focusing on the most significant fluctuations.
3. It's fine to return less than 5 timestamps. If there is no significant spike or dip, provide an empty response.
3. Use 24-hour time format (HH:mm).
4. Indicate whether each timestamp represents a spike or a dip.

Provide your response using only the specified format.
"""

LOG_RATE_SUMMARY = """
Provide a statement up to 50 words about the orange graph, paying attention to time, taking notes on significant spikes and dips in relation to time. When applicable, also mention any other notable trend. Your respond must not mention the colours, must not mention prediction bound.
"""


def try_parse_datetime(dt_str: str, start_date: date, end_date: date):
    utc_tz = timezone.utc
    try:
        local_time = datetime.strptime(dt_str, "%H:%M").time()
        for d in [start_date, end_date]:
            local_datetime = datetime.combine(d, local_time).replace(tzinfo=ZoneInfo("Asia/Tokyo"))
            utc_datetime = local_datetime.astimezone(utc_tz)
            if start_date <= utc_datetime < end_date:
                return utc_datetime
    except ValueError:
        return None


class LogRateAnalysis:
    def __init__(self, task_start: DateTime, provider: BaseProvider):
        self.task_start = task_start
        self.provider = provider
        self.regex = re.compile(r"\[(\d{2}:\d{2})\]\s*->\s*\[(spike|dip)\]", re.IGNORECASE)

    def _parse_result(self, result: str):
        """spikes:
        :param result:
        :return:
        """
        res = []
        for dt_str, tag in self.regex.findall(result):
            dt = try_parse_datetime(dt_str, self.task_start, self.task_start + timedelta(days=1))
            if dt and tag in ["spike", "dip"]:
                res.append((dt, tag))
        return res

    def invoke(self, *, image: str):
        prompt = LOG_RATE_STRUCTURE
        messages = [
            {"role": "system", "content": SYSTEM_INSTRUCTION},
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": image}},
                ],
            },
        ]
        res = self.provider.conversation(messages, temperature=0.12)
        parsed_result = self._parse_result(res)
        messages.append({"role": "assistant", "content": res})
        messages.append({"role": "assistant", "content": LOG_RATE_SUMMARY})
        summary = self.provider.conversation(messages, temperature=0.12)
        return parsed_result, summary
