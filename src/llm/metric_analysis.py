from string import <PERSON>atter

import pandas as pd

from anomaly_detector.marker import Marker
from anomaly_detector.sks.marker_descriptions import get_marker_description
from llm.providers import BaseProvider

SYSTEM_INSTRUCTION_TEMPLATE = """
You are a site reliability engineer, monitoring enterprise IT infrastructure logs.

For metric analysis, you have access to the following technical knowledge base:

{extra_context}

Use this knowledge base to provide accurate analysis of wireless network performance metrics, understanding the specific calculation methods and relationships between different QoE components.
"""


MARKER_SUMMARY = """
Provide a statement up to 50 words about the {marker_name}, the {marker_name} is about {marker_description}.
Here is the values of this marker
{marker_values}
the "upper" means predicted upper bound, the "lower" means predicted lower bound.
the prediction is trained from a normal values of this marker. if the values are not in normal range, it is abnormal.
paying attention to time, taking notes on significant spikes and dips.
When applicable, also mention any other notable trend.
Provide an analysis of symptoms and suggest potential root cause in less than 80 words, do not use bullet points and markdown format.
"""


class MetricAnalysis:
    def __init__(self, provider: BaseProvider, extra_context: str = ""):
        self.provider = provider
        self.formatter = Formatter()
        self.extra_context = extra_context

    def _get_system_instruction(self):
        """Generate system instruction with QoE calculation context."""
        return self.formatter.format(
            SYSTEM_INSTRUCTION_TEMPLATE,
            extra_context=self.extra_context,
        )

    def invoke(self, *, marker: Marker, values: pd.DataFrame):
        prompt = self.formatter.format(
            MARKER_SUMMARY,
            marker_name=marker.name,
            marker_description=get_marker_description(marker.name, lang="zh-cn"),
            marker_values=values.to_csv(index=True, float_format="%.2f"),
        )
        # Use system instruction with context in conversation
        messages = [
            {"role": "system", "content": self._get_system_instruction()},
            {"role": "user", "content": prompt},
        ]
        return self.provider.conversation(messages, temperature=0.2)
