from string import Formatter

from llm.llamington import LLamingtonClient
from llm.providers import BaseProvider
from llm.utils import filter_annotations

SYSTEM_INSTRUCTION = """
you are a site reliability engineer, monitoring enterprise IT infrastructure logs.
Check into project files or knowledge base to analyze and provide background information.
If you tried to use the file_search, please include the reference and mention it in your response.
If there are no documents or you haven't used documents, ignore it.
NOTE: this network has frequent configuration changes performed by network operators, which is considered normal
"""

PANEL_ANALYSIS_TEMPLATES = {
    # log structure without training groups
    "legacy_log_structure": """
You have previously benchmarked a normal set of logs based on structure profiles.
Your system logs are found to contain known predictable structures, they maybe logs of different lengths, containing different fields, containing different contents.
During the past days, the following issues were already identified and addressed. Consider these as known issues that do not require reporting again:
{past_7_day_advisories}
On this day however, the following logs were occurring at unusual frequency compared to normal. These log lines are randomly sampled and sorted.

```
{log_lines}
```

If any issues match those already reported in the past days, mark them as already addressed and do not include them in your final analysis.
Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
assess and evaluate this findings, find potential causal relationship between log lines, and provide an analysis in under 50 words.
    """,
    # sequence pattern without training groups
    "legacy_sequence_pattern": """
You have previously benchmarked a normal set of logs based on log arrival sequence profiles.
Your system logs are found to contain known predictable sequences.
During the past days, the following issues were already identified and addressed. Consider these as known issues that do not require reporting again:
{past_7_day_advisories}
On this day however, the following logs were arriving at unusual sequences compared to normal. These log lines are randomly sampled and sorted.

```
{log_lines}
```

If any issues match those already reported in the past days, mark them as already addressed and do not include them in your final analysis.
Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
assess and evaluate this findings, find potential causal relationship between log lines, and provide an analysis in under 50 words.
    """,
    # log structure with training groups and multi rounds
    "log_structure": [
        """We have previously benchmarked a normal set of groups with log samples based on log content structures. Previous system logs were found to contain known predictable structures, there maybe logs of different lengths, containing different fields, containing different contents.
Below are some samples of normal logs received in the past, note that normal logs may indicate both healthy or unhealthy underlying symptoms. Past normal groups with log samples:

```
{training_groups}
```

Understand and reason on this input, Next question to come.""",
        """During the past days, the following issues were already identified and reported. Consider these as known issues that do not require reporting again:

{past_7_day_advisories}

Understand and reason on this input. Next question to come.""",
        """From TODAY's logs, the following logs were identified to possess structures that occur at unusual frequency. Today's log groups with problematic samples:

```
{log_lines}
```

Understand and reason on this input, Next question to come.""",
        """
Analyze logs from TODAY {current_date} by comparing them with past normal sample logs. Identify any unhealthy conditions and key differences.

Based on log timestamps, determine potential causal relationships between TODAY's log entries. Understand the meaning of the logs without force-fitting issues.

Provide a concise analysis (<100 words) of NEW symptoms only, including:

1. Specific log patterns indicating anomalies
2. Exact components/services affected
3. Precise technical root cause hypothesis
4. Concrete, actionable remedy
5. Do not use bullet points or line breaks, enclose the component or entity in backticks

If all current symptoms match previously reported issues or are within normal parameters, state "No new significant issues detected today" with brief explanation.

Be technical and specific in your analysis, citing exact log fields, values, timestamps, error codes, and patterns rather than making general observations. Avoid vague terms without qualification.
IMPORTANT: Exclude any issues already reported in past days.
""",
    ],
    # log sequence pattern with training groups and multi rounds
    "sequence_pattern": [
        """We have previously benchmarked a normal set of groups with log samples based on log arrival sequence profiles. Previous system logs were found to contain known predictable sequences.
Below are some samples of normal logs received in the past, note that normal logs may indicate both healthy or unhealthy underlying symptoms. Past normal groups with log samples:

```
{training_groups}
```

Understand and reason on this input, Next question to come.""",
        """During the past days, the following issues were already identified and reported. Consider these as known issues, you MUST NOT report similar symptoms again:

{past_7_day_advisories}

Understand and reason on this input, Next question to come.""",
        """From TODAY's logs, the following logs were identified to possess structures that occur at unusual frequency. Today's logs:

```
{log_lines}
```

Understand and reason on this input, Next question to come.""",
        """Assess and evaluate today's logs, compare them to the past normal sample logs. Identify previously encountered underlying unhealthy conditions, identify key differences that stand out between today's logs and past normal sample logs.
IMPORTANT: If any issues match those already reported in the past days, mark them as already addressed and do not include them in your final analysis
Understand and reason on this input, Next question to come.""",
        """Based on timestamps of the logs, find potential causal relationship between log lines from TODAY. Understand the meaning of the logs, do not try to force-fit issues, it is common to see logs simply with little or no issue.
Understand and reason on this input, Next question to come.""",
        """
TODAY is {current_date}
Based on TODAY's new symptoms ONLY, provide an analysis of symptoms and suggest potential root cause and remedies in under 100 words. Include:
1. The specific log pattern that indicates the anomaly
2. The exact components or services affected
3. A precise technical root cause hypothesis
4. A concrete, actionable remedy
Do not use bullet points or line breaks, enclose the component or entity in backticks

If all current symptoms match previously reported issues or are within normal operational parameters, state "No new significant issues detected today." and explain a bit.
Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
""",
    ],
    "numeric_fields": """
# Log Analysis for Numeric Outliers
TODAY is {current_date}
We have previously benchmarked a trained normal set of system logs with numeric value fields.
The field name is `{field_name}`
The benchmark results show the expected numeric ranges as {expected}.

## Baseline data

**Normal Log Samples**: Benchmark logs from training period representing normal system behaviour patterns (may include both healthy and unhealthy patterns):

```
{training_groups}
```

## Previously Identified Issues
The following issues have already been identified and reported in the previous days:

{past_7_day_advisories}

## Current Outliers
Today's logs contain significant numeric outliers in the {field_name} field:

```
{log_lines}
```

IMPORTANT NOTE:
1. Exclude issues matching those already reported in the previous days. Only report on new symptoms observed TODAY.
2. if the Normal Log Samples is empty, please ignore the baseline data and only focus on the current outliers
3. If all current symptoms match previously reported issues or are within normal operational parameters, state "No new significant issues detected today." and explain a bit.


Provide a max 50 words statement of analysis on the deviation found. Specify:
1. The exact magnitude and direction of deviation
2. The technical impact this deviation has on the system
3. The most likely component causing this change

Do not use bullet points or line breaks, enclose the component or entity in backticks

Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
""",
    "notable_fields": """
TODAY is {current_date}
We have previously benchmarked a trained normal set of system logs most commonly seen top fields.
The benchmark histogram data is {expected}.
From TODAY's logs, the inference histogram data is {actual}.

## Baseline data

**Normal Log Samples**: Benchmark logs during training period representing normal system behaviour patterns (may include both healthy and unhealthy patterns):

```
{training_groups}
```

## Previously Addressed Issues
The following issues have already been identified and reported in the previous days:

{past_7_day_advisories}

IMPORTANT NOTE:
1. Exclude issues matching those already reported in the previous days. Only report on new symptoms observed TODAY.
2. if the Normal Log Samples is empty, please ignore the baseline data and only focus on the current outliers
3. If all current symptoms match previously reported issues or are within normal operational parameters, state "No new significant issues detected today." and explain a bit.

Provide a max 50 words statement of analysis on the divergence found. Include:
1. The specific fields showing the greatest deviation
2. The exact statistical change observed
3. The probable technical cause for this field frequency change

Do not use bullet points or line breaks, enclose the component or entity in backticks

Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis

""",
    "discrete_fields": """
TODAY is {current_date}
We have previously benchmarked a trained normal set of system logs discrete fields.
The field name is `{field_name}`
The benchmark histogram data is {expected}.
From TODAY's logs, the inference histogram data is {actual}.

## Baseline Data

- **Training Log Samples:** Benchmark logs during training period representing normal system behaviour patterns (may include both healthy and unhealthy patterns):

```
{training_groups}
```

## Previously Addressed Issues
The following issues have already been identified and addressed in the previous days:

{past_7_day_advisories}

IMPORTANT NOTE:
1. Exclude issues matching those already reported in the previous days. Only report on new symptoms observed TODAY.
2. if the Normal Log Samples is empty, please ignore the baseline data and only focus on the current outliers
3. If all current symptoms match previously reported issues or are within normal operational parameters, state "No new significant issues detected today." and explain a bit.

Provide a max 50 words statement of analysis on the divergence found. Focus on:
1. The specific value(s) showing divergence
2. The exact percentage or count change
3. The technical component likely causing this change in values

Do not use bullet points or line breaks, enclose the component or entity in backticks

Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
""",
    "new_discrete_fields": """
TODAY is {current_date}
We have previously benchmarked a trained normal set of system logs discrete fields.
There is a field that was not seen in the trained normal set.
The field name is `{field_name}`
From TODAY's logs, The histogram data is {actual}.

## Baseline Data

- **Training Log Samples:** Benchmark logs during training period representing normal system behaviour patterns (may include both healthy and unhealthy patterns):

```
{training_groups}
```

## Previously Addressed Issues
The following issues have already been identified and reported in the previous days:

{past_7_day_advisories}

IMPORTANT NOTE:
1. Exclude issues matching those already reported in the previous days. Only report on new symptoms observed TODAY.
2. if the Normal Log Samples is empty, please ignore the baseline data and only focus on the current outliers
3. If all current symptoms match previously reported issues or are within normal operational parameters, state "No new significant issues detected today." and explain a bit.

Provide a max 50 words statement of analysis on the new field. Specify:
1. The technical significance of this specific field appearing
2. The most common values in this new field and their meaning
3. The exact component or service likely generating this field
4. Do not use bullet points or line breaks, enclose the component or entity in backticks

Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
""",
}


class PanelAnalysis:
    def __init__(self, model: str, llamington_client: LLamingtonClient, provider: BaseProvider | None = None):
        self.model = model
        self.provider = provider
        self.llamington = llamington_client
        self.provider = provider
        self.formatter = Formatter()

    def initial_prompt(self, prompt):
        return [
            {"role": "assistant", "content": SYSTEM_INSTRUCTION},
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                ],
            },
        ]

    def _invoke_provider_completion(self, templates: list[str], args: dict):
        if self.provider is None:
            raise ValueError("Provider is not set")
        messages = [
            {"role": "system", "content": SYSTEM_INSTRUCTION},
            {
                "role": "user",
                "content": self.formatter.format(templates[0], **args),
            },
        ]
        res = self.provider.conversation(messages, temperature=0.2)
        messages.append({"role": "system", "content": res})
        for template in templates[1:]:
            prompt = self.formatter.format(template, **args)
            messages.append({"role": "user", "content": prompt})
            res = self.provider.conversation(messages, temperature=0.2)
            messages.append({"role": "system", "content": res})
        return res

    def invoke(self, *, panel: str, args: dict):
        template = PANEL_ANALYSIS_TEMPLATES[panel]

        if self.provider:
            return self._invoke_provider_completion([template] if isinstance(template, str) else template, args)

        if isinstance(template, list):
            return self._invoke_multiple_step(template, args)
        prompt = self.formatter.format(template, **args)
        run = self.llamington.create_thread(
            model=self.model,
            messages=self.initial_prompt(prompt),
            temperature=0.2,
        )
        thread_id = run["thread-id"]
        msgs = self.llamington.list_thread_messages(thread_id)
        return filter_annotations(msgs["data"][0]["content"][0])

    def invoke_with_image(self, *, panel: str, args: dict, image: str):
        template = PANEL_ANALYSIS_TEMPLATES[panel]
        prompt = self.formatter.format(template, **args)
        messages = self.initial_prompt(prompt)
        messages[1]["content"].append({"type": "image_url", "image_url": {"url": image}})
        run = self.llamington.create_thread(
            model=self.model,
            messages=messages,
            temperature=0.2,
        )
        msgs = self.llamington.list_thread_messages(run.thread_id)
        return filter_annotations(msgs["data"][0]["content"][0])

    def _invoke_multiple_step(self, templates: list[str], args: dict):
        messages = self.initial_prompt(self.formatter.format(templates[0], **args))
        run = self.llamington.create_thread(
            model=self.model,
            messages=messages,
            temperature=0.2,
        )
        thread_id = run["thread-id"]
        for template in templates[1:]:
            prompt = self.formatter.format(template, **args)
            self.llamington.append_thread_message(thread_id, {"role": "user", "content": prompt})
        msgs = self.llamington.list_thread_messages(thread_id)
        return filter_annotations(msgs["data"][0]["content"][0])
