import re

from llm.providers import BaseProvider
from llm.utils import remove_thinking
from vector_db.vector_search import VectorSearch

EXISTING_SYMPTOM_SYSTEM_INSTRUCTION = """
You are a senior site reliability engineer analyzing enterprise IT infrastructure logs.

IMPORTANT GUIDELINES:
- Configuration changes by operators are EXPECTED and NORMAL
- Focus only on high-severity or clear anomalies that haven't been reported before
- Correlate related symptoms to identify root causes rather than reporting individual alerts
"""

SYMPTOM_INITIAL_PROMPT_TEMPLATE = """
TODAY is {current_date}

Analyze today's symptoms against past advisories to identify if the symptom is in the past symptoms. I'll provide previous advisories for context.

## Previously Addressed Issues
The following issues have already been identified and reported in the previous days:

{past_advisories}

## TODAY's symptom

{symptom}

## Instructions
1. First, review and understand all previously addressed issues listed above
2. I will send you symptoms one by one from today's logs
3. For each symptom I share:
   - Compare it with the previously addressed issues
   - Determine if it represents a new issue or relates to a known problem
   - For known issues, state "No new significant issues detected today", and briefly note which previous advisory it relates to
   - For new issues, explain what the symptom indicates and its potential significance
4. Focus only on identifying truly new problems that require attention

Tell me if it's new or related to a previously identified issue. Ignore the timestamp
"""

SYMPTOM_FINAL_YES_OR_NO_PROMPT_TEMPLATE = """
if the symptom is in the past advisories or it's a known issue, please give response "YES",
if it's a new issue, please give response "NO"
"""

PAST_LOGS_SYSTEM_INSTRUCTION = """
You are a senior site reliability engineer analyzing enterprise IT infrastructure logs.
Your task is to identify if the logs I provided are in the past logs.
"""

LOGS_INITIAL_PROMPT_TEMPLATE = """
TODAY is {current_date}

Here are past logs that have been previously addressed:
{past_logs}

## TODAY's Logs

{logs}

There are a total of {num_logs} logs to analyze.

Please analyze each new log and determine if it has already appeared in the past logs.

Important instructions:
- Compare the content/error message of each log, NOT the timestamps
- Focus on the issue described and the meanings in each log
- Two logs are considered the same if they only have minor details differ

Please respond in this exact format. they should be wrapped in square brackets:
[log_index] -> [YES/NO]

Where:
- [log_index] is the number of the log in the list I'll provide
- [YES] means this log already exists in the past logs (duplicate)
- [NO] means this log is new and not found in past logs

Example response:

```
[1] -> [YES]
[2] -> [YES]
[3] -> [NO]
```

Your response must include an entry for EVERY log I provide, maintaining the same order. You must return the format like I provided in the example.

"""

LOGS_RETRY_TEMPLATE = """
You didn't return all the results I provided or you didn't follow the format I requested.
Please retry.
"""


regex = re.compile(r"\[(\d+)\]\s*->\s*\[?(yes|no)\]?", re.IGNORECASE)


def _parse_logs_res(content: str) -> dict[int, bool]:
    res = {}
    for index, tag in regex.findall(content):
        if tag.lower() == "yes":
            res[int(index)] = True
        elif tag.lower() == "no":
            res[int(index)] = False
    return res


class PastSuppression:
    def __init__(self, provider: BaseProvider, vector_search: VectorSearch, date: str):
        self.provider = provider
        self.vector_search = vector_search
        self.current_date = date

    def _token_limit(self, messages: list[dict], logs: list[str]):
        model = self.provider.model
        remaining_tokens = model.token_limit
        for msg in messages:
            remaining_tokens -= model.calculate_token(msg["content"])
        log_count = 0
        for log in logs:
            remaining_tokens -= model.calculate_token(log)
            log_count += 1
            if remaining_tokens < 0:
                return logs[:log_count]
        return logs

    def existed_symptom(self, symptom: str, current_date: str) -> tuple[str, bool]:
        similar_symptoms = self.vector_search.search_symptoms(symptom, self.current_date)
        if not similar_symptoms:
            return "", False
        past_advisories = "\n".join([p.content for p in similar_symptoms])
        messages = [
            {"role": "system", "content": EXISTING_SYMPTOM_SYSTEM_INSTRUCTION},
            {
                "role": "user",
                "content": SYMPTOM_INITIAL_PROMPT_TEMPLATE.format(
                    past_advisories=past_advisories,
                    current_date=current_date,
                    symptom=symptom,
                ),
            },
        ]
        symptom_res = self.provider.conversation(messages, temperature=0.2, keep_thinking_tag=True)
        messages.append({"role": "assistant", "content": symptom_res})
        messages.append({"role": "user", "content": SYMPTOM_FINAL_YES_OR_NO_PROMPT_TEMPLATE})
        existed_str = self.provider.conversation(messages, temperature=0.2, keep_thinking_tag=True)
        if self.provider.model.thinking:
            symptom_res = remove_thinking(symptom_res)
            existed_str = remove_thinking(existed_str)
        return symptom_res, "YES" in existed_str

    def existed_logs(self, logs: list[str]) -> dict[int, bool]:
        similar_logs = self.vector_search.search_logs(logs, self.current_date)
        if not similar_logs:
            return {}
        past_log_lines = [line for log_batch in similar_logs for line in log_batch.split("\n")]
        limited_past_log_lines = self._token_limit(
            [{"content": LOGS_INITIAL_PROMPT_TEMPLATE + "\n".join(logs)}], past_log_lines,
        )
        messages = [
            {"role": "system", "content": PAST_LOGS_SYSTEM_INSTRUCTION},
            {
                "role": "user",
                "content": LOGS_INITIAL_PROMPT_TEMPLATE.format(
                    past_logs="\n".join(limited_past_log_lines),
                    current_date=self.current_date,
                    num_logs=len(logs),
                    logs="\n".join([f"{i}. {log}" for i, log in enumerate(logs)]),
                ),
            },
        ]
        logs_res = self.provider.conversation(messages, temperature=0.4, keep_thinking_tag=True)
        messages.append({"role": "assistant", "content": logs_res})
        logs_res_content = logs_res
        if self.provider.model.thinking:
            logs_res_content = remove_thinking(logs_res)
        parsed_result = _parse_logs_res(logs_res_content)
        if len(parsed_result) != len(logs):
            messages.append({"role": "user", "content": LOGS_RETRY_TEMPLATE})
            logs_res = self.provider.conversation(messages, temperature=0.4, keep_thinking_tag=True)
            logs_res_content = logs_res
            if self.provider.model.thinking:
                logs_res_content = remove_thinking(logs_res)
            new_parsed_result = _parse_logs_res(logs_res_content)
            if len(new_parsed_result) == len(logs):
                return new_parsed_result
            if len(new_parsed_result) > len(parsed_result):
                parsed_result = new_parsed_result
        return parsed_result
