from abc import ABC, abstractmethod

import tiktoken


class BaseModel:
    def __init__(
        self,
        name: str,
        token_limit: int,
        token_model="cl100k_base",
        token_estimate_adjustment_factor: float = 1,
        thinking: bool = False,
    ):
        self.name = name
        self.token_limit = token_limit
        self.token_model = token_model
        self.token_estimate_adjustment_factor = token_estimate_adjustment_factor
        self.enc = tiktoken.get_encoding(self.token_model)
        self.thinking = thinking

    def calculate_token(self, prompt: str):
        return len(self.enc.encode(prompt)) * self.token_estimate_adjustment_factor

    def __repr__(self):
        return (
            f"BaseModel(name={self.name!r}, token_limit={self.token_limit}, "
            f"token_model={self.token_model!r}, "
            f"token_estimate_adjustment_factor={self.token_estimate_adjustment_factor}, "
            f"thinking={self.thinking})"
        )


class BaseProvider(ABC):
    def __init__(self, model: BaseModel):
        self.model = model

    @abstractmethod
    def invoke(self, prompt: str, *, keep_thinking_tag: bool = False, add_no_think_instruction: bool = False, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def conversation(self, messages: list, *, keep_thinking_tag: bool = False, **kwargs):
        raise NotImplementedError

    @abstractmethod
    def invoke_with_image(self, prompt: str, image_url: str, **kwargs):
        raise NotImplementedError
