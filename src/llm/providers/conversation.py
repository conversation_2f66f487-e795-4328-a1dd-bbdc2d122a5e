import uuid
from copy import deepcopy
from dataclasses import dataclass, field

from llm.providers import BaseProvider


@dataclass
class LLMThread:
    thread_id: str
    messages: list = field(default_factory=list)
    temperature: float | None = None


class LLMConversation:
    def __init__(self, provider: BaseProvider):
        self.provider = provider
        self.threads: dict[str, LLMThread] = {}

    def invoke_completion(self, thread_id: str, temperature: float | None = None):
        if thread_id not in self.threads:
            msg = f"Thread {thread_id} does not exist."
            raise ValueError(msg)
        thread = self.threads[thread_id]
        res = self.provider.conversation(thread.messages, temperature=temperature)
        thread.messages.append({"role": "system", "content": res})
        return res

    def create_thread(self, model: str, messages: list, temperature: float | None = None):
        thread_id = str(uuid.uuid4())
        self.threads[thread_id] = LLMThread(thread_id=thread_id, temperature=temperature)
        copied_messages = deepcopy(messages)
        for message in copied_messages:
            if "role" in message and message["role"] == "assistant":
                message["role"] = "system"
        self.threads[thread_id].messages = copied_messages
        self.invoke_completion(thread_id, temperature=temperature)
        return {"thread-id": thread_id}

    def append_thread_message(self, thread_id, message):
        if thread_id not in self.threads:
            msg = f"Thread {thread_id} does not exist."
            raise ValueError(msg)
        self.threads[thread_id].messages.append(message)
        self.invoke_completion(thread_id)

    def list_thread_messages(self, thread_id):
        if thread_id not in self.threads:
            msg = f"Thread {thread_id} does not exist."
            raise ValueError(msg)
        return {
            "data": [
                {
                    "role": message["role"],
                    "content": [{"text": {"value": message["content"]}}],
                }
                for message in reversed(self.threads[thread_id].messages)
            ],
        }
