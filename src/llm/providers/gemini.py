import google.generativeai as genai
from google.generativeai.types import HarmBlockThreshold, HarmCategory

from .base import BaseModel, BaseProvider


class GeminiProvider(BaseProvider):
    models = {
        "gemini-1.5-pro-latest": BaseModel(
            name="gemini-1.5-pro-latest",
            token_limit=1_000_000,
            # gemini doesn't have a tokenizer so we just use a rough estimate
            token_estimate_adjustment_factor=1.25,
        ),
    }

    def __init__(self, gemini_api_key: str, model: BaseModel):
        super().__init__(model)
        self.gemini_api_key = gemini_api_key
        self.model = model

    def get_model(self):
        genai.configure(api_key=self.gemini_api_key)
        return genai.GenerativeModel(
            self.model.name,
            safety_settings={
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            },
            # system_instruction=SYSTEM_INSTRUCTIONS,
        )

    def invoke(self, prompt: str, *, keep_thinking_tag: bool = False, add_no_think_instruction: bool = False, **kwargs):
        response = self.get_model().generate_content(prompt)
        return response.text

    def invoke_with_image(self, prompt: str, image_url: str, **kwargs):
        raise NotImplementedError

    def conversation(self, messages: list, *, keep_thinking_tag: bool = False, **kwargs):
        raise NotImplementedError
