import httpx
from openai import OpenAI
from tenacity import retry, stop_after_attempt, wait_random

from llm.utils import remove_thinking

from .base import BaseModel, BaseProvider


class FFWDLogger:
    def __init__(self, target_url: str):
        self.target_url = target_url
        if target_url:
            self.ffwd_client = httpx.Client()
        else:
            self.ffwd_client = None

    def log(self, content):
        if not self.ffwd_client:
            return
        try:
            self.ffwd_client.post(self.target_url, json=content)
        except Exception as e:
            print(f"Failed to log to ffwd: {e}")


class OpenAIProvider(BaseProvider):
    models = {
        "gpt-4o": BaseModel(
            # limit 10k token per requests
            # as we are at openai tire1, only have 30k tpm.
            # ref https://platform.openai.com/docs/guides/rate-limits/usage-tiers
            name="gpt-4o",
            token_limit=80_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "gpt-4.1": BaseModel(
            name="gpt-4.1",
            token_limit=80_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "gpt-4.1-mini": BaseModel(
            name="gpt-4.1-mini",
            token_limit=60_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "gpt-o3-mini": BaseModel(
            # limit 10k token per requests
            # as we are at openai tire1, only have 30k tpm.
            # ref https://platform.openai.com/docs/guides/rate-limits/usage-tiers
            name="gpt-o3-mini",
            token_limit=150_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "gpt-4o-mini": BaseModel(
            name="gpt-4o-mini",
            token_limit=80_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "gpt-3.5-turbo": BaseModel(
            name="gpt-3.5-turbo",
            token_limit=16_000,
            token_model="cl100k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "qwen-qwq-32b": BaseModel(
            name="qwen-qwq-32b",
            token_limit=80_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
            thinking=True,
        ),
        "openrouter:qwq-32b": BaseModel(
            name="qwen/qwq-32b",
            token_limit=80_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
            thinking=True,
        ),
        "qwen3": BaseModel(
            name="qwen3:14b",
            token_limit=30_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.3,
            thinking=True,
        ),
        "qwen3:14b": BaseModel(
            name="qwen3:14b",
            token_limit=32_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.3,
            thinking=True,
        ),
        "qwen3:8b": BaseModel(
            name="qwen3:8b",
            token_limit=30_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.3,
            thinking=True,
        ),
        "openrouter:qwen3:235b": BaseModel(
            name="qwen/qwen3-235b-a22b",
            token_limit=30_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.3,
            thinking=True,
        ),
        "llama3.1": BaseModel(
            name="llama3.1",
            token_limit=10_000,
            token_model="cl100k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "openrouter:gemini-2.5-flash": BaseModel(
            name="google/gemini-2.5-flash",
            token_limit=128_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "openrouter:gpt-4.1-mini": BaseModel(
            name="openai/gpt-4.1-mini",
            token_limit=60_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "openrouter:gpt-4.1": BaseModel(
            name="openai/gpt-4.1-mini",
            token_limit=60_000,
            token_model="o200k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "meta-llama/llama-4-scout-17b-16e-instruct": BaseModel(
            name="meta-llama/llama-4-scout-17b-16e-instruct",
            token_limit=100_000,
            token_model="cl100k_base",
            token_estimate_adjustment_factor=1.1,
        ),
        "qwen-turbo": BaseModel(
            name="qwen-turbo",
            token_limit=50_000,
            token_model="cl100k_base",
            token_estimate_adjustment_factor=1.1,
        ),
    }

    @classmethod
    def get_model(
        cls,
        model_name: str,
        token_limit: int | None = None,
        thinking_model: bool | None = False,
    ) -> BaseModel:
        if model_name not in cls.models:
            return BaseModel(
                name=model_name,
                token_limit=token_limit or 30_000,
                token_model="cl100k_base",
                thinking=thinking_model or False,
                token_estimate_adjustment_factor=1.1,
            )
        return cls.models[model_name]

    def __init__(
        self,
        openai_api_key: str,
        model: BaseModel,
        system_instruction: str | None = None,
        openai_url: str | None = None,
    ):
        super().__init__(model)
        self.openai_api_key = openai_api_key
        self.system_instruction = system_instruction
        self.client = OpenAI(api_key=self.openai_api_key, base_url=openai_url if openai_url else None)
        self.ffwd_logger = FFWDLogger(  # flow 1a8709fc-c9ef-43cd-a148-664e3b7473af
            "https://public.varin.dc.ffwd.dev/write/uhukdyjrzl003a25h7308jw41apwdu0i?key=3gI7Ptk7fuWAoPyxS5h6RSqNtFXZjC8D",
        )

    @retry(wait=wait_random(min=180, max=600), stop=stop_after_attempt(3))
    def invoke(self, prompt: str, *, keep_thinking_tag: bool = False, add_no_think_instruction: bool = False, **kwargs):
        messages = []
        if self.system_instruction:
            messages.append({"role": "system", "content": self.system_instruction})
        if self.model.thinking and add_no_think_instruction:
            prompt = " /no_think " + prompt + " /no_think /nothink "
        messages.append({"role": "user", "content": prompt})
        try:
            completion = self.client.chat.completions.create(
                n=1,
                model=self.model.name,
                messages=messages,
                **kwargs,
            )
        except Exception as e:
            self.ffwd_logger.log(
                {
                    "prompt": prompt,
                    "error": str(e),
                    "model": self.model.name,
                    "func": "invoke",
                },
            )
            raise
        res = completion.choices[0].message.content
        self.ffwd_logger.log(
            {
                "prompt": prompt,
                "response": res,
                "model": self.model.name,
                "func": "invoke",
            },
        )
        if not keep_thinking_tag and self.model.thinking:
            return remove_thinking(res)
        return res

    @retry(wait=wait_random(min=180, max=600), stop=stop_after_attempt(5))
    def conversation(self, messages: list, *, keep_thinking_tag: bool = False, **kwargs):
        try:
            completion = self.client.chat.completions.create(
                n=1,
                model=self.model.name,
                messages=messages,
                **kwargs,
            )
        except Exception as e:
            self.ffwd_logger.log(
                {
                    "messages": messages,
                    "error": str(e),
                    "model": self.model.name,
                    "func": "conversation",
                },
            )
            raise
        res = completion.choices[0].message.content
        self.ffwd_logger.log(
            {
                "messages": messages,
                "response": res,
                "model": self.model.name,
                "func": "conversation",
            },
        )
        if not keep_thinking_tag and self.model.thinking:
            return remove_thinking(res)
        return res

    def invoke_with_image(self, prompt: str, image_url: str, **kwargs):
        messages = []
        if self.system_instruction:
            messages.append({"role": "system", "content": self.system_instruction})
        messages.append({
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {"type": "image_url", "image_url": {"url": image_url}},
            ],
        })
        completion = self.client.chat.completions.create(
            n=1,
            model=self.model.name,
            messages=messages,
            **kwargs,
        )
        res = completion.choices[0].message.content
        self.ffwd_logger.log({
            "prompt": prompt,
            "image_url": image_url,
            "response": res,
        })
        return res
