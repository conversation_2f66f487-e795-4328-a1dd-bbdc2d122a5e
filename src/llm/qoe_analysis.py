from string import <PERSON>atter

import pandas as pd

from anomaly_detector.marker import Prom<PERSON>ark<PERSON>
from anomaly_detector.sks.marker import <PERSON>Marker
from anomaly_detector.sks.marker_descriptions import get_marker_description
from llm.providers import BaseProvider

SYSTEM_INSTRUCTION_TEMPLATE = """
You are a site reliability engineer, monitoring enterprise IT infrastructure logs.

For QoE (Quality of Experience) analysis, you have access to the following technical knowledge base:

{qoe_context}

Use this knowledge base to provide accurate analysis of wireless network performance metrics, understanding the specific calculation methods and relationships between different QoE components.
"""

QOE_TEMPLATE = """
Here are the metrics for {qoe_name}. This QoE is about {qoe_description}

related metrics:

{metrics}

The Qoe is based on these sub qoes:
{sub_qoes}

Here are today's scores of these qoes,
{qoe_scores}

Here are the related metrics:
{metric_advisories}

Describe in 50 words the symptom observed.
Reason on the symptom, referencing knowledge base and various available input.
Describe in 50 words the probable root cause.
Response with one paragraph, and do not use any bullet points.
"""

SUB_MARKER_TEMPLATE = """
analyze the submarker {marker_name},
Describe in 50 words the symptom observed.
Reason on the symptom, referencing knowledge base and various available input.
Describe in 50 words the probable root cause.
Response with one paragraph, and do not use any bullet points.
"""


class QoEAnalysis:
    def __init__(self, provider: BaseProvider, qoe_context: str = ""):
        self.provider = provider
        self.formatter = Formatter()
        self.qoe_context = qoe_context

    def _get_system_instruction(self):
        """Generate system instruction with QoE calculation context."""
        return self.formatter.format(
            SYSTEM_INSTRUCTION_TEMPLATE,
            qoe_context=self.qoe_context,
        )

    def invoke(
        self,
        *,
        qoe_marker: CustomMarker,
        qoe_scores: pd.DataFrame,
        related_metrics: list[PromMarker],
        metric_advisories: dict[str, dict],
    ):
        metrics_str = "\n".join(
            [f"`{marker.name}`: {get_marker_description(marker.name, 'zh-cn')}" for marker in related_metrics],
        )
        sub_qoes_str = "\n".join(
            [f"`{marker.name}`: {get_marker_description(marker.name, 'zh-cn')}" for marker in qoe_marker.sub_markers],
        )
        qoe_scores_str = qoe_scores.resample("10min").mean().to_csv(index=False, float_format="%.2f")
        metric_advisories_list = []
        for marker in related_metrics:
            for k, v in metric_advisories.items():
                if marker.name in k:
                    content = v.get("content", "")
                    if content:
                        metric_advisories_list.append(f"`{marker.name}`: {content}")

        prompt = self.formatter.format(
            QOE_TEMPLATE,
            qoe_name=qoe_marker.name,
            qoe_description=get_marker_description(qoe_marker.name, lang="zh-cn"),
            metrics=metrics_str,
            sub_qoes=sub_qoes_str,
            qoe_scores=qoe_scores_str,
            metric_advisories="\n".join(metric_advisories_list),
        )
        messages = [
            {"role": "system", "content": self._get_system_instruction()},
            {"role": "user", "content": prompt},
        ]
        results = {}
        res = self.provider.conversation(messages, temperature=0.2)
        results[qoe_marker.name] = res
        messages.append({"role": "assistant", "content": res})
        for sub_marker in qoe_marker.sub_markers:
            prompt = self.formatter.format(SUB_MARKER_TEMPLATE, marker_name=sub_marker.name)
            messages.append({"role": "assistant", "content": prompt})
            res = self.provider.conversation(messages, temperature=0.2)
            results[sub_marker.name] = {"content": res}
        return results
