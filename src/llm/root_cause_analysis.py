from collections.abc import Callable, Iterator
from dataclasses import dataclass
from string import Formatter

from llm.llamington import <PERSON>amingtonClient
from llm.providers import BaseProvider
from llm.providers.conversation import LLMConversation
from llm.utils import filter_annotations, parse_res_to_normal_log_groups

SYSTEM_INSTRUCTION = """
You are a site reliability engineer, monitoring enterprise IT infrastructure logs.
Check into project files or knowledge base to analyze and provide background information.
If you tried to use the file_search, please include the reference and mention it in your response.
If there are no documents or you haven't used documents, ignore it.
IMPORTANT: this network or system may have frequent configuration changes performed by operators, which is considered normal
"""


@dataclass
class PromptGuide:
    system_instruction: str = SYSTEM_INSTRUCTION
    is_symptom: bool = False
    prompts: str = ""
    steps: list[str] | None = None
    prompt_generator: Callable[[dict], Iterator[str]] | None = None
    output_as: str = "output"
    parser: Callable[[str, dict], str] | None = None
    with_score: bool = False


def with_logs_parser(res: str, args: dict) -> str:
    if "original_log_lines" not in args:
        return res
    log_lines = args["original_log_lines"]
    groups = parse_res_to_normal_log_groups(log_lines, res)
    return "\n".join([group.to_prompt(sample_size=20) for group in groups])


PromptFlow = list[PromptGuide] | PromptGuide

SYMPTOM_SYSTEM_INSTRUCTION = """
You are a senior site reliability engineer analyzing enterprise IT infrastructure logs.
Check into project files or knowledge base to analyse and provide background information.
If you tried to use the file_search, please include the reference and mention it in your response.
If there are no documents or you haven't used documents, ignore it.

IMPORTANT GUIDELINES:
- Configuration changes by operators are EXPECTED and NORMAL
- Focus only on high-severity or clear anomalies that haven't been reported before
- Correlate related symptoms to identify root causes rather than reporting individual alerts
- Ignore any symptoms that match or closely resemble issues in past advisories
"""


SYMPTOM_INITIAL_PROMPT_TEMPLATE = """
Analyze today's symptoms against past advisories to identify ONLY NEW and SIGNIFICANT issues. I'll provide previous advisories for context.

## Previously Addressed Issues
The following issues have already been identified and reported in the previous days:

{past_7_day_advisories}

## Instructions
1. First, review and understand all previously addressed issues listed above
2. I will send you symptoms one by one from today's logs
3. For each symptom I share:
   - Compare it with the previously addressed issues
   - Determine if it represents a new issue or relates to a known problem
   - For new issues, explain what the symptom indicates and its potential significance
   - For known issues, state "No new significant issues detected today, and briefly note which previous advisory it relates to
4. Focus only on identifying truly new problems that require attention

I'll sharing today's symptom. Tell me if it's new or related to a previously identified issue.
"""

SYMPTOM_SUMMARY_PROMPT_TEMPLATE = """
Following are the analysis of today's symptoms:

{symptoms}

Review and Provide a concise summary of ONLY NEW significant issues that require attention.
If all current symptoms are previously reported issues or are within normal operational parameters, state "No new significant issues detected today.".
Include specific metrics, timestamps, and affected services for any new issues identified.
Do not use bullet points or line breaks, enclose the component or entity in backticks. Limit your analysis to 150 words total.
"""

SYMPTOM_TODAY_TEMPLATE = """
TODAY is {current_date}
One of TODAY's symptoms:
{symptom}

----------
Compare with the previously addressed issues and determine if it represents a new issue or relates to a known problem.
If it's a new issue, explain what the symptom indicates and its potential significance. Focus only on identifying truly new problems that require attention. Limit your analysis to 150 words total
If it's related to a known issue, state "known issue" and brief explain in less than 50 words.
"""

severity_score = """
# Severity Score

## Context
Based on the root cause analysis for TODAY as provided from the previous response, do the following task:

## Scoring Instructions
1. Provide a severity score based on the root cause identified from TODAY
2. If the issues reported or root cause are similar to those already identified previously in Benchmark Log Pattern or previous past days reports, you must assign a score of 0
3. Use the following scale:
   - 0 = No significant issues or previously known issues (all within normal parameters)
   - 0.25 = Minor issue (minimal operational impact, low security threat)
   - 0.5 = Moderate issue (noticeable but not critical impact)
   - 0.75 = Serious issue (significant operational impact)
   - 1 = Critical issue (severe disruption or very high security threat)
4. Output only the numerical score value (e.g., "0.5") without any explanation or additional text
"""

root_cause_summary: PromptFlow = [
    PromptGuide(
        output_as="keywords_symptoms",
        system_instruction="""
You are an expert log analysis system designed to detect NEW anomalies in IT infrastructure logs. Your primary function is to distinguish between:
1. Known log patterns (even if they contain errors/warnings) that are part of normal operations
2. Genuinely new symptoms that require attention

CRITICAL: This environment has frequent configuration changes by operators which are NORMAL. Many logs that appear concerning are actually expected. Your value comes from identifying truly new issues, not reacting to known patterns.
        """,
        steps=[
            """
Below are BASELINE LOG GROUPS that represent NORMAL system behaviour. Study these carefully - they define what is considered normal in this environment.

IMPORTANT: These baseline logs may contain errors, warnings, and concerning messages that are EXPECTED and ACCEPTABLE. Your job is to learn these patterns and underlying reason as normal.

Baseline log groups:

```
{training_groups}
```

Analyze these baseline logs and identify the key patterns, error types, and message structures that define "normal" in this environment.
            """,
            """
Now examine TODAY'S LOGS collected on {current_date}:

```
{keywords_log_lines}
```

Compare these logs to the baseline patterns you identified. Focus on semantic understanding rather than exact text matching.
           """,
            """
For each log in today's logs, determine:

1. Does it semantically match a pattern from the baseline? If yes, which one?
2. Does it represent a new pattern not seen in the baseline?

Be specific about which logs match baseline patterns and which ones don't. Use concrete evidence from the logs to support your classifications.""",
            """
FOCUS ONLY on logs that do NOT match baseline patterns.

For each non-matching log:
1. Is it truly a new symptom or just a variation of a known pattern?
2. What specific evidence indicates this is a genuinely new issue?
3. What components or services are affected?
4. What is the likely technical root cause?

If you cannot identify any genuinely new symptoms, explicitly state "No new symptoms detected" and explain why the non-matching logs are likely normal variations.

DO NOT FORCE YOURSELF TO FIND ISSUES. It is completely acceptable and expected to report no new symptoms.""",
            """
Based on your analysis of TODAY'S logs compared to the BASELINE, provide your final assessment in a single paragraph without markdown formatting:

If no new symptoms were detected:
- Begin with "No new significant issues detected today"
- Briefly explain which logs might appear concerning but match baseline patterns

If new symptoms were detected:
- Begin with a clear statement of the new issue
- Include specific log evidence (exact timestamps, error codes, field values)
- Identify affected components/services
- Provide technical root cause hypothesis and actionable remedy
- Do not use bullet points or line breaks, enclose the component or entity in backticks

Be precise, technical, and evidence-based. Limit your analysis to 150 words total.""",
        ],
    ),
    PromptGuide(
        output_as="log_lines",
        parser=with_logs_parser,
        prompts="""Based on knowledge of your IT system and provided knowledge base,
Assess and evaluate today's logs, compare them to the past normal sample logs. Identify previously encountered underlying unhealthy conditions, identify key differences that stand out between today's logs and past normal sample logs.
IMPORTANT: If any issues or entities are already in the past normal sample logs, you MUST NOT flag the issue again in this report.

## Baseline Data
- **Normal Log Samples:** Benchmark logs patterns during training period representing normal system behaviour patterns (may include both healthy and unhealthy patterns):

```
{training_groups}
```

## Symptomatic log lines
Today's sample of symptomatic log lines, not in particular order:

```
{log_lines}
```

## Output Format:
only output the index of today's log lines that are symptomatic and not in the past normal sample logs.
And re-categorize the symptomatic log lines into groups.

with the following format. The NUMBER is the index of the log line in Symptomatic log lines.

```
<group>
<title>TITLE OF GROUP</title>
<description>Concise description of what these logs represent and why they're grouped</description>
<samples>
NUMBER
NUMBER
NUMBER
</samples>
</group>
```
""",
    ),
    PromptGuide(
        is_symptom=True,
        output_as="symptoms",
    ),
    PromptGuide(
        with_score=True,
        system_instruction="""
You are a senior site reliability engineer specializing in log analysis and incident response. Your task is to analyze enterprise IT infrastructure logs to identify new issues.

Key responsibilities:
- Analyze both reported symptoms AND log lines with equal importance
- Correlate symptoms with specific evidence in log entries
- Identify technical relationships between different symptoms
- Determine root causes based on log patterns and timestamps
- Reference knowledge base information when relevant

Important guidelines:
- Frequent configuration changes by operators are normal and expected
- Focus only on NEW issues from today's data
- Do not report previously identified issues
- Make your own determination based on evidence, not past conclusions
- Provide concise, technical analysis without unnecessary elaboration
""",
        steps=[
            """
TODAY is {current_date}

## TODAY Symptoms

TODAY's current analysis:

{symptoms}

## TODAY Symptomatic log lines

Today's sample of symptomatic log lines, not in particular order:

```
{log_lines}
```

## Previously Addressed Issues

The following issues have already been identified and reported in the previous days:

```
{past_7_day_advisories}
```

Analyze both the symptoms and log lines above to:
1. Identify technical correlations between symptoms and specific log entries
2. Determine if these represent new issues not previously reported
3. Extract exact timestamps, error codes, and anomalous values as evidence
4. Establish a chronological sequence of events if multiple issues exist

If a root cause is identified, provide:
- The specific technical failure point
- The exact component or configuration responsible
- Quantifiable metrics showing the impact
- Supporting log evidence with precise references

IMPORTANT REQUIREMENTS:
- Analyze BOTH symptoms AND log lines equally - the logs may contain critical information not mentioned in symptoms
- Only report NEW issues from TODAY
- Do not report previously identified issues
- If you cannot identify any new issues, explicitly state "No new significant issues detected today"
- Respond with a single paragraph of 150 words or less without markdown paragraph breaks or bullet points and enclose the component or entity in backticks
""",
            severity_score,
        ],
    ),
]

root_cause_analysis_prompts: dict[str, PromptFlow] = {
    "root_cause_summary": root_cause_summary,
    "advise_summary": PromptGuide(
        with_score=True,
        steps=[
            """
TODAY is {current_date}
In the context of your IT system, correlate all observed information and provide advice.
Summarise in less than 120 words and include:
1. The exact technical action needed to resolve the issue
2. Specific components that need configuration changes
3. Precise monitoring metrics to watch during remediation
4. Concrete validation steps to confirm resolution
5. Do not use bullet points and markdown format and enclose the component or entity in backticks

## Baseline Data
- **Normal Log Samples:** Historical patterns representing expected system behaviour

```
{training_groups}
```

## Previously Addressed Issues
The following issues have already been identified and reported in the previous days:

```
{past_7_day_advisories}
```

## Observed Symptoms

{symptoms}

## Log Evidence
Unordered symptomatic log lines:

```
{log_lines}
```

NOTE:
1. Exclude any causes matching those already identified in:
   - Past normal log patterns
   - Issues addressed in the past days. if the Normal Log Samples is empty, please ignore the baseline data and only focus on the current outliers
2.  If you tried to use the file_search, please include the reference and mention it in your response.

Be specific and technical in your analysis. Cite exact log fields, values, and patterns rather than general observations. Avoid vague terms like 'issue' or 'problem' without qualification
Reference specific evidence from the logs (exact timestamps, error codes, or field values) that supports your analysis
""",
            severity_score,
        ],
    ),
}

RETRY_INSTRUCTIONS = """
Prompt:
Output only the numerical score value (e.g., "0.5") without any explanation or additional text
Completion:
{completion}

Above, the Completion did not satisfy the constraints given in the Prompt.
Please try again
"""


class RootCauseAnalysis:
    def __init__(self, model: str, llamington_client: LLamingtonClient, provider: BaseProvider | None = None):
        self.model = model
        self.conversation_client = llamington_client
        self.provider = provider
        if self.provider:
            self.conversation_client = LLMConversation(self.provider)
        self.formatter = Formatter()

    def _initial_prompt(self, prompt, system_instruction=SYSTEM_INSTRUCTION):
        return [
            {"role": "assistant", "content": system_instruction},
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                ],
            },
        ]

    def _parse_score(self, score_res: str) -> float:
        score = float(score_res)
        if score < 0 or score > 1:
            raise ValueError("Score out of range")
        return score

    def _retry(self, thread_id: str, score_res: str, retry_count: int = 0) -> float:
        self.conversation_client.append_thread_message(
            thread_id,
            {"role": "user", "content": self.formatter.format(RETRY_INSTRUCTIONS, completion=score_res)},
        )
        msgs = self.conversation_client.list_thread_messages(thread_id)
        score_res = msgs["data"][0]["content"][0]["text"]["value"]
        try:
            score = self._parse_score(score_res)
        except Exception:
            score = 0 if retry_count >= 3 else self._retry(thread_id, score_res, retry_count + 1)
        return score

    def _create_thread_run(
        self,
        prompt: str,
        system_instruction: str,
    ):
        messages = self._initial_prompt(prompt, system_instruction=system_instruction)
        return self.conversation_client.create_thread(
            model=self.model,
            messages=messages,
            temperature=0.2,
        )

    def _invoke_prompt_guide_prompts(self, prompt_guide: PromptGuide, args: dict):
        run = self._create_thread_run(
            self.formatter.format(prompt_guide.prompts, **args),
            prompt_guide.system_instruction,
        )
        thread_id = run["thread-id"]
        msgs = self.conversation_client.list_thread_messages(thread_id)
        content = filter_annotations(msgs["data"][0]["content"][0])
        if prompt_guide.parser:
            content = prompt_guide.parser(content, args)
        if prompt_guide.output_as:
            args[prompt_guide.output_as] = content
        return content

    def _invoke_symptoms(self, prompt_guide: PromptGuide, args: dict):
        symptoms = args["symptoms"] + [args["keywords_symptoms"]]
        all_analysis = []
        if not symptoms:
            return ""
        for symptom in symptoms:
            run = self._create_thread_run(
                self.formatter.format(SYMPTOM_INITIAL_PROMPT_TEMPLATE, **args),
                system_instruction=SYMPTOM_SYSTEM_INSTRUCTION,
            )
            thread_id = run["thread-id"]
            self.conversation_client.append_thread_message(
                thread_id,
                {
                    "role": "user",
                    "content": self.formatter.format(SYMPTOM_TODAY_TEMPLATE, symptom=symptom, **args),
                },
            )
            msgs = self.conversation_client.list_thread_messages(thread_id)
            content = filter_annotations(msgs["data"][0]["content"][0])
            all_analysis.append(content)
        run = self._create_thread_run(
            self.formatter.format(SYMPTOM_SUMMARY_PROMPT_TEMPLATE, symptoms="\n".join(all_analysis)),
            system_instruction=SYMPTOM_SYSTEM_INSTRUCTION,
        )
        thread_id = run["thread-id"]
        msgs = self.conversation_client.list_thread_messages(thread_id)
        content = filter_annotations(msgs["data"][0]["content"][0])
        if prompt_guide.output_as:
            args[prompt_guide.output_as] = content
        return content

    def _invoke_prompt_guide_steps(self, prompt_guide: PromptGuide, args: dict):
        if not prompt_guide.steps:
            return None

        run = self._create_thread_run(
            self.formatter.format(prompt_guide.steps[0], **args),
            prompt_guide.system_instruction,
        )
        thread_id = run["thread-id"]
        if prompt_guide.with_score:
            self.conversation_client.append_thread_message(
                thread_id,
                {"role": "user", "content": self.formatter.format(prompt_guide.steps[1], **args)},
            )
            msgs = self.conversation_client.list_thread_messages(thread_id)
            score_res = msgs["data"][0]["content"][0]
            content = filter_annotations(msgs["data"][2]["content"][0])
            try:
                score = self._parse_score(score_res["text"]["value"])
            except Exception:
                score = self._retry(thread_id, score_res)
            return score, content
        for step in prompt_guide.steps[1:]:
            self.conversation_client.append_thread_message(
                thread_id,
                {"role": "user", "content": self.formatter.format(step, **args)},
            )
        msgs = self.conversation_client.list_thread_messages(thread_id)
        content = filter_annotations(msgs["data"][0]["content"][0])
        if prompt_guide.output_as:
            args[prompt_guide.output_as] = content
        return content

    def _invoke_prompt_generator(self, prompt_guide: PromptGuide, args: dict):
        # prompt_generator is guaranteed to be non-None by the caller
        assert prompt_guide.prompt_generator is not None
        thread_id = None
        for prompt in prompt_guide.prompt_generator(args):
            if thread_id is None:
                run = self._create_thread_run(prompt, prompt_guide.system_instruction)
                thread_id = run["thread-id"]
            else:
                self.conversation_client.append_thread_message(thread_id, {"role": "user", "content": prompt})
        if thread_id is None:
            raise ValueError("No prompts were generated")
        msgs = self.conversation_client.list_thread_messages(thread_id)
        content = filter_annotations(msgs["data"][0]["content"][0])
        if prompt_guide.output_as:
            args[prompt_guide.output_as] = content
        return content

    def _invoke_prompt_guide(self, prompt_guide: PromptGuide, args: dict):
        if prompt_guide.steps:
            return self._invoke_prompt_guide_steps(prompt_guide, args)
        if prompt_guide.prompt_generator:
            return self._invoke_prompt_generator(prompt_guide, args)
        if prompt_guide.is_symptom:
            return self._invoke_symptoms(prompt_guide, args)
        return self._invoke_prompt_guide_prompts(prompt_guide, args)

    def invoke(self, template_name: str, args: dict) -> tuple[float, str]:
        template = root_cause_analysis_prompts[template_name]
        if isinstance(template, PromptGuide):
            res = self._invoke_prompt_guide(template, args)
            assert isinstance(res, tuple)
            return res
        if isinstance(template, list):
            res = None
            for prompt_guide in template:
                if isinstance(prompt_guide, PromptGuide):
                    res = self._invoke_prompt_guide(prompt_guide, args)
                else:
                    msg = f"Invalid prompt guide: {prompt_guide}"
                    raise TypeError(msg)
            assert isinstance(res, tuple)
            return res
        msg = f"Unknown template name: {template_name}"
        raise ValueError(msg)
