from string import Formatter

from llm.providers import BaseProvider

TRANSLATE_TEMPLATE = """
Translate the following text from English to {target_lang}. Only provide the translation, do not include any other text. if no content in the following text, just return an empty string:
{content}
"""


class Translator:
    def __init__(self, provider: BaseProvider):
        self.provider = provider
        self.formatter = Formatter()

    def invoke(self, target_lang: str, content: str) -> str:
        if content.strip():
            prompt = self.formatter.format(TRANSLATE_TEMPLATE, target_lang=target_lang, content=content)
            return self.provider.invoke(prompt, add_no_think_instruction=True)
        return content
