import random
import re
from dataclasses import dataclass, field


def try_parse_log_groups(res: str) -> list[dict]:
    groups_raw = re.findall(r"<group>(.*?)</group>", res, re.DOTALL)
    groups = []
    for group in groups_raw:
        title_match = re.search(r"<title>(.*?)</title>", group, re.DOTALL)
        title = title_match.group(1) if title_match else ""
        desc_match = re.search(r"<description>(.*?)</description>", group, re.DOTALL)
        description = desc_match.group(1) if desc_match else ""
        samples_match = re.search(r"<samples>(.*?)</samples>", group, re.DOTALL)
        if title and samples_match:
            samples_text = samples_match.group(1).strip()
            groups.append({"title": title, "description": description, "samples_text": samples_text})
    return groups


def filter_annotations(msg_content: dict) -> str:
    text_content = msg_content["text"]["value"]
    if "annotations" in msg_content["text"] and msg_content["text"]["annotations"]:
        annotations = msg_content["text"]["annotations"]
        for annotation in sorted(annotations, key=lambda x: x["start_index"], reverse=True):
            start = annotation["start_index"]
            end = annotation["end_index"]
            text_content = text_content[:start] + text_content[end:]
    return text_content


@dataclass
class NormalLLMLogGroup:
    samples: list[str] = field(default_factory=list)
    description: str = ""
    title: str = ""

    def to_prompt(self, sample_size: int):
        samples = self.samples if sample_size >= len(self.samples) else random.sample(self.samples, sample_size)
        samples_prompt = "\n".join(samples)
        return f"""
<group>
    <title>{self.title}</title>
    <description>{self.description}</description>
    <samples>
{samples_prompt}
    </samples>
</group>
"""


def parse_res_to_normal_log_groups(logs: list[str], res: str) -> list[NormalLLMLogGroup]:
    groups: list[NormalLLMLogGroup] = []
    try:
        parsed_groups = try_parse_log_groups(res)
        for group in parsed_groups:
            samples_text = group["samples_text"]
            sample_indexes = [int(x) for x in samples_text.split("\n") if x.strip()]
            samples = [logs[i] for i in sample_indexes if i < len(logs)]
            if samples:
                groups.append(
                    NormalLLMLogGroup(
                        samples=samples,
                        description=group["description"],
                        title=group["title"],
                    ),
                )
        return groups
    except Exception:
        return []


def cleanup_past_advisories(adv: str) -> str:
    """Remove Today and Today's and "No new significant issues detected today"
    ignore case
    :param adv:
    :return: cleaned advisory
    """
    keywords = ["no new significant issues detected today", "today's", "today"]
    for keyword in keywords:
        adv = re.sub(rf"{keyword}", "", adv, flags=re.IGNORECASE)
    replaced_words = ["no new significant issues"]
    for word in replaced_words:
        adv = re.sub(rf"{word}", "[IGNORED]", adv, flags=re.IGNORECASE)
    return adv


def remove_thinking(res: str) -> str:
    return re.sub(r"<think>.*?</think>\n*", "", res, flags=re.DOTALL)


def try_parse_int(i: str):
    try:
        return int(i)
    except ValueError:
        return None
