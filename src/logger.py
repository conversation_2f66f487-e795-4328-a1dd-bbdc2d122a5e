import logging.config
import os
import sys
import warnings
from typing import Literal

import orj<PERSON>
import structlog
from structlog.contextvars import merge_contextvars
from structlog.processors import <PERSON><PERSON><PERSON><PERSON><PERSON>, StackInfoRenderer, TimeStamper, format_exc_info

warnings.filterwarnings("ignore", category=UserWarning)
log_level = "INFO"  # FIXME use env var or config


def _is_dev():
    return os.environ.get("PYTHON_ENV", "") == "development" or os.environ.get("PYTHONDEVMODE", "") == "1"


def orjson_renderer(evt, **kwargs):
    return orjson.dumps(evt, **kwargs).decode("utf-8")


def config_structlog(target: Literal["stdout", "stderr"] = "stdout"):
    if _is_dev():
        return

    shared_processors = [
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        merge_contextvars,
        structlog.processors.CallsiteParameterAdder(
            {
                structlog.processors.CallsiteParameter.FILENAME,
                structlog.processors.CallsiteParameter.FUNC_NAME,
                structlog.processors.CallsiteParameter.LINENO,
            },
        ),
        StackInfoRenderer(),
        format_exc_info,
        TimeStamper(fmt="iso", utc=True),
        # JSONRenderer(serializer=orjson_renderer),
    ]
    structlog.configure(
        cache_logger_on_first_use=True,
        processors=[*shared_processors, structlog.stdlib.ProcessorFormatter.wrap_for_formatter],
        logger_factory=structlog.stdlib.LoggerFactory(),
    )
    formatter = structlog.stdlib.ProcessorFormatter(
        foreign_pre_chain=shared_processors,
        processors=[
            # Remove _record & _from_structlog.
            structlog.stdlib.ProcessorFormatter.remove_processors_meta,
            JSONRenderer(serializer=orjson_renderer),
        ],
    )
    if target == "stdout":
        target_stream = sys.stdout
    elif target == "stderr":
        target_stream = sys.stderr
    else:
        raise ValueError(f"Invalid target: {target}. Must be 'stdout' or 'stderr'.")
    handler = logging.StreamHandler(target_stream)
    handler.setFormatter(formatter)
    root_logger = logging.getLogger()
    for h in root_logger.handlers[:]:
        root_logger.removeHandler(h)
    root_logger.setLevel(log_level)
    for module_name in [
        "sagemaker.config",
        "sagemaker",
        "prophet.models",
        "prophet",
        "httpx",
        "httpcore",
        "smart_open",
    ]:
        logging.getLogger(module_name).handlers.clear()
        logging.getLogger(module_name).propagate = False
    for module_name in [
        "httpx",
        "sagemaker",
        "pythia",
        "anomaly_detector",
        "prophecy",
        "custom_exceptions",
        "pb",
        "prom_writer",
    ]:
        logging.getLogger(module_name).handlers.clear()
        logging.getLogger(module_name).propagate = False

    for _log in ["uvicorn", "uvicorn.error"]:
        # Make sure the logs are handled by the root logger
        logging.getLogger(_log).handlers.clear()
        logging.getLogger(_log).propagate = True

    # Uvicorn logs are re-emitted with more context. We effectively silence them here
    logging.getLogger("uvicorn.access").handlers.clear()
    logging.getLogger("uvicorn.access").propagate = False

    root_logger.addHandler(handler)
