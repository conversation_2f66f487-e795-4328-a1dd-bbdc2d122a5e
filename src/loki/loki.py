from datetime import datetime

import httpx
import orjson

import constants
import utils
from custom_exceptions import ServiceError

http_client = httpx.Client()


def merge_loki_streams(response):
    if len(response["result"]) and "resultType" in response and response["resultType"] == "streams":
        values = []
        for stream in response["result"]:
            values.extend(stream["values"])
        values = sorted(values, key=lambda x: x[0], reverse=True)
        response["result"] = [{"values": values, "stream": response["result"][0]["stream"]}]
        return response
    return response


def query_logs_from_loki(
    loki_url: str,
    tenant_id: str,
    start: datetime,
    end: datetime,
    query: str,
    limit: int = 1000,
    merge_streams=True,
):
    params = {"start": utils.iso_format(start), "end": utils.iso_format(end), "limit": limit, "query": query}
    res = http_client.get(
        f"{loki_url}/loki/api/v1/query_range",
        params=params,
        headers={"X-Scope-OrgID": tenant_id},
        timeout=20,
    )
    if res.status_code != httpx.codes.OK:
        raise ServiceError(res.text)
    response = orjson.loads(res.read()).get("data", {})
    if merge_streams:
        return merge_loki_streams(response)
    return response


def retrieve_original_logs_from_loki(
    loki_url: str,
    tenant_id: str,
    flow_id: str,
    start: datetime,
    end: datetime,
    log_seq_ids: list[str],
    limit: int = 1000,
):
    regex = "|".join(
        (f'{constants.ORIGINAL_LOG_SEQ_ID_KEY}\\":\\"{seq_id}\\"' for seq_id in log_seq_ids),
    )
    q = f'{{_reserved_flow_id="{flow_id}"}} |~ "{regex}"'
    response = query_logs_from_loki(
        loki_url=loki_url,
        tenant_id=tenant_id,
        start=start,
        end=end,
        query=q,
        limit=limit,
        merge_streams=True,
    )
    return response.get("result", [])
