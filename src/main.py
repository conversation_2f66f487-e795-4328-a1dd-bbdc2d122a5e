import os
from collections.abc import AsyncIterator
from contextlib import asynccontextmanager
from dataclasses import dataclass

import anyio
import asyncpg
import uvicorn
from asgi_correlation_id import CorrelationIdMiddleware
from fastapi import FastAPI

from anomaly_detector.job.job import anomaly_detector_job_factory
from anomaly_detector.router import router as anomaly_detector_router
from config import get_settings
from database import get_db_connection_pool
from job.model import JobType
from job.scheduler import JobScheduler, JobStore
from job.scheduler.executor import ProcessPoolJobExecutor, ThreadPoolJobExecutor
from logger import config_structlog
from middlewares import StructLogMiddleware
from prophecy import prophecy_job_factory, prophecy_router
from s3 import register_aws_s3, register_hosted_s3
from services import ServicesRegistry
from sks.mcs_nss_job import sks_mcs_nss_job_factory
from sks.router import router as sks_router

log_config = config_structlog()
use_thread_executor = os.getenv("USE_THREAD_EXECUTOR") == "1"


@dataclass
class AppState:
    db_pool: asyncpg.Pool


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncIterator[dict]:
    db_pool = await get_db_connection_pool()
    settings = get_settings()
    service_registry = ServicesRegistry(settings.consul, dict(settings.services))
    s3_service = service_registry.service("s3")
    register_hosted_s3(settings, s3_service.url())
    register_aws_s3(settings)
    job_store = JobStore(
        settings=settings,
        services_registry=service_registry,
        id=settings.nomad_alloc_id,
        conn_pool=db_pool,
    )
    job_store.add_job_type(JobType.prophecy, prophecy_job_factory)
    job_store.add_job_type(JobType.log_anomaly, anomaly_detector_job_factory)
    job_store.add_job_type(JobType.sks_mcs_nss, sks_mcs_nss_job_factory)

    scheduler = JobScheduler(
        job_store,
        ThreadPoolJobExecutor() if use_thread_executor else ProcessPoolJobExecutor(),
    )
    async with anyio.create_task_group() as tg:
        if not settings.disable_scheduler:
            tg.start_soon(scheduler.start, tg)
        yield {"db_pool": db_pool, "service_registry": service_registry}
        await scheduler.stop()
        await db_pool.close()


app = FastAPI(lifespan=lifespan)


@app.get("/health", status_code=204)
async def health():
    """Health check endpoint that returns 204 No Content"""
    return


app.include_router(anomaly_detector_router)
if get_settings().is_sks_env:
    app.include_router(sks_router)
app.include_router(prophecy_router)
app.add_middleware(StructLogMiddleware)
app.add_middleware(CorrelationIdMiddleware)


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=3000, proxy_headers=True, log_config=None)
