
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_bw_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_unsolicited_probe_response_failed" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "correlation_markers" (
  "correlation_group" STRING NULL,
  "timestamp" TIMESTAMP(3) NOT NULL,
  "score" DOUBLE NULL,
  TIME INDEX ("timestamp")
)

ENGINE=mito
;
CREATE TABLE IF NOT EXISTS "ap_radio_vap_total_number_of_offchan_tx_mgmt_frames" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_crc_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_resource_utilization_0_255" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "rx_data_packets_per_ac_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_offer_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_status_mem_avg_usage" (
  "ap_base_mac" STRING NULL,
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_base_mac", "ap_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_total_beacons_sent_to_fw_in_swba_intr" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "rx_mcs_stats_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_unicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_failures_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_mcs_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_nss_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_mgmt_failures_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "tx_mcs_stats_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_mgmt_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_offer_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_unsolicited_probe_response_success" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_total_per" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_multi_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_mgmt_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_per_over_configured_period" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_nss_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_nss_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_data_payload_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_data_payload_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_mcs_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_eapol_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);

CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_data_payload_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_status_cpu_avg_usage" (
  "ap_base_mac" STRING NULL,
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_base_mac", "ap_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_nss_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_w" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_offer_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_voice" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_phy_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_throughput_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_average_tx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_unicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_dropped_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_total_number_of_offchan_tx_data_frames" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_wildcard_probe_requests_drops" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_mic_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "rx_data_packets_per_ac_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_broadcast_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_bw_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_mcs_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_mic_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_multicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_video" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "rx_data_packets_per_ac_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_average_rx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_dropped_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_current_profileperiodicity" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_beacon_frames_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_tx_failures_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_failure_duration_ns_total" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "reason" STRING NULL,
  "sta_mac" STRING NULL,
  "state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "reason", "sta_mac", "state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_fils_frames_sent" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_obss_chan_util_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_state_duration_ns_sum" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "last_state" STRING NULL,
  "sta_mac" STRING NULL,
  "target_state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "last_state", "sta_mac", "target_state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_failure_duration_ns_sum" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "reason" STRING NULL,
  "sta_mac" STRING NULL,
  "state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "reason", "sta_mac", "state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_decryption_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_ctl_frames" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_fils_frames_sent_fail" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_phy_errors_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_6ghz_soc_status" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_per_over_configured_period" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ack_rssi_chain_4" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_chan_nf_bdf_averaged_nf_dbm" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_success_duration_ns_sum" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "reason" STRING NULL,
  "sta_mac" STRING NULL,
  "state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "reason", "sta_mac", "state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_current_profile_pperiodicity" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_rssi_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_rssi_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_ctl_frames" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_connections_refuse_vap_limit" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_channel_utilization_0_255" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_average_rx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_average_tx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_chan_tx_pwr" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_20tu_probe_response_interval" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_avg_nss_symmetry_stddev" (
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_current_proffile_periodicity" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_last_tx_rate_for_unicast_packets_mcs" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_throughput_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_phy_errors_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_tx_frame_cnt" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_phy_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_802_11_auth_success_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_avg_mcs_symmetry_yhat" (
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_background" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_tx_data_bytes_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_cycle_cnt_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_crc_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_state_duration" (
  "ap_mac" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "last_state" STRING NULL,
  "sta_mac" STRING NULL,
  "target_state" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "bssid", "last_state", "sta_mac", "target_state")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_success_duration_ns" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "reason" STRING NULL,
  "sta_mac" STRING NULL,
  "state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "reason", "sta_mac", "state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_mgmt_frames_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_tx_frame_cnt_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_unicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_failure_duration_ns" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "reason" STRING NULL,
  "sta_mac" STRING NULL,
  "state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "reason", "sta_mac", "state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_state_duration_ns_total" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "last_state" STRING NULL,
  "sta_mac" STRING NULL,
  "target_state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "last_state", "sta_mac", "target_state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_802_11_auth_attempts_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_signal_power" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "sta_mac" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "sta_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_802_11_auth_attempts_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_last_tx_rate_for_multicast_packets" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_ctl_frames_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_data_packets_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_mic_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_beacon_failed_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_last_tx_rate_for_unicast_packets" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_created_vap" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_data_bytes_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_total_per" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_self_bss_chan_util" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_20tu_probe_response_status" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_rx_clear_cnt" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ack_rssi_chain_2" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_obss_chan_util" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_last_tx_rate_for_multicast_packets_mcs" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_ie_overflow_count_for_tx_non_tx_vap_beacon_resource_overflow" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_connections_refuse_radio_limit" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_cycle_cnt" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_tx_failures_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_state_duration_ns" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "last_state" STRING NULL,
  "sta_mac" STRING NULL,
  "target_state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "last_state", "sta_mac", "target_state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_mlme_authorize_attempts_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);

CREATE TABLE IF NOT EXISTS "ap_radio_tx_vap_id" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_host_discard_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_data_payload_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_tx_data_packets_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_current_profile_periodicity" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_multicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_current_profle_periodicity" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_decryption_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_connections_refuse_radio_limit" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_multi_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "tx_mcs_stats_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ack_rssi_chain_1" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_background" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "rx_mcs_stats_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_mlme_authorize_attempts_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_number_of_active_vaps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_sta_to_ap_rx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "sta_mac" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "sta_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "correlation_metrics" (
  "coverage_interference_score" DOUBLE NULL,
  "coverage_rssi_score" DOUBLE NULL,
  "coverage_link_score" DOUBLE NULL,
  "coverage_coverage_qoe" DOUBLE NULL,
  "throughput_efficiency" DOUBLE NULL,
  "throughput_success_rate" DOUBLE NULL,
  "throughput_qoe" DOUBLE NULL,
  "ts" TIMESTAMP(9) NOT NULL,
  "connection_successful_connect_rate" DOUBLE NULL,
  "connection_successful_state_change_rate" DOUBLE NULL,
  "connection_successful_duration_score" DOUBLE NULL,
  "connection_qoe" DOUBLE NULL,
  TIME INDEX ("ts")
)

ENGINE=mito
WITH(
  merge_mode = 'last_non_null'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_phy_err_cnt" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rnr_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_active_vap" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_low" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_mcs_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_rx_clear_cnt_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_connections_refuse_vap_limit" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_video" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_beacon_success_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_rx_frame_cnt_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_unicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_tx_data_packets_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_voice" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "skslog" (
  "message" STRING NULL,
  "timestamp" TIMESTAMP(9) NOT NULL,
  TIME INDEX ("timestamp")
)

ENGINE=mito
WITH(
  append_mode = 'true'
);
CREATE TABLE IF NOT EXISTS "rx_mcs_stats_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_dropped_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_tx_bw_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_wlan_rx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_multi_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "tx_mcs_stats_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "node" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "node", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_nss_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_unicast_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mpdu_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_multicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_trigger" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_bw_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mcs_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_unsolicited_probe_response_failed" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_session" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_bw_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_fils_frames_sent_fail" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_last_packet_error_rate_per" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_mcs_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_msdu_tx_failed_retry_count_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_packets_queued_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_non_tx_profile_rollback_count_when_ema_ext_enabled" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_mcs_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_host_discard_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_multicast_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ession" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_unicast_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_unicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_percentage_of_tx_pkt_in_twt_session" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_unsolicited_probe_response_success" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_bw_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_ppdu_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_in_twt_session" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_decryption_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_retries_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ion" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mcs_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_broadcast_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_average_rx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_on" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_broadcast_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_session" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_multicast_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_probe_request_drops_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_n" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_packets_dropped_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_dialog_id" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_percentage_of_rx_pkt_in_twt_session" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_bytes_for_last_one_second" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_packets_errored_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_multicast_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_mcs_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_broadcast_twt_enable" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_wake_duration_us" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mic_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_mgmt_frames_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_last_rx_rate" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_avg_ppdu_rx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_rssi" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_fils_frames_sent" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_obss_chan_util_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_dropped_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_bw_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_tx_data_bytes_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_errors_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_tx_failures_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_20tu_probe_response_interval" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_bw_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_data_bytes_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_phy_err_cnt_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_broadcast_twt" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_connection_success_duration_ns_total" (
  "ap_mac" STRING NULL,
  "auth_mode" STRING NULL,
  "bssid" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "reason" STRING NULL,
  "sta_mac" STRING NULL,
  "state" STRING NULL,
  "webauth" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "auth_mode", "bssid", "reason", "sta_mac", "state", "webauth")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ack_rssi_chain_3" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_peer_delete_req_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_best_effort" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_maxium_rate_per_client_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_mlme_authorize_success_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_multi_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_non_tx_profile_rollback_count_when_ema_ext_enabled" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithiium_cycle_cnt_chan_tx_pwr" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_last_mgmt_rx_rate" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_last_packet_error_rate_per" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_nss_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_802_11_auth_success_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_wake_interval_us" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_mgmt_frames_total_yhat" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_bw_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_avg_nss_symmetry_yhat" (
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_number_of_failed_offchan_tx_frames" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_mlme_authorize_success_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_lithium_cycle_cnt_rx_frame_cnt" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_peer_delete_all_req_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_avg_mcs_symmetry_stddev" (
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  TIME INDEX ("greptime_timestamp")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_mgmt_frames_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_sp_offset_us" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_best_effort" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_msdu_tx_retry_count_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_failures_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_fils_enable" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_rx_mgmt_frames_dropped_rssi_too_low" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "_reserved_prophecy_ap_radio_rx_data_packets_total_stddev" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_node_ap_to_sta_tx_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "sta_mac" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "sta_mac")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_tx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_20tu_probe_response_status" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_oob_probe_requests" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_bw_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_best_effort" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_rssi" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_nss_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_average_tx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_last_tx_rate" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_nss_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_flow_id" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_peer_delete_all_resp_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_peer_delete_resp_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mgmtrssi" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_wt_session" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_voice" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_band_width" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_t_flow_id" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_fils_enable" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_failed_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_ow" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_unicast_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_packets_for_last_one_second" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_bw_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_video" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_msdu_tx_multiple_retry_count_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_success_unicast_data_bytes" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_unicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_d" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_retry_count" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ssion" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mgmt_rssi" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_announcement" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_twt_event_type" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_nss_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mgmt_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_nss_bucket" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "le" STRING NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "le", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_mcs_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_sion" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_multicast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_bytes_for_last_one_second" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_avg_ppdu_tx_rate_kbps" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_multicast_data_bytes_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_rx_broadcast_data_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_background" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_ie_overflow_count_for_tx_non_tx_vap_beacon_resource_overflow" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_packets_for_last_one_second" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_mgmt_packets_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_tx_dropped_valid_for_offload_driver_total" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
CREATE TABLE IF NOT EXISTS "ap_radio_vap_node_rx_bw_sum" (
  "ap_mac" STRING NULL,
  "greptime_timestamp" TIMESTAMP(3) NOT NULL,
  "greptime_value" DOUBLE NULL,
  "radio" STRING NULL,
  "site_id" STRING NULL,
  "sta_mac" STRING NULL,
  "vap" STRING NULL,
  TIME INDEX ("greptime_timestamp"),
  PRIMARY KEY ("ap_mac", "radio", "site_id", "sta_mac", "vap")
)

ENGINE=metric
WITH(
  on_physical_table = 'greptime_physical_table'
);
