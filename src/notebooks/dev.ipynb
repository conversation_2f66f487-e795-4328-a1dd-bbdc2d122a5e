{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["from collections import defaultdict\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "f9a64b6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["x = {}\n", "print(x.get(\"test\"))"]}, {"cell_type": "code", "execution_count": 16, "id": "49579d10", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-21T12:50:40.838+08:00\n"]}], "source": ["import pandas as pd\n", "import pendulum\n", "import datetime\n", "import pytz\n", "\n", "pdt = pendulum.parse(\"2023-10-01T12:34:56.789+08:00\")\n", "dt = datetime.datetime.now(tz=pytz.timezone(\"Asia/Shanghai\"))\n", "print(dt.isoformat(timespec=\"milliseconds\").replace(\"+00:00\", \"Z\"))\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "38fb9f49", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/dev/core0/new-pythia/pythia/src\n", "/Users/<USER>/dev/core0/new-pythia/pythia/src\n"]}], "source": ["import pathlib\n", "import os\n", "\n", "print(pathlib.Path.cwd())\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": 6, "id": "eb8512e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DELETE FROM \"jobs\" WHERE \"type\"='log-anomaly' AND \"tenant_id\"='test' AND \"job_source_id\"='test2' AND \"config\"->'inference-interval-minutes' IS NOT NULL\n", "UPDATE \"jobs\" SET \"\"config\"->'inference-interval-minutes'\"=null WHERE \"type\"='log-anomaly' AND \"tenant_id\"='test' AND \"job_source_id\"='test2' AND \"config\"->'inference-interval-minutes' IS NOT NULL\n"]}], "source": ["from pypika import Criterion, PostgreSQLQuery as Q, Table\n", "\n", "jobs = Table(\"jobs\")\n", "base_conditions = [\n", "    jobs.type == \"log-anomaly\",\n", "    jobs.tenant_id == \"test\",\n", "    jobs.job_source_id == \"test2\",\n", "    jobs.config.get_json_value(\"inference-interval-minutes\").isnotnull(),\n", "]\n", "print(str(Q.from_(jobs).delete().where(\n", "    Criterion.all(base_conditions)\n", ")))\n", "print(str(\n", "    Q.update(jobs).where(Criterion.all(base_conditions)).set(jobs.config.get_json_value(\"inference-interval-minutes\"),\n", "                                                             None)\n", "))"]}, {"cell_type": "code", "execution_count": null, "id": "db02cfc2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1  col2\n", "0     1     2\n", "1     3     4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "r1 = [{\"col1\": 1, \"col2\": 2}, {\"col1\": 3, \"col2\": 4}]\n", "r2 = {\"col1\": [1, 3], \"col2\": [2, 4]}\n", "df = pd.DataFrame.from_dict(r2)\n", "df"]}, {"cell_type": "code", "execution_count": 7, "id": "0c4729b4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>new_name</th>\n", "    </tr>\n", "    <tr>\n", "      <th>label</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>0.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>0.422222</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       new_name\n", "label          \n", "a      0.375000\n", "b      0.422222"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame({\n", "    \"a\": [1, 2, 3, 9],\n", "    \"b\": [4, 5, 6, 4],\n", "    \"label\": [\"a\", \"b\", \"a\", \"b\"],\n", "})\n", "\n", "df[\"min/max\"] = df[[\"a\", \"b\"]].min(axis=1) / df[[\"a\", \"b\"]].max(axis=1)\n", "df.groupby(\"label\").agg(new_name=(\"min/max\", \"mean\"))\n"]}, {"cell_type": "code", "execution_count": null, "id": "1a701fe0c8cc6278", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "print(os.getcwd())\n", "\n", "load_dotenv(os.path.join(\"../../\", \".env\"))\n", "\n", "import pendulum"]}, {"cell_type": "code", "execution_count": null, "id": "1724d6193854de82", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from prom import PrometheusClient\n", "\n", "prometheus_client = PrometheusClient(\n", "    mimir_proxy_url=\"http://127.0.0.1:24000/v1/prometheus/api/v1\",\n", "    uri=\"/query_range\",\n", "    headers={\"x-greptime-db-name\": \"sks\"},\n", ")\n", "\n", "prophecy_prometheus_client = PrometheusClient(\n", "    mimir_proxy_url=\"http://127.0.0.1:24000/v1/prometheus/api/v1\",\n", "    uri=\"/query_range\",\n", "    headers={\"x-greptime-db-name\": \"pythia\"},\n", ")\n", "\n", "now = pendulum.parse(\"2025-04-10T16:00:00Z\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "73184a413bb9451c", "metadata": {}, "outputs": [], "source": ["from greptime import SyncGreptimeClient, greptime_timestamp_sql\n", "from anomaly_detector.sks.qoe import sks_markers\n", "\n", "end = pendulum.parse(\"2025-04-12T16:00:00Z\")\n", "start = end.add(hours=-24)\n", "sql_marker = sks_markers[0]\n", "greptime_client = SyncGreptimeClient(\n", "    url=\"http://127.0.0.1:24000\",\n", "    db=\"sks\"\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "3aef63f57279aaf6", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame.from_dict(response).rename(columns={sql_marker.timestamp_field: \"timestamp\"})\n", "df[\"timestamp\"] = pd.to_datetime(df[\"timestamp\"], unit=\"ms\", utc=True).dt.tz_convert(\"Asia/Shanghai\")"]}, {"cell_type": "code", "execution_count": null, "id": "fd8c52fcc9cc3244", "metadata": {}, "outputs": [], "source": ["df[\"timestamp2\"] = pd.to_datetime(df[\"timestamp\"], unit=\"ms\", utc=True).dt.tz_convert(\"Asia/Shanghai\")"]}, {"cell_type": "code", "execution_count": null, "id": "95148c8fb677780b", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.sks.qoe import default_omit_labels\n", "from anomaly_detector.marker import PromMarker\n", "from anomaly_detector.sks.marker import MarkerType, Tag, CustomMarker\n", "from datetime import timedelta\n", "\n", "import pandas as pd\n", "from polars import Datetime\n", "\n", "from prom import PrometheusClient\n", "from anomaly_detector.job.metric.metric import MetricTask\n", "from anomaly_detector.schema import MetricsQuery\n", "from greptime import AsyncGreptimeClient\n", "\n", "metric_tasks = [MetricTask(\n", "    prometheus_client=prometheus_client,\n", "    prophecy_prometheus_client=prophecy_prometheus_client,\n", "    greptime_client=greptime_client,\n", "    prophecy_id=m.name,\n", "    marker=m,\n", "    start=start,\n", "    end=end,\n", ") for m in sks_markers]"]}, {"cell_type": "code", "execution_count": null, "id": "49c0b912c42e7409", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation_marker import MetricMarkerCorrelation\n", "\n", "mmc = MetricMarkerCorrelation(metric_tasks=metric_tasks)"]}, {"cell_type": "code", "execution_count": null, "id": "c0dc1358f0963cd3", "metadata": {}, "outputs": [], "source": ["mmc = MetricMarkerCorrelation(metric_tasks=[])\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ae034bfe26c09ab", "metadata": {}, "outputs": [], "source": ["mmc.retrieve_graph()"]}, {"cell_type": "code", "execution_count": null, "id": "63d0e0ba91b3b7d3", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation_marker import ReportMarkersCorrelation\n", "from pathlib import Path\n", "import orjson\n", "\n", "report = orjson.loads(Path(\"./inference_2025-04-10_report.json\").read_bytes())\n", "\n", "pythia_greptime_client = SyncGreptimeClient(\n", "    url=\"http://127.0.0.1:24000\",\n", "    db=\"pythia\"\n", ")\n", "rmc = ReportMarkersCorrelation(anomaly_detector_id=\"sks\", report=report, greptime_client=pythia_greptime_client,\n", "                               start=start, end=end, )"]}, {"cell_type": "code", "execution_count": null, "id": "9ee744f6d01d9a34", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation import correlate, timestamps_detection\n", "\n", "r, merged_df = correlate(rmc, mmc)"]}, {"cell_type": "code", "execution_count": null, "id": "4210986d214c0097", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation import correlate, timestamps_detection\n", "\n", "edges = timestamps_detection(merged_df)"]}, {"cell_type": "code", "execution_count": null, "id": "f58cd0b8c8e82c4c", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation import rising_edge_to_correlation_rows\n", "\n", "rising_edge_to_correlation_rows(edges[1])"]}, {"cell_type": "code", "execution_count": null, "id": "a9c497c69d8beb86", "metadata": {}, "outputs": [], "source": ["edges[0].correlation.loc[edges[0].correlation.columns]"]}, {"cell_type": "code", "execution_count": null, "id": "834fff7941e87705", "metadata": {}, "outputs": [], "source": ["edges[0].df.index.duplicated()"]}, {"cell_type": "code", "execution_count": null, "id": "b797e1a67dcdc1de", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation import rising_edge_to_correlation_rows\n", "\n", "rising_edge_to_correlation_rows(edges[0])"]}, {"cell_type": "code", "execution_count": null, "id": "7cf6ff4a2cc7fc42", "metadata": {}, "outputs": [], "source": ["edges[0].columns"]}, {"cell_type": "code", "execution_count": null, "id": "92feb8fd061d6e9e", "metadata": {}, "outputs": [], "source": ["edges[0].correlation"]}, {"cell_type": "code", "execution_count": null, "id": "5332f2480375121e", "metadata": {}, "outputs": [], "source": ["(\"log_structure\", None) in edges[0].correlation[('log_structure', None)].index"]}, {"cell_type": "code", "execution_count": null, "id": "75f9d14ef70c3ba9", "metadata": {}, "outputs": [], "source": ["float(edges[0].correlation[('log_structure', None)].loc[[('log_structure', None)]].iloc[0])"]}, {"cell_type": "code", "execution_count": null, "id": "c91fd9220960e01b", "metadata": {}, "outputs": [], "source": ["merged = pd.concat([report_df, metrics_df], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "219e530b7a815d5c", "metadata": {}, "outputs": [], "source": ["merged"]}, {"cell_type": "code", "execution_count": null, "id": "6861905c647eb630", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.marker import MarkerType, PromMarker, Tag, CustomMarker\n", "\n", "qoe_mmc = MetricMarkerCorrelation(\n", "    metric_tasks=[m for m in metric_tasks if isinstance(m.marker, CustomMarker) and m.marker.qoe_score])"]}, {"cell_type": "code", "execution_count": null, "id": "4dd60c0a5ef24fba", "metadata": {}, "outputs": [], "source": ["qoe_df = qoe_mmc.retrieve_graph()"]}, {"cell_type": "code", "execution_count": null, "id": "ea419d0407e9a3cc", "metadata": {}, "outputs": [], "source": ["qoe_df"]}, {"cell_type": "code", "execution_count": null, "id": "af647e96546489ad", "metadata": {}, "outputs": [], "source": ["sampled_qoe_df = qoe_df.resample('10min').mean()"]}, {"cell_type": "code", "execution_count": null, "id": "641d7bab99e1a97a", "metadata": {}, "outputs": [], "source": ["sampled_qoe_df = sampled_qoe_df.fillna(1)"]}, {"cell_type": "code", "execution_count": null, "id": "aa5cff914c598663", "metadata": {}, "outputs": [], "source": ["merged = pd.concat([report_df, g], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "b8aa11526015bf7c", "metadata": {}, "outputs": [], "source": ["a = frozenset([\"test\", (\"xx\", None), (\"yyy\", \"zzz\")])"]}, {"cell_type": "code", "execution_count": null, "id": "4565a9f42c684e76", "metadata": {}, "outputs": [], "source": ["another = frozenset([(\"xx\", None), (\"yyy\", \"zzz\"), \"test\"])\n"]}, {"cell_type": "code", "execution_count": null, "id": "be794cdf65c98342", "metadata": {}, "outputs": [], "source": ["y = [a]\n", "another in y"]}, {"cell_type": "code", "execution_count": null, "id": "94910d5eec2604d2", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.job.correlation.correlation import timestamps_detection\n", "\n", "edges = timestamps_detection(merged)"]}, {"cell_type": "code", "execution_count": null, "id": "7dbac44909e21ad0", "metadata": {}, "outputs": [], "source": ["merged"]}, {"cell_type": "code", "execution_count": null, "id": "f8242ed2f321ef6f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b59bef649111cb7c", "metadata": {}, "outputs": [], "source": ["re = edges[8]\n", "re.df"]}, {"cell_type": "code", "execution_count": null, "id": "6e14811205184e8c", "metadata": {}, "outputs": [], "source": ["corr_matrix = g.corr(method=\"spearman\")"]}, {"cell_type": "code", "execution_count": null, "id": "2ef031569953e1c4", "metadata": {}, "outputs": [], "source": ["import pendulum\n", "\n", "d = pendulum.now()\n", "ts = int(d.timestamp() * 1e9)\n", "print(ts)"]}, {"cell_type": "code", "execution_count": null, "id": "4678e706a6278e9", "metadata": {}, "outputs": [], "source": ["for col in corr_matrix.columns:\n", "    related_columns = corr_matrix[col][abs(corr_matrix[col]) > 0.5].index.to_list()\n", "    if len(related_columns) <= 1:\n", "        continue\n", "    print(col, related_columns)"]}, {"cell_type": "code", "execution_count": null, "id": "9c80bcfa7f45694f", "metadata": {}, "outputs": [], "source": ["records = [\n", "    {'ap_mac': None, 'correlation_score': 0.5576548330166359, 'marker_1': 'qoe:connection_successful_connect_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744487400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5576548330166359, 'marker_1': 'qoe:connection_successful_connect_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744505400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5576548330166359, 'marker_1': 'qoe:connection_successful_connect_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744521600000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5576548330166359, 'marker_1': 'qoe:connection_successful_connect_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744522200000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5576548330166359, 'marker_1': 'qoe:connection_successful_connect_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744553400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5617594697392193, 'marker_1': 'qoe:connection_successful_state_change_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744487400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5617594697392193, 'marker_1': 'qoe:connection_successful_state_change_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744505400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5617594697392193, 'marker_1': 'qoe:connection_successful_state_change_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744521600000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5617594697392193, 'marker_1': 'qoe:connection_successful_state_change_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744522200000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.5617594697392193, 'marker_1': 'qoe:connection_successful_state_change_rate',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744553400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.7707098333371367, 'marker_1': 'qoe:connection_successful_duration_score',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744487400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.7707098333371367, 'marker_1': 'qoe:connection_successful_duration_score',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744505400000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.7707098333371367, 'marker_1': 'qoe:connection_successful_duration_score',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744521600000000000},\n", "    {'ap_mac': None, 'correlation_score': 0.7707098333371367, 'marker_1': 'qoe:connection_successful_duration_score',\n", "     'marker_2': 'qoe:connection_qoe', 'radio': None, 'ts': 1744553400000000000}]"]}, {"cell_type": "code", "execution_count": null, "id": "78884f525bb3f75a", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T07:08:19.026095Z", "start_time": "2025-04-14T07:08:19.020821Z"}}, "outputs": [], "source": ["from collections import defaultdict\n", "import pendulum\n", "\n", "anomalies = defaultdict(list)\n", "target_marker = \"qoe:connection_qoe\"\n", "for record in records:\n", "    ts = record[\"ts\"]\n", "    marker = record[\"marker_1\"] if record[\"marker_1\"] != target_marker else record[\"marker_2\"]\n", "    split = marker.split(':')\n", "    if len(split) == 2:\n", "        marker_type, name = split\n", "    else:\n", "        marker_type = marker\n", "        name = None\n", "    corr = {\"marker_type\": marker_type, \"name\": name, \"correlation_score\": record[\"correlation_score\"]}\n", "    anomalies[ts].append(corr)\n", "results = []\n", "for ts, items in anomalies.items():\n", "    results.append({\"timestamp\": pendulum.from_timestamp(ts / 1e9), \"marker_type\": \"qoe\", \"name\": \"connection_qoe\",\n", "                    \"correlations\": items})\n", "sorted(results, key=lambda x: x['timestamp'])"]}, {"cell_type": "code", "execution_count": null, "id": "8539119e67b19f50", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eecaf1c6344a1dae", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T07:09:08.381571Z", "start_time": "2025-04-14T07:09:08.377891Z"}}, "outputs": [], "source": "tuple([1, 2])"}, {"cell_type": "code", "execution_count": 8, "id": "93d539cb35a8cede", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T07:12:25.054109Z", "start_time": "2025-04-15T07:12:24.515600Z"}}, "outputs": [], "source": ["from greptime import SyncGreptimeClient\n", "\n", "greptime_client = SyncGreptimeClient(url=\"http://127.0.0.1:24000\", db=\"sks\")\n", "tables = greptime_client.execute(\"show tables\")"]}, {"cell_type": "code", "execution_count": null, "id": "c88bc995107846d0", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T13:12:24.795293Z", "start_time": "2025-04-15T13:12:24.792168Z"}}, "outputs": [], "source": ["import re\n", "\n", "x = re.sub(r\"\\$__extraFilter\", \"test\", \"target $__extraFilt\")\n", "x"]}, {"cell_type": "code", "execution_count": null, "id": "3d7da03c63158256", "metadata": {"ExecuteTime": {"end_time": "2025-05-06T04:14:43.270310Z", "start_time": "2025-05-06T04:14:42.793492Z"}}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame({\"a\": [1, 2, 3], \"b\": [4, 5, 6.0]})\n", "print(df.to_csv(index=False, float_format=\"%.2f\"))"]}, {"cell_type": "code", "execution_count": null, "id": "3ff4303e5aaa0795", "metadata": {"ExecuteTime": {"end_time": "2025-05-15T14:08:18.267180Z", "start_time": "2025-05-15T14:08:18.238354Z"}}, "outputs": [], "source": ["import pendulum\n", "\n", "pendulum.parse(1747273800000)"]}], "metadata": {"kernelspec": {"display_name": "pythia-BPx0uQVf-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}