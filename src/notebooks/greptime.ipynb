{"cells": [{"cell_type": "code", "execution_count": null, "id": "3cc242f4f29e4b96", "metadata": {}, "outputs": [], "source": ["from tinycss2.nth import parse_end\n", "\n", "from greptime import SyncGreptimeClient\n", "new_greptime_client = SyncGreptimeClient(url=\"http://127.0.0.1:24000\", db=\"sks\")\n", "tables = new_greptime_client.execute(\"show tables\")"]}, {"cell_type": "code", "execution_count": null, "id": "2e4fe79dab044e9", "metadata": {}, "outputs": [], "source": ["for table in tables:\n", "    table_name = table['Tables']\n", "    if not table_name.startswith(\"_reserved\"):\n", "        continue\n", "    # migrate to t_sks_reserved\n", "    print(f\"Table: {table_name} restored\")"]}, {"cell_type": "code", "execution_count": null, "id": "318f6b4187c8fb53", "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "missing_tables = defaultdict(list)\n", "for table in tables:\n", "    table_name = table[\"Tables\"]\n", "    site_id = ['11265', '351', '332']\n", "    for site in site_id:\n", "        try:\n", "            latest_site_value = new_greptime_client.execute(f'select * from \"{table_name}\" where site_id=\\'{site}\\' order by greptime_timestamp desc limit 1 ')\n", "            if not latest_site_value:\n", "                print(\"no site value\", table_name)\n", "                missing_tables[site].append(table_name)\n", "            else:\n", "                print(f\"site: {site}, table: {table_name}, latest value\", latest_site_value[0])\n", "        except Exception as e:\n", "            print(f\"Error: {e}\", table_name)\n", "            continue"]}, {"cell_type": "markdown", "id": "9d64c0c9920f0898", "metadata": {}, "source": ["## Backup and restore greptime"]}, {"cell_type": "code", "execution_count": null, "id": "1372534e3bee4700", "metadata": {}, "outputs": [], "source": ["current_greptime_url = \"http://127.0.0.1:24000\"\n", "new_greptime_url = \"http://127.0.0.1:24000\"\n", "backup_dbs = ['sks', 'pythia']\n", "for db in backup_dbs:\n", "    pass"]}, {"cell_type": "code", "execution_count": 5, "id": "5eb32ff68738272a", "metadata": {"ExecuteTime": {"end_time": "2025-04-22T21:34:21.388277Z", "start_time": "2025-04-22T21:34:21.385409Z"}}, "outputs": [{"data": {"text/plain": ["['_reserved_prophecy:stddev:ap_radio_tx_data_bytes_total',\n", " '_reserved_prophecy:stddev:ap_radio_tx_failures_total',\n", " '_reserved_prophecy:yhat:ap_radio_tx_data_bytes_total',\n", " '_reserved_prophecy:yhat:ap_radio_tx_failures_total',\n", " 'ap_radio_vap_',\n", " 'ap_radio_vap_ack_rssi_chain_1',\n", " 'ap_radio_vap_ack_rssi_chain_2',\n", " 'ap_radio_vap_ack_rssi_chain_3',\n", " 'ap_radio_vap_ack_rssi_chain_4',\n", " 'ap_radio_vap_avg_ppdu_rx_rate_kbps',\n", " 'ap_radio_vap_avg_ppdu_tx_rate_kbps',\n", " 'ap_radio_vap_band_width',\n", " 'ap_radio_vap_broadcast_twt',\n", " 'ap_radio_vap_last_mgmt_rx_rate',\n", " 'ap_radio_vap_last_rx_rate',\n", " 'ap_radio_vap_last_tx_rate',\n", " 'ap_radio_vap_maxium_rate_per_client_kbps',\n", " 'ap_radio_vap_msdu_tx_failed_retry_count_total',\n", " 'ap_radio_vap_msdu_tx_multiple_retry_count_total',\n", " 'ap_radio_vap_msdu_tx_retry_count_total',\n", " 'ap_radio_vap_node_',\n", " 'ap_radio_vap_node_ession',\n", " 'ap_radio_vap_node_n',\n", " 'ap_radio_vap_node_on',\n", " 'ap_radio_vap_ow',\n", " 'ap_radio_vap_packets_queued_total',\n", " 'ap_radio_vap_rx_bytes_for_last_one_second',\n", " 'ap_radio_vap_rx_decryption_errors_total',\n", " 'ap_radio_vap_rx_mgmt_rssi',\n", " 'ap_radio_vap_rx_mpdu_count',\n", " 'ap_radio_vap_rx_multicast_data_bytes_total',\n", " 'ap_radio_vap_rx_packets_for_last_one_second',\n", " 'ap_radio_vap_rx_ppdu_count',\n", " 'ap_radio_vap_rx_retry_count',\n", " 'ap_radio_vap_rx_rssi',\n", " 'ap_radio_vap_rx_unicast_data_bytes_total',\n", " 'ap_radio_vap_twt_event_type',\n", " 'ap_radio_vap_twt_flow_id',\n", " 'ap_radio_vap_tx_bytes_for_last_one_second',\n", " 'ap_radio_vap_tx_dropped_valid_for_offload_driver_total',\n", " 'ap_radio_vap_tx_failed_total',\n", " 'ap_radio_vap_tx_packets_for_last_one_second',\n", " 'ap_radio_vap_tx_success_broadcast_data_bytes_total',\n", " 'ap_radio_vap_tx_success_broadcast_data_packets_total',\n", " 'ap_radio_vap_tx_success_data_bytes_total',\n", " 'ap_radio_vap_tx_success_data_packets_total',\n", " 'ap_radio_vap_tx_success_multicast_data_bytes_total',\n", " 'ap_radio_vap_tx_success_multicast_data_packets_total',\n", " 'ap_radio_vap_tx_success_unicast_data_bytes_total',\n", " 'ap_radio_vap_tx_success_unicast_data_packets_total',\n", " 'ap_radio_vap_w',\n", " 'tx_data_packets_per_ac_count',\n", " 'tx_data_packets_per_ac_sum']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_tables['332']"]}, {"cell_type": "code", "execution_count": null, "id": "b8926cf86c6fb2a1", "metadata": {}, "outputs": [], "source": ["import pendulum\n", "timestamp_threshold = pendulum.parse(\"2025-04-10\")\n", "non_site_tables = []\n", "for table in tables:\n", "    table_name = table[\"Tables\"]\n", "    try:\n", "        latest_value = new_greptime_client.execute(f'select * from \"{table_name}\" order by greptime_timestamp desc limit 1')\n", "    except Exception as e:\n", "        print(f\"Error: {e}\", table_name)\n", "        continue\n", "    if \"site_id\" not in latest_value[0]:\n", "        print(f'Table: {table_name} no site_id, latest value', latest_value[0])\n", "        non_site_tables.append(table_name)\n", "    # break"]}, {"cell_type": "code", "execution_count": null, "id": "48cbdaaceebab536", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2804b130e4d453da", "metadata": {}, "outputs": [], "source": ["non_site_tables"]}, {"cell_type": "code", "execution_count": null, "id": "2a399d0724eaa4df", "metadata": {}, "outputs": [], "source": ["failed_tables = []\n", "for t in tables:\n", "    table_name = t[\"Tables\"]\n", "    r = greptime_client.execute(f\"select * from {table_name} limit 1\")\n", "    if r:\n", "        continue\n", "        # print(table_name, r)\n", "    else:\n", "        print(f\"Table: {table_name} is empty\")\n", "        failed_tables.append(table_name)"]}, {"cell_type": "code", "execution_count": null, "id": "fbb489eca376a6a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2bc8944ebed2777f", "metadata": {}, "outputs": [], "source": ["# restore tables\n", "databases = [\"metrics\"]\n", "r = \"\"\"\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_ALERTS restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_ALERTS_FOR_STATE restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_cpu_used_percent_gauge restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_cpu_temperature restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_cpu_used_percent restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_disk_used_percent restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_mem_used_percent restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_net_bytes_recv restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_net_bytes_sent restored\n", "Table: t_31e65070_cb87_4849_9ead_d189e9e20835_d_virtual_mem_used_percent restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_0ff1275d_06fb_41f4_bf68_58e56284ffa4_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_27b06e80_2919_4727_b2fc_95f7d9b35b63_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_299bd630_a5ce_4d4b_b353_64931137a881_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_440a6629_d894_4282_9ed0_b96a9810c8ec_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_6c7359c4_c13a_4b98_bdd1_032fa05dfeaf_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_8ba6723b_3b98_4a23_ac54_2686ccfb73a3_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_afb16798_bad4_4d5b_8567_61304e635fe6_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_c5a690e2_f061_4051_8273_a47b0136655a_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_ddd5b477_f792_447a_96a0_141f2fa983b1_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db__reserved_default_e17384f3_80c5_45f1_b6aa_b088df449aed_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_cpu_temperature_gauge restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_cpu_temperature restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_cpu_used_percent restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_disk_used_percent restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_mem_used_percent restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_net_bytes_recv restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_net_bytes_sent restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_d_virtual_mem_used_percent restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_mem_used_percent_gauge restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_net_energy_used_counter restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_net_energy_used_gauge restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_prophecy:stddev:86ece23a_86eb_4137_906c_a5522d986974 restored\n", "Table: t_4436aa34_758b_447f_a6f4_23cc115e13db_prophecy:yhat:86ece23a_86eb_4137_906c_a5522d986974 restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_ALERTS restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_ALERTS_FOR_STATE restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_04320793_1468_475d_8a5d_ee49ab5159d2_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_098be1cf_7631_414b_86ba_2f42b96e106b_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_0d609320_c618_4c42_8f22_1b501a8a0c2d_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_203fa894_cbea_4d55_bbb8_e3719caa8fb3_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_272941d3_1086_4bd1_88b3_21f00c0c38ce_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_2bc6f9d6_6c57_408c_9c68_3a9de906f3cc_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_4a3e109e_0520_4db9_b2aa_62dad1753541_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_4fa305b1_7516_4101_aff6_4a5a75a4896c_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_5fdaa4d7_60a6_4cf3_86d3_76323e970907_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_6df48083_9c6f_4156_bc49_f648d817a2e6_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_9d48b630_d6b8_4f86_b2d3_2204ad2f1753_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_a282d82d_1ea3_4dde_b087_ded9707c6743_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_a745a062_c54c_400c_95ea_9849273093fd_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_c6d5fa1e_5c61_4d82_a7dc_0a96ba3e8793_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_cd45e27f_680c_4fa9_b368_40ceaf33cca9_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_e6f5ba28_ea6b_4887_8d58_aee71a476f0a_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d__reserved_default_eaf60f76_64f4_43e0_983b_292450023db1_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_cpu_used_percent restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_disk_used_percent restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_ifHCInOctets restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_ifHCOutOctets restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_mem_used_percent restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_net_bytes_recv restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_net_bytes_sent restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_d_virtual_mem_used_percent restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_net_bytes_recv_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_net_bytes_recv_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_net_bytes_recv_counter restored\n", "Table: t_5b6a8592_d31d_458a_b7ef_37093656585d_net_bytes_recv_counter restored\n", "\"\"\"\n", "from greptime import SyncGreptimeClient\n", "\n", "for db in databases:\n", "    greptime_client = SyncGreptimeClient(url=\"http://192.168.50.9:54130\", db=db, timeout=120)\n", "    tables = greptime_client.execute(\"show tables\")\n", "    for t in tables:\n", "        table_name = t[\"Tables\"]\n", "        if table_name.startswith(\"physical_\"):\n", "            continue\n", "        if table_name in r:\n", "            continue\n", "        try:\n", "            greptime_client.execute(\n", "                f\"COPY \\\"{table_name}\\\" FROM 's3://greptime/greptime/metrics/{table_name}.parquet' WITH (FORMAT = 'parquet') CONNECTION(REGION= 'us-east-1', ENDPOINT='http://garage.service.ffwd:3900', ACCESS_KEY_ID='GK1f2a64f5fc537328e63baf3d', SECRET_ACCESS_KEY='12029575e42a6211ca4636df1f92200360e747e80aa8120efb1c95885469a689');\"\n", "            )\n", "            print(f\"Table: {table_name} restored\")\n", "        except Exception as e:\n", "            print(f\"Error: {e}\", table_name)"]}, {"cell_type": "code", "execution_count": null, "id": "e874cef3ca760434", "metadata": {}, "outputs": [], "source": ["from asyncpg import create_pool\n", "\n", "failed_tables_str = \"\"\"t_5b6a8592_d31d_458a_b7ef_37093656585d_ifHCInOctets_counter\n", "t_5b6a8592_d31d_458a_b7ef_37093656585d_ifHCOutOctets_counter\n", "t_5b6a8592_d31d_458a_b7ef_37093656585d_mem_used_percent_gauge\n", "t_5b6a8592_d31d_458a_b7ef_37093656585d_net_bytes_sent_counter\n", "t_5b6a8592_d31d_458a_b7ef_37093656585d_power_usage_milliwatts_gauge\n", "t_5b6a8592_d31d_458a_b7ef_37093656585d_prophecy:stddev:69d5c8e2_b507_4de2_adb7_9eae8ccad03d\n", "t__reserved_ffwd_accounting_vector_component_sent_events_total\n", "t__reserved_ffwd_accounting_vector_component_sent_event_bytes_total\n", "t__reserved_ffwd_accounting_vector_component_received_event_bytes_total\n", "t_961478ea_4c14_4830_8f50_b504965adfe0_net_bytes_sent_total_counter\n", "t_961478ea_4c14_4830_8f50_b504965adfe0_net_bytes_recv_total_counter\n", "t_961478ea_4c14_4830_8f50_b504965adfe0_mem_used_percent_gauge\n", "t_961478ea_4c14_4830_8f50_b504965adfe0_disk_used_percent_gauge\n", "t_961478ea_4c14_4830_8f50_b504965adfe0_d_net_bytes_sent_total\n", "t_961478ea_4c14_4830_8f50_b504965adfe0_cpu_used_percent_gauge\n", "\"\"\"\n", "\n", "failed_tables = failed_tables_str.split(\"\\n\")\n", "pool = await create_pool(\n", "    database=\"metrics\", user=\"greptime\", host=\"127.0.0.1\", port=54132, max_inactive_connection_lifetime=30000\n", ")\n", "async with pool.acquire() as conn:\n", "    for table_name in failed_tables:\n", "        if not table_name:\n", "            continue\n", "        await conn.execute(\n", "            f\"COPY \\\"{table_name.strip()}\\\" FROM 's3://greptime/greptime/metrics/{table_name}.parquet' WITH (FORMAT = 'parquet') CONNECTION(REGION= 'us-east-1', ENDPOINT='http://garage.service.ffwd:3900', ACCESS_KEY_ID='GK1f2a64f5fc537328e63baf3d', SECRET_ACCESS_KEY='12029575e42a6211ca4636df1f92200360e747e80aa8120efb1c95885469a689');\"\n", "        )\n", "        print(\"restoring table\", table_nam"]}, {"cell_type": "code", "execution_count": null, "id": "bc19dc821b47fb01", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "784637e01d061fea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cc058aa3ced295d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e570af7062cbecb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}