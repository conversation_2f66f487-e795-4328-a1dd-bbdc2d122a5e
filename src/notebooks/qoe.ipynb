{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2025-07-01T05:32:11.446940Z", "start_time": "2025-07-01T05:32:11.382432Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-07-01T05:32:12.559961Z", "start_time": "2025-07-01T05:32:12.547244Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/dev/core0/new-pythia/pythia/src /Users/<USER>/dev/core0/new-pythia/pythia/.env\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "import os\n", "import pathlib\n", "\n", "env_path = pathlib.Path(os.getcwd(), \"../.env\").resolve().absolute()\n", "print(os.getcwd(), env_path)\n", "load_dotenv(env_path)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mimir_config: {}\n", "\u001b[2m2025-07-21 15:42:28\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mquerying mimir with 1 requests\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-07-17 08:00:00+08:00</td>\n", "      <td>299.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-07-17 08:01:00+08:00</td>\n", "      <td>300.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-07-17 08:02:00+08:00</td>\n", "      <td>305.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-07-17 08:03:00+08:00</td>\n", "      <td>300.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-07-17 08:04:00+08:00</td>\n", "      <td>300.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4311</th>\n", "      <td>2025-07-20 07:56:00+08:00</td>\n", "      <td>299.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4312</th>\n", "      <td>2025-07-20 07:57:00+08:00</td>\n", "      <td>305.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4313</th>\n", "      <td>2025-07-20 07:58:00+08:00</td>\n", "      <td>302.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4314</th>\n", "      <td>2025-07-20 07:59:00+08:00</td>\n", "      <td>295.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4315</th>\n", "      <td>2025-07-20 08:00:00+08:00</td>\n", "      <td>300.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4316 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                     timestamp  value\n", "0    2025-07-17 08:00:00+08:00  299.0\n", "1    2025-07-17 08:01:00+08:00  300.0\n", "2    2025-07-17 08:02:00+08:00  305.0\n", "3    2025-07-17 08:03:00+08:00  300.0\n", "4    2025-07-17 08:04:00+08:00  300.0\n", "...                        ...    ...\n", "4311 2025-07-20 07:56:00+08:00  299.0\n", "4312 2025-07-20 07:57:00+08:00  305.0\n", "4313 2025-07-20 07:58:00+08:00  302.0\n", "4314 2025-07-20 07:59:00+08:00  295.0\n", "4315 2025-07-20 08:00:00+08:00  300.0\n", "\n", "[4316 rows x 2 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"name": "stdout", "output_type": "stream", "text": ["10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n", "10{\"stdout\":\"[{\\\"variableName\\\": \\\"ID_TO_MEANING\\\", \\\"type\\\": \\\"dictionary\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"builtins.dict\\\"}, {\\\"variableName\\\": \\\"NULL\\\", \\\"type\\\": \\\"unknown\\\", \\\"supportedEngines\\\": [\\\"pandas\\\"], \\\"isLocalVariable\\\": true, \\\"rawType\\\": \\\"_pydevd_bundle.pydevd_constants.Null\\\"}]\\n\",\"stderr\":\"\",\"mime\":[]}\n"]}], "source": ["from prom.prom import PrometheusClient\n", "from prophecy.job.config import init_config\n", "from prophecy.job.prom_reads import batch_series_query\n", "from typing import cast\n", "import pendulum\n", "\n", "end = cast(pendulum.DateTime, pendulum.parse(\"2025-07-20T00:00:00Z\"))\n", "start = end.subtract(days=3)\n", "resolution = 60\n", "tenant_id = \"332\"\n", "step = \"10m\"\n", "metric_selector = f\"ap_radio_vap_node_rx_rssi{{site_id=\\\"{tenant_id}\\\"}}\"\n", "promql = f\"avg_over_time({metric_selector}[{step}])\"\n", "\n", "prometheus_client = PrometheusClient(\n", "    mimir_enabled=True,\n", "    mimir_frontend_url=\"http://127.0.0.1:19100\",\n", "    headers={\n", "        # \"x-greptime-db-name\": \"sks\",\n", "        \"X-Scope-OrgID\": \"4436aa34-758b-447f-a6f4-23cc115e13db\"\n", "    },\n", ")\n", "\n", "q2 = '{__name__=\"_reserved_default_d86278b8_3e1e_44b2_af8b_175eca5f7b5d_counter\",_reserved_flow_id=\"ced2d94d-3f35-4c1d-8c03-49394fcb0edb\"} - {__name__=\"_reserved_default_d86278b8_3e1e_44b2_af8b_175eca5f7b5d_counter\",_reserved_flow_id=\"ced2d94d-3f35-4c1d-8c03-49394fcb0edb\"} offset 60s > 0 * label_replace(vector(1), \"_reserved_flow_id\", \"ced2d94d-3f35-4c1d-8c03-49394fcb0edb\", \"\", \"\")'\n", "\n", "df = batch_series_query(prometheus_client, q2, start.int_timestamp, end.int_timestamp, resolution, multi_series=False)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-07-01T05:32:18.783060Z", "start_time": "2025-07-01T05:32:16.461596Z"}}, "outputs": [], "source": ["import asyncio\n", "import httpx\n", "import pandas as pd\n", "import pendulum\n", "from pypika import PostgreSQLQuery as Q, Table, functions as fn\n", "from anomaly_detector.sks.utils import get_mcs_index\n", "from config import get_settings\n", "from dependencies import get_async_greptime, get_async_greptime_promql_client\n", "from greptime import AsyncGreptimeClient\n", "from services import ServicesRegistry\n", "from utils import try_parse_float\n", "from anomaly_detector.job.share.utils import prom_result_to_panda_df\n", "\n", "\n", "def _remove_name_label(labels):\n", "    if \"__name__\" in labels:\n", "        del labels[\"__name__\"]\n", "    return labels\n", "\n", "\n", "settings = get_settings()\n", "svc_registry = ServicesRegistry(settings.consul, dict(settings.services))\n", "prom_client = httpx.AsyncClient(\n", "    base_url=f\"http://127.0.0.1:24000/v1/prometheus/api/v1\", headers={\"x-greptime-db-name\": \"sks\"}\n", ")\n", "greptime_client = AsyncGreptimeClient(svc_registry.service(\"greptime\").url(), settings.greptime.sks_db)\n", "step = \"10m\"\n", "end_dt = pendulum.now()\n", "start_time = pendulum.now().subtract(minutes=30)\n", "site_id = \"332\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["settings.llm"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-07-01T06:39:07.829093Z", "start_time": "2025-07-01T06:39:07.129795Z"}}, "outputs": [], "source": ["from anomaly_detector.sks.qoe import default_omit_labels\n", "from anomaly_detector.marker import PromMarker\n", "from anomaly_detector.sks.marker import MarkerType, Tag, get_prom_marker_promql\n", "import numpy as np\n", "\n", "nss_prom = PromMarker(\n", "    \"ap_radio_vap_node_tx_nss_bucket\",\n", "    Tag.LINK,\n", "    MarkerType.INDIVIDUAL_HISTOGRAM,\n", "    omit_labels=list(default_omit_labels),\n", "    qoes=[\"symmetry_score\"],\n", ")\n", "step = \"10m\"\n", "end = pendulum.parse(\"2025-07-01T00:10:00Z\")\n", "start = end.subtract(minutes=30)\n", "site_id = \"84947\"\n", "\n", "q = get_prom_marker_promql(nss_prom, \"10m\", {\"site_id\": 84947})\n", "prom_client = httpx.AsyncClient(\n", "    base_url=f\"http://127.0.0.1:24000/v1/prometheus/api/v1\", headers={\"x-greptime-db-name\": \"sks\"}\n", ")\n", "print(\"promql\", q)\n", "res = await prom_client.get(\n", "    \"/query_range\",\n", "    params={\n", "        \"start\": start.to_iso8601_string() if start else None,\n", "        \"end\": end.to_iso8601_string() if end else None,\n", "        \"query\": q,\n", "        \"step\": step,\n", "        \"db\": \"sks\",\n", "    },\n", ")\n", "\n", "data = res.json()\n", "df = prom_result_to_panda_df(data, nss_prom.name, multi_series=True)\n", "df[\"le\"] = pd.to_numeric(df[\"le\"], errors=\"coerce\")\n", "df = df[~np.isinf(df[\"le\"]) & ~np.isnan(df[\"le\"])]\n", "\n", "\n", "def to_incremental(group):\n", "    group = group.sort_values(\"le\")\n", "    group[nss_prom.name] = group[nss_prom.name].diff().fillna(group[nss_prom.name]).clip(lower=0)\n", "    return group\n", "\n", "\n", "value_column = nss_prom.name\n", "timestamp_column = \"timestamp\"\n", "bucket_column = \"le\"\n", "label_columns = df.columns.difference([timestamp_column, value_column, bucket_column])\n", "df = df.groupby([\"timestamp\", \"radio\", \"ap_mac\", \"vap\"]).apply(to_incremental, include_groups=False).reset_index()\n", "df.drop(columns=[\"level_4\"], inplace=True)\n", "df[timestamp_column] = df[timestamp_column].astype(int) // 1e9\n", "\n", "from greptime import greptime_prom_response_metrics\n", "\n", "greptime_prom_response_metrics(df.to_dict(orient=\"records\"), timestamp_column=\"timestamp\", value_column=nss_prom.name,\n", "                               exclude_columns=[\"timestamp\", nss_prom.name])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pendulum import DateTime\n", "from anomaly_detector.sks.mcs_nss import calculate_mcs_nss_qoes\n", "\n", "step = \"10m\"\n", "end_time = pendulum.parse(\"2025-07-08T07:20:00Z\")\n", "if not isinstance(end_time, DateTime):\n", "    raise ValueError(\"end_time must be a DateTime object\")\n", "start_time = end_time.subtract(minutes=10)\n", "site_id = \"84947\"\n", "\n", "mcs_df = await calculate_mcs_nss_qoes(\n", "    greptime_client=greptime_client,\n", "    promql_async_client=prom_client,\n", "    start_time=start_time,\n", "    end_time=end_time,\n", "    site_id=site_id,\n", ")\n", "mcs_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from greptime import greptime_timestamp_sql\n", "from anomaly_detector.sks.sqls import throughput_qoe\n", "import pendulum\n", "\n", "step = \"10m\"\n", "end_time = pendulum.parse(\"2025-07-08T07:20:00Z\")\n", "if not isinstance(end_time, pendulum.DateTime):\n", "    raise ValueError(\"end_time must be a DateTime object\")\n", "start_time = end_time.subtract(minutes=600)\n", "site_id = \"84947\"\n", "\n", "print(greptime_timestamp_sql(start_time, end_time, throughput_qoe, {\"site_id\": site_id}))"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "pythia-BPx0uQVf-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}