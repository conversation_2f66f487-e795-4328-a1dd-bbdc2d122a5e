{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/dev/core0/new-pythia/pythia/src /Users/<USER>/dev/core0/new-pythia/pythia/.env\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "import os\n", "import pathlib\n", "\n", "env_path = pathlib.Path(os.getcwd(), \"../.env\").resolve().absolute()\n", "print(os.getcwd(), env_path)\n", "load_dotenv(env_path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pythia-BPx0uQVf-py3.11/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Importing plotly failed. Interactive plots will not work.\n", "/Users/<USER>/dev/core0/new-pythia/pythia/src/anomaly_detector/job/share/peak_detection.py:3: UserWarning: A NumPy version >=1.22.4 and <2.3.0 is required for this version of SciPy (detected version 2.3.0)\n", "  from scipy import signal\n"]}], "source": ["import asyncio\n", "import httpx\n", "import pandas as pd\n", "import pendulum\n", "from pypika import PostgreSQLQuery as Q, Table, functions as fn\n", "from anomaly_detector.sks.utils import get_mcs_index\n", "from config import get_settings\n", "from dependencies import get_async_greptime, get_async_greptime_promql_client\n", "from greptime import AsyncGreptimeClient\n", "from services import ServicesRegistry\n", "from utils import try_parse_float\n", "from anomaly_detector.job.share.utils import prom_result_to_panda_df\n", "from anomaly_detector.sks.marker import get_prom_marker_promql\n", "\n", "settings = get_settings()\n", "svc_registry = ServicesRegistry(settings.consul, dict(settings.services))\n", "prom_client = await get_async_greptime_promql_client(svc_registry)\n", "greptime_client = AsyncGreptimeClient(svc_registry.service(\"greptime\").url(), settings.greptime.sks_db)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["progress: 0/56549\n", "progress: 1000/56549\n", "progress: 2000/56549\n", "progress: 3000/56549\n", "progress: 4000/56549\n", "progress: 5000/56549\n", "progress: 6000/56549\n", "progress: 7000/56549\n", "progress: 8000/56549\n", "progress: 9000/56549\n", "progress: 10000/56549\n", "progress: 11000/56549\n", "progress: 12000/56549\n", "progress: 13000/56549\n", "progress: 14000/56549\n", "progress: 15000/56549\n", "progress: 16000/56549\n", "progress: 17000/56549\n", "progress: 18000/56549\n", "progress: 19000/56549\n", "progress: 20000/56549\n", "progress: 21000/56549\n", "progress: 22000/56549\n", "progress: 23000/56549\n", "progress: 24000/56549\n", "progress: 25000/56549\n", "progress: 26000/56549\n", "progress: 27000/56549\n", "progress: 28000/56549\n", "progress: 29000/56549\n", "progress: 30000/56549\n", "progress: 31000/56549\n", "progress: 32000/56549\n", "progress: 33000/56549\n", "progress: 34000/56549\n", "progress: 35000/56549\n", "progress: 36000/56549\n", "progress: 37000/56549\n", "progress: 38000/56549\n", "progress: 39000/56549\n", "progress: 40000/56549\n", "progress: 41000/56549\n", "progress: 42000/56549\n", "progress: 43000/56549\n", "progress: 44000/56549\n", "progress: 45000/56549\n", "progress: 46000/56549\n", "progress: 47000/56549\n", "progress: 48000/56549\n", "progress: 49000/56549\n", "progress: 50000/56549\n", "progress: 51000/56549\n", "progress: 52000/56549\n", "progress: 53000/56549\n", "progress: 54000/56549\n", "progress: 55000/56549\n", "progress: 56000/56549\n", "progress: 0/1144\n", "progress: 1000/1144\n"]}], "source": ["from prom_writer.writer import MetricPoint, PrometheusRemoteWriter\n", "import orjson\n", "\n", "base = pathlib.Path(\"/Users/<USER>/dev/core0/new-pythia/pythia\")\n", "# tables = [\"ap_node_connection_failure_duration_ns\", \"ap_node_connection_success_duration_ns\"]\n", "tables = [\"ap_node_connection_failure_duration_ns\", \"ap_node_connection_success_duration_ns\"]\n", "\n", "for table in tables:\n", "    json_path = base / f\"{table}.json\"\n", "    df = pd.read_json(json_path, lines=True, dtype=False)\n", "\n", "    df = df.dropna(subset=[\"site_id\"])\n", "    df = df.sort_values(by=\"greptime_timestamp\", ascending=True)\n", "\n", "    prometheus_writer = PrometheusRemoteWriter(\n", "        base_url=\"http://localhost:24000\",\n", "        tenant_id=\"0\",\n", "        params={\"db\": \"sks\"},\n", "    )\n", "\n", "    metric_points = [\n", "        MetricPoint(\n", "            metric_name=table,\n", "            timestamp=int(pendulum.parse(row[\"greptime_timestamp\"]).timestamp()),\n", "            value=row[\"greptime_value\"],\n", "            labels={\n", "                \"site_id\": row[\"site_id\"],\n", "                \"ap_mac\": row[\"ap_mac\"],\n", "                \"auth_mode\": row[\"auth_mode\"],\n", "                \"bssid\": row[\"bssid\"],\n", "                \"reason\": row[\"reason\"],\n", "                \"state\": row[\"state\"],\n", "                \"webauth\": row[\"webauth\"],\n", "                \"sta_mac\": row[\"sta_mac\"],\n", "            },\n", "        )\n", "        for _, row in df.iterrows()\n", "    ]\n", "\n", "    for batch in range(0, len(metric_points), 1000):\n", "        prometheus_writer.write_metric_points(metric_points[batch:batch + 1000])\n", "        print(f\"progress: {batch}/{len(metric_points)}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["df = pd.read_json(json_path, lines=True, dtype=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["progress: 0/1144\n", "progress: 1000/1144\n"]}], "source": []}], "metadata": {"kernelspec": {"display_name": "pythia-BPx0uQVf-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}