#merge [#include "prism.edn"
        {:services-bucket        #or [#env SERVICES_BUCKET "ffwd-services"]
         :metronome-url          #or [#env METRONOME_URL "http://ffwd-metronome.service.ffwd:8911"]
         :mimir-enabled?         #boolean #or [#env MIMIR_ENABLED "false"]
         :greptime-logs-enabled? #boolean #or [#env GREPTIME_LOGS_ENABLED "false"]
         :nomad-alloc-id         #or [#env NOMAD_ALLOC_ID "local"]
         :job-threads            #long #or [#env JOB_THREADS 20]
         :llm                    {:openai #env OPENAI_API_KEY}
         :on-premises-gpu?       #boolean #or [#env ON_PREMISES_GPU "false"]
         :sagemaker              {:region         #env SAGEMAKER_AWS_REGION
                                  :access-key     #env SAGEMAKER_ACCESS_KEY
                                  :secret-key     #env SAGEMAKER_SECRET_KEY
                                  :execution-role #env SAGEMAKER_EXECUTION_ROLE
                                  :bucket         #env SAGEMAKER_BUCKET}
         :greptime               {:dbname #or [#env GREPTIME_DB "pythia"]}
         :services               {:loki-frontend  {:service :ffwd-loki
                                                   :tag     :query-frontend}
                                  :greptime       {:service #keyword #or [#env GREPTIME_SERVICE_NAME "greptime"]}
                                  :llamington     {:service :ffwd-llamington}
                                  :mimir-proxy    {:service :mimir-proxy-http}
                                  :mimir-frontend {:service :ffwd-mimir
                                                   :tag     :query-frontend}
                                  :s3             {:service :garage}
                                  :ollama         {:service :ollama}}}]
