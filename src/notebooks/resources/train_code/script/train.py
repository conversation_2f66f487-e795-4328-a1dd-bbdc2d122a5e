import argparse
import logging
import os
import sys

import datasets
import torch.cuda
from trainer import EarlyStoppingCallback, LogBertTrainer, OnEpochBeginCallback
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
    BertConfig,
    BertForMaskedLM,
    DataCollatorForLanguageModeling,
    TrainingArguments,
)
from transformers.trainer_utils import get_last_checkpoint

max_len = 512
hidden_size = 256

logger = logging.getLogger(__name__)

logging.basicConfig(
    level=logging.getLevelName("INFO"),
    handlers=[logging.StreamHandler(sys.stdout)],
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)


def print_files_in_dir(directory):
    for root, _dirs, files in os.walk(directory):
        for file in files:
            print(os.path.join(root, file))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    # hyperparameters sent by the client are passed as command-line arguments to the script.
    parser.add_argument("--epochs", type=int, default=20)
    parser.add_argument("--train_batch_size", type=int, default=16)
    parser.add_argument("--eval_batch_size", type=int, default=32)
    parser.add_argument("--warmup_steps", type=int, default=500)
    parser.add_argument("--from_scratch", type=bool, default=True)
    parser.add_argument("--early_stopping_threshold", type=float, default=0.002)
    parser.add_argument("--early_stopping_target", type=float, default=0.08)
    parser.add_argument("--early_stopping_patience", type=float, default=10)
    parser.add_argument("--output_dir", type=str)
    parser.add_argument("--model_name", type=str)
    # parser.add_argument("--learning_rate", type=str, default=5e-5)

    # Data, model, and output directories
    parser.add_argument("--output_data_dir", type=str, default=os.environ.get("SM_OUTPUT_DATA_DIR"))
    parser.add_argument("--model_dir", type=str, default=os.environ.get("SM_MODEL_DIR"))
    parser.add_argument("--tokenizer_dir", type=str, default=os.environ.get("SM_MODEL_DIR"))
    parser.add_argument("--n_gpus", type=str, default=os.environ.get("SM_NUM_GPUS"))
    parser.add_argument("--training_dir", type=str, default=os.environ.get("SM_CHANNEL_TRAINING"))
    parser.add_argument("--test_dir", type=str, default=os.environ.get("SM_CHANNEL_TEST"))

    args, _ = parser.parse_known_args()

    logger.info(f"args: {args}")

    logger.info(f"training_dir {args.training_dir}")
    tokenizer = AutoTokenizer.from_pretrained(args.training_dir + "/tokenizer")
    logging.info("tokenizer initialized")
    # load datasets
    data = datasets.load_from_disk(args.training_dir + "/dataset")
    logging.info("dataset loaded")

    # compute metrics function for binary classification
    def compute_metrics(pred):
        # preds = pred.predictions.argmax(-1)
        # precision, recall, f1, _ = precision_recall_fscore_support(labels, preds, average="binary")
        # acc = accuracy_score(labels, preds)
        print("labels:", pred)
        return {"test": 0}

    if args.from_scratch:
        config = BertConfig(
            vocab_size=tokenizer.vocab_size,
            type_vocab_size=2,
            hidden_size=hidden_size,
            num_hidden_layers=4,
            num_attention_heads=4,
            intermediate_size=hidden_size * 2,  # logbert use 2, but bert use 4
        )
        model = BertForMaskedLM(config)
    else:
        model = AutoModelForSequenceClassification.from_pretrained(args.training_dir + "/pretrained_model")

    training_args = TrainingArguments(
        output_dir=args.output_dir,
        overwrite_output_dir=get_last_checkpoint(args.output_dir) is not None,
        num_train_epochs=args.epochs,
        learning_rate=1e-3,
        logging_steps=200,
        adam_beta1=0.9,
        adam_beta2=0.999,
        weight_decay=0,
        fp16=torch.cuda.is_available(),
        # half_precision_backend="cuda_amp",
        per_device_train_batch_size=args.train_batch_size,
        per_device_eval_batch_size=args.eval_batch_size,
        save_total_limit=2,
        save_strategy="epoch",
        warmup_steps=args.warmup_steps,
        logging_first_step=True,
        load_best_model_at_end=True,
        evaluation_strategy="epoch",
    )

    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm_probability=0.5,
        pad_to_multiple_of=8,
    )

    trainer = LogBertTrainer(
        model=model,
        args=training_args,
        compute_metrics=compute_metrics,
        train_dataset=data["train"],
        eval_dataset=data["test"],
        data_collator=data_collator,
        tokenizer=tokenizer,
    )

    trainer.add_callback(OnEpochBeginCallback(trainer))
    trainer.add_callback(
        EarlyStoppingCallback(
            args.early_stopping_patience,
            args.early_stopping_threshold,
            args.early_stopping_threshold,
        ),
    )

    trainer.train()

    # evaluate model
    eval_result = trainer.evaluate(eval_dataset=data["test"])

    # writes eval result to file which can be accessed later in s3 ouput
    with open(os.path.join(args.output_data_dir, "eval_results.txt"), "w") as writer:
        print("***** Eval results *****")
        writer.writelines(f"{key} = {value}\n" for key, value in sorted(eval_result.items()))

    trainer.save_state()
    # Saves the model to s3
    trainer.save_model(args.model_dir)
