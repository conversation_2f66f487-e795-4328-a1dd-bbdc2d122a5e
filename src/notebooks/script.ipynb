{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["os.getcwd()\n", "data = json.loads(Path(\"/Users/<USER>/dev/core0/new-pythia/pythia/x.json.json\").read_text())"]}], "metadata": {"kernelspec": {"display_name": "pythia-BPx0uQVf-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}