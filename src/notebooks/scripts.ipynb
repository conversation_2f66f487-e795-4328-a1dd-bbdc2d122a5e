{"cells": [{"metadata": {"collapsed": true}, "cell_type": "code", "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "print(os.getcwd())\n", "load_dotenv(os.path.join(\"../../\", \".env\"))"], "id": "initial_id", "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "600e2ae6a0386e4c", "metadata": {}, "source": ["import pendulum\n", "from job.model import JobModel, JobType, JobStatus, GreptimeAnomalyDetectorConfig, GreptimeProphecyConfig\n", "\n", "ad_job = JobModel(\n", "    id=\"b57305d5-10cf-4d8e-b148-5f65bdbd2c7f\",\n", "    type=JobType.log_anomaly,\n", "    tenant_id=\"sks\",\n", "    job_source_id=\"sks\",\n", "    status=JobStatus.pending,\n", "    created=pendulum.parse(\"2025-02-26T15:00:00Z\"),\n", "    config=GreptimeAnomalyDetectorConfig(\n", "        table_name=\"skslog\",\n", "        initial_trained_date=None,\n", "        log_rate_metric=\"\",\n", "        prophecy_id=\"\",\n", "        scheduled_train_date=pendulum.parse(\"2025-03-06T15:00:00Z\"),\n", "        skip_public_holiday=False,\n", "        trained=False,\n", "        trained_date=None,\n", "    ),\n", ")\n", "\n", "# {\"flow-id\":\"sks\",\"initial-trained-date\":null,\"log-rate-metric\":\"\",\"prophecy-id\":\"sks\",\"scheduled-train-date\":\"2025-03-06T15:00:00Z\",\"skip-public-holiday\":false,\"trained\":false,\"trained-date\":null}"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "4c2174225deddcdc", "metadata": {}, "source": "## create prophecy sql job"}, {"cell_type": "code", "id": "e33edb77f8782b7a", "metadata": {}, "source": ["from anomaly_detector.sks.sqls import coverage_sql, throughput_qoe\n", "from anomaly_detector.sks.marker import MarkerType, Tag, CustomMarker\n", "import pendulum\n", "from job.model import JobModel, JobType, JobStatus, GreptimeAnomalyDetectorConfig\n", "from database import get_db_connection_pool\n", "\n", "markers = [\n", "    CustomMarker(\n", "        \"coverage_qoe\",\n", "        Tag.QOE,\n", "        MarkerType.GAUGE,\n", "        sql=coverage_sql,\n", "        sub_markers=[\n", "            \"coverage_interference_score\",\n", "            \"coverage_rssi_score\",\n", "            \"coverage_link_score\",\n", "        ],\n", "    ),\n", "    CustomMarker(\n", "        \"throughput_qoe\",\n", "        Tag.QOE,\n", "        MarkerType.GAUGE,\n", "        sql=throughput_qoe,\n", "        sub_markers=[\n", "            \"throughput_efficiency\",\n", "            \"throughput_success_rate\",\n", "        ],\n", "    ),\n", "]\n", "\n", "\n", "async def create_prophecy_sql_job(cm: CustomMarker, submarker: str | None = None):\n", "    pool = await get_db_connection_pool()\n", "    async with pool.acquire() as conn:\n", "        column = submarker if submarker else cm.name\n", "        config = GreptimeProphecyConfig(\n", "            sql=cm.sql,\n", "            timestamp_column=cm.timestamp_field,\n", "            value_column=column,\n", "        )\n", "        await conn.execute(\n", "            \"\"\"\n", "            INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)\n", "            VALUES ($1, $2, $3, $4, $5, $6, $7)\n", "            \"\"\",\n", "            \"sks\",\n", "            column,\n", "            pendulum.now().add(days=-5).naive(),\n", "            pendulum.now().add(days=-5).naive(),\n", "            JobType.prophecy,\n", "            20,\n", "            config.kebab_dump(),\n", "        )\n", "\n", "\n", "for marker in markers:\n", "    await create_prophecy_sql_job(marker)\n", "    for sub_marker in marker.sub_markers:\n", "        await create_prophecy_sql_job(marker, sub_marker)"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["from pypika import PostgreSQLQuery as Q, Table\n", "from database import get_db_connection_pool\n", "from job.model import JobModel, JobType\n", "\n", "jobs = Table('jobs')\n", "pool = await get_db_connection_pool()\n", "site_id = \"sks\"\n", "async with pool.acquire() as conn:\n", "    q = Q.from_(jobs).select(jobs.job_source_id).where((jobs.type == JobType.prophecy) & (jobs.tenant_id == site_id))\n", "    print(q)\n", "    res = await conn.fetch(str(q))\n", "    for r in res:\n", "        print(r['job_source_id'])"], "id": "3d5a1ff165fe1760", "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "fb3293ce40ccb987", "metadata": {}, "source": "", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": "## Run anomaly detector job", "id": "b3501c583dc062f2"}, {"metadata": {}, "cell_type": "code", "source": ["from services import ServicesRegistry\n", "from config import get_settings\n", "from database import get_db_connection_pool\n", "from anomaly_detector.job.job import anomaly_detector_job_factory, AnomalyDetectorInferenceJob, \\\n", "    AnomalyDetectorTrainingJob\n", "from job.model import JobModel, JobType\n", "\n", "settings = get_settings()\n", "service_registry = ServicesRegistry(settings.consul, dict(settings.services))\n", "job_id = \"44a4ec23-f807-4da0-83c4-9b35d186e77d\"\n", "pool = await get_db_connection_pool()\n", "# job_record = None\n", "# anomaly_detector_job = None\n", "async with pool.acquire() as conn:\n", "    records = await conn.fetch(\n", "        \"select * from jobs where id = $1\", job_id\n", "    )\n", "    record = records[0]\n", "    job_record = JobModel(**record)\n", "    job_record.next_run = job_record.next_run.add(days=-1)\n", "    anomaly_detector_job: AnomalyDetectorTrainingJob = anomaly_detector_job_factory(job_record, settings,\n", "                                                                                    service_registry, pool)\n", "\n", "print(job_record, anomaly_detector_job)"], "id": "7f02331a841a12c6", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["args = await anomaly_detector_job.prepare_args()\n", "print(args)\n", "anomaly_detector_job.run()(**args)"], "id": "21dfa37f8245f729", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["from llm.providers import OpenAIProvider\n", "\n", "openai_provider = OpenAIProvider(\n", "    openai_api_key=\"********************************************************\",\n", "    model=OpenAIProvider.models[\"gpt-4.1\"],\n", ")"], "id": "147613ccc740e834", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "openai_provider.client.base_url", "id": "b1951a81b728e66", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": "## Run prophecy job", "id": "db9e0a29366b8dc8"}, {"metadata": {}, "cell_type": "code", "source": ["from services import ServicesRegistry\n", "from config import get_settings\n", "from database import get_db_connection_pool\n", "from prophecy import ProphecyTrainingJob, ProphecyPredictionJob, prophecy_job_factory\n", "from job.model import JobModel, JobType\n", "\n", "settings = get_settings()\n", "service_registry = ServicesRegistry(settings.consul, dict(settings.services))\n", "job_id = \"28bc7773-a7a8-4ae6-8923-e945cae94fd6\"\n", "pool = await get_db_connection_pool()\n", "async with pool.acquire() as conn:\n", "    records = await conn.fetch(\n", "        \"select * from jobs where id = $1\", job_id\n", "    )\n", "    record = records[0]\n", "    job_record = JobModel(**record)\n", "    # job_record.next_run = job_record.next_run.add(days=-1)\n", "    prophecy_job: ProphecyTrainingJob = prophecy_job_factory(job_record=job_record, settings=settings,\n", "                                                             services_registry=service_registry, pool=pool)\n", "\n", "print(job_record, prophecy_job)"], "id": "cf8e382325b07847", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["args = await prophecy_job.prepare_args()\n", "print(args)\n", "prophecy_job.run()(*args)"], "id": "b017faec544523c9", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "import", "id": "a801227718acb773"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}