{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["\n", "%load_ext autoreload\n", "%autoreload 2\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5415da28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/dev/core0/new-pythia/pythia/src /Users/<USER>/dev/core0/new-pythia/pythia/.env\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "import os\n", "import pathlib\n", "\n", "env_path = pathlib.Path(os.getcwd(), \"../.env\").resolve().absolute()\n", "print(os.getcwd(), env_path)\n", "load_dotenv(env_path)"]}, {"cell_type": "code", "execution_count": 4, "id": "91c0531832065b1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["update marker ap_radio_rx_crc_errors_total with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_rx_crc_errors_total\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_rx_phy_errors_total with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_rx_phy_errors_total\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_rx_data_packets_total with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_rx_data_packets_total\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_rx_mgmt_frames_total with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_rx_mgmt_frames_total\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_rx_rssi with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_radio_rx_rssi\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_rx_mcs_bucket with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_vap_node_rx_mcs_bucket\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_tx_mcs_bucket with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_vap_node_tx_mcs_bucket\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_rx_nss_bucket with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_vap_node_rx_nss_bucket\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_tx_nss_bucket with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_vap_node_tx_nss_bucket\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_maxium_rate_per_client_kbps with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_radio_vap_node_maxium_rate_per_client_kbps\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_tx_data_packets_total with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_tx_data_packets_total\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_tx_failures_total with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (increase({__name__=\"ap_radio_tx_failures_total\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_node_signal_power with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_node_signal_power\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_average_tx_rate_kbps with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_radio_vap_node_average_tx_rate_kbps\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_radio_vap_node_average_rx_rate_kbps with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_radio_vap_node_average_rx_rate_kbps\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_status_cpu_avg_usage with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_status_cpu_avg_usage\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_status_mem_avg_usage with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_status_mem_avg_usage\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_node_connection_success_duration_ns with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_node_connection_success_duration_ns\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_node_connection_failure_duration_ns with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_node_connection_failure_duration_ns\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n", "update marker ap_node_connection_state_duration_ns with config {'trained': False, 'query': 'avg without (__name__, sta_mac, state, site_id) (avg_over_time({__name__=\"ap_node_connection_state_duration_ns\",site_id=\"84947\"}[10m]))', 'multi-series': True}\n"]}], "source": ["\n", "import pendulum\n", "from anomaly_detector.sks.qoe import sks_markers, PromMarker\n", "from job.model import GreptimeProphecyConfig, JobType, ProphecyConfig\n", "import orjson\n", "from asyncpg import Connection, create_pool\n", "from job.model import AnomalyDetectorConfig, JobStatus, JobType, table as jobs\n", "from pypika import Criterion, Order, PostgreSQLQuery as Q, Table\n", "\n", "pool = await create_pool(database=\"pythia\", user=\"postgres\", host=\"127.0.0.1\", port=26257, password=\"postgres\")\n", "\n", "dt = pendulum.parse(\"2025-07-20T16:00:00Z\")\n", "\n", "tenant_id = \"84947\"\n", "\n", "\n", "async def update_prophecy_sql_job(\n", "    conn: Connection,\n", "    tenant_id: str,\n", "    custom_marker: CustomMarker,\n", "    sub_marker: str | None = None,\n", "):\n", "    column = sub_marker if sub_marker else custom_marker.name\n", "    config = GreptimeProphecyConfig(\n", "        sql=custom_marker.sql,\n", "        timestamp_column=custom_marker.timestamp_field,\n", "        value_column=column,\n", "        extra_filters={\"site_id\": tenant_id},\n", "    )\n", "    await conn.execute(\n", "        \"\"\"\n", "        UPDATE jobs\n", "        SET config = $1\n", "        WHERE tenant_id = $2\n", "          AND job_source_id = $3\n", "          AND type = $4\n", "        \"\"\",\n", "        config.kebab_dump(),\n", "        tenant_id,\n", "        column,\n", "        JobType.prophecy,\n", "    )\n", "\n", "\n", "async with pool.acquire() as conn:\n", "    await conn.set_type_codec(\n", "        \"jsonb\", encoder=lambda v: orjson.dumps(v).decode(), decoder=orjson.loads, schema=\"pg_catalog\"\n", "    )\n", "    for marker in sks_markers:\n", "        if isinstance(marker, PromMarker) and marker.name != \"ap_radio_obss_chan_util\":\n", "            promql = get_prom_marker_promql(marker, \"10m\", {\"site_id\": tenant_id})\n", "            config = ProphecyConfig(\n", "                query=promql,\n", "                multi_series=True,\n", "                trained=False,\n", "            )\n", "            print(\"update marker\", marker.name, \"with config\", config.kebab_dump())\n", "            await conn.execute(\n", "                \"\"\"\n", "                UPDATE jobs\n", "                SET config = $1\n", "                WHERE tenant_id = $2\n", "                  AND job_source_id = $3\n", "                  AND type = $4\n", "                \"\"\",\n", "                config.kebab_dump(),\n", "                tenant_id,\n", "                marker.name,\n", "                JobType.prophecy,\n", "            )\n", "    for marker in sks_markers:\n", "        if isinstance(marker, CustomMarker):\n", "            if not marker.hide:\n", "                await update_prophecy_sql_job(conn, tenant_id, marker)\n", "            for sub_marker in marker.sub_markers:\n", "                await update_prophecy_sql_job(conn, tenant_id, marker, sub_marker.name)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b54e6be9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-06-19 17:44:29\u001b[0m [\u001b[32m\u001b[1mdebug    \u001b[0m] \u001b[1mConnecting to database        \u001b[0m \u001b[36mpg_setting\u001b[0m=\u001b[35mPostgresSettings(host='localhost', port=34321, user='postgres', password='postgres', name='pythia')\u001b[0m\n"]}], "source": ["from database import get_db_connection_pool\n", "import pendulum\n", "from job.model import GreptimeProphecyConfig, JobType, SKSMcsNssConfig\n", "import orjson\n", "from asyncpg import create_pool\n", "\n", "pool = await get_db_connection_pool()\n", "\n", "tenant = \"84946\"\n", "\n", "async with pool.acquire() as conn:\n", "    await conn.set_type_codec(\n", "        \"jsonb\", encoder=lambda v: orjson.dumps(v).decode(), decoder=orjson.loads, schema=\"pg_catalog\"\n", "    )\n", "    await conn.execute(\n", "        \"\"\"\n", "        INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)\n", "        VALUES ($1, $2, $3, $4, $5, $6, $7)\n", "        \"\"\",\n", "        tenant,\n", "        \"mcs_nss_qoe\",\n", "        pendulum.now().naive(),\n", "        pendulum.now().naive(),\n", "        JobType.sks_mcs_nss,\n", "        2,\n", "        SKSMcsNssConfig().kebab_dump(),\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "74561c30d9b500e6", "metadata": {}, "outputs": [], "source": ["train(\n", "    config,\n", "    prophecy_id=marker.name,\n", "    tenant_id=\"sks\",\n", "    start=start.int_timestamp,\n", "    end=end.int_timestamp,\n", "    country=\"CN\",\n", "    subdivision=None,\n", "    resolution=300,\n", "    sql=sql,\n", "    multi_series=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ac215885981ac168", "metadata": {}, "outputs": [], "source": ["predict(config, tenant_id=\"332\", prophecy_id=marker.name, start=start.int_timestamp, end=end.int_timestamp,\n", "        resolution=300)"]}, {"cell_type": "code", "execution_count": 10, "id": "e306fa35", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'marker_registry' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[10]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mmarker_registry\u001b[49m\n", "\u001b[31mNameError\u001b[39m: name 'marker_registry' is not defined"]}], "source": ["marker_registry"]}, {"cell_type": "code", "execution_count": 7, "id": "98a4354b", "metadata": {}, "outputs": [], "source": ["from anomaly_detector.sks.qoe import marker_registry"]}, {"cell_type": "code", "execution_count": 4, "id": "63b6d269", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SubMarker(name='ap_radio_nss_symmetry'),\n", " SubMarker(name='ap_radio_mcs_symmetry'),\n", " PromMarker(name='ap_radio_vap_node_rx_mcs_bucket', tag=<Tag.THROUGHPUT: 'throughput'>, type=<MarkerType.GAUGE: 'gauge'>, omit_labels=['__name__', 'sta_mac', 'state', 'site_id'], qoe_score=False, qoes=['throughput_efficiency', 'symmetry_score'], hide=False),\n", " PromMarker(name='ap_radio_vap_node_tx_mcs_bucket', tag=<Tag.THROUGHPUT: 'throughput'>, type=<MarkerType.GAUGE: 'gauge'>, omit_labels=['__name__', 'sta_mac', 'state', 'site_id'], qoe_score=False, qoes=['throughput_efficiency', 'symmetry_score'], hide=False),\n", " PromMarker(name='ap_radio_vap_node_rx_nss_bucket', tag=<Tag.LINK: 'link'>, type=<MarkerType.GAUGE: 'gauge'>, omit_labels=['__name__', 'sta_mac', 'state', 'site_id'], qoe_score=False, qoes=['symmetry_score'], hide=False),\n", " PromMarker(name='ap_radio_vap_node_tx_nss_bucket', tag=<Tag.LINK: 'link'>, type=<MarkerType.GAUGE: 'gauge'>, omit_labels=['__name__', 'sta_mac', 'state', 'site_id'], qoe_score=False, qoes=['symmetry_score'], hide=False)]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "from anomaly_detector.sks.marker import Tag, CustomMarker, get_prom_marker_promql\n", "from anomaly_detector.sks.qoe import SksMarkerRegistry, sks_markers\n", "\n", "marker_registry = SksMarkerRegistry()\n", "for m in sks_markers:\n", "    marker_registry.register(m)\n", "\n", "marker_registry.qoe_related_markers(\"symmetry_score\")"]}, {"cell_type": "code", "execution_count": null, "id": "b1d7d7c7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pythia-BPx0uQVf-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}