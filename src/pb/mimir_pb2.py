# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mimir.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import gogo_pb2 as gogo__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bmimir.proto\x12\x08\x63ortexpb\x1a\ngogo.proto\"\xff\x01\n\x0cWriteRequest\x12\x44\n\ntimeseries\x18\x01 \x03(\x0b\x32\x14.cortexpb.TimeSeriesB\x1a\xc8\xde\x1f\x00\xda\xde\x1f\x12PreallocTimeseries\x12\x31\n\x06Source\x18\x02 \x01(\x0e\x32!.cortexpb.WriteRequest.SourceEnum\x12\x30\n\x08metadata\x18\x03 \x03(\x0b\x32\x18.cortexpb.MetricMetadataB\x04\xc8\xde\x1f\x01\x12#\n\x1askip_label_name_validation\x18\xe8\x07 \x01(\x08\"\x1f\n\nSourceEnum\x12\x07\n\x03\x41PI\x10\x00\x12\x08\n\x04RULE\x10\x01\"\x0f\n\rWriteResponse\"8\n\x11WriteErrorDetails\x12#\n\x05\x43\x61use\x18\x01 \x01(\x0e\x32\x14.cortexpb.ErrorCause\"\xcc\x01\n\nTimeSeries\x12\x39\n\x06labels\x18\x01 \x03(\x0b\x32\x13.cortexpb.LabelPairB\x14\xc8\xde\x1f\x00\xda\xde\x1f\x0cLabelAdapter\x12\'\n\x07samples\x18\x02 \x03(\x0b\x32\x10.cortexpb.SampleB\x04\xc8\xde\x1f\x00\x12+\n\texemplars\x18\x03 \x03(\x0b\x32\x12.cortexpb.ExemplarB\x04\xc8\xde\x1f\x00\x12-\n\nhistograms\x18\x04 \x03(\x0b\x32\x13.cortexpb.HistogramB\x04\xc8\xde\x1f\x00\"(\n\tLabelPair\x12\x0c\n\x04name\x18\x01 \x01(\x0c\x12\r\n\x05value\x18\x02 \x01(\x0c\"-\n\x06Sample\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\r\n\x05value\x18\x01 \x01(\x01\"\xf6\x01\n\x0eMetricMetadata\x12\x31\n\x04type\x18\x01 \x01(\x0e\x32#.cortexpb.MetricMetadata.MetricType\x12\x1a\n\x12metric_family_name\x18\x02 \x01(\t\x12\x0c\n\x04help\x18\x04 \x01(\t\x12\x0c\n\x04unit\x18\x05 \x01(\t\"y\n\nMetricType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07\x43OUNTER\x10\x01\x12\t\n\x05GAUGE\x10\x02\x12\r\n\tHISTOGRAM\x10\x03\x12\x12\n\x0eGAUGEHISTOGRAM\x10\x04\x12\x0b\n\x07SUMMARY\x10\x05\x12\x08\n\x04INFO\x10\x06\x12\x0c\n\x08STATESET\x10\x07\"C\n\x06Metric\x12\x39\n\x06labels\x18\x01 \x03(\x0b\x32\x13.cortexpb.LabelPairB\x14\xc8\xde\x1f\x00\xda\xde\x1f\x0cLabelAdapter\"j\n\x08\x45xemplar\x12\x39\n\x06labels\x18\x01 \x03(\x0b\x32\x13.cortexpb.LabelPairB\x14\xc8\xde\x1f\x00\xda\xde\x1f\x0cLabelAdapter\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\"\x87\x04\n\tHistogram\x12\x13\n\tcount_int\x18\x01 \x01(\x04H\x00\x12\x15\n\x0b\x63ount_float\x18\x02 \x01(\x01H\x00\x12\x0b\n\x03sum\x18\x03 \x01(\x01\x12\x0e\n\x06schema\x18\x04 \x01(\x11\x12\x16\n\x0ezero_threshold\x18\x05 \x01(\x01\x12\x18\n\x0ezero_count_int\x18\x06 \x01(\x04H\x01\x12\x1a\n\x10zero_count_float\x18\x07 \x01(\x01H\x01\x12\x32\n\x0enegative_spans\x18\x08 \x03(\x0b\x32\x14.cortexpb.BucketSpanB\x04\xc8\xde\x1f\x00\x12\x17\n\x0fnegative_deltas\x18\t \x03(\x12\x12\x17\n\x0fnegative_counts\x18\n \x03(\x01\x12\x32\n\x0epositive_spans\x18\x0b \x03(\x0b\x32\x14.cortexpb.BucketSpanB\x04\xc8\xde\x1f\x00\x12\x17\n\x0fpositive_deltas\x18\x0c \x03(\x12\x12\x17\n\x0fpositive_counts\x18\r \x03(\x01\x12\x31\n\nreset_hint\x18\x0e \x01(\x0e\x32\x1d.cortexpb.Histogram.ResetHint\x12\x11\n\ttimestamp\x18\x0f \x01(\x03\":\n\tResetHint\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03YES\x10\x01\x12\x06\n\x02NO\x10\x02\x12\t\n\x05GAUGE\x10\x03\x1a\x04\x88\xa3\x1e\x01\x42\x07\n\x05\x63ountB\x0c\n\nzero_count\"\xcd\x03\n\x0e\x46loatHistogram\x12\x61\n\x12\x63ounter_reset_hint\x18\x0e \x01(\rBE\xfa\xde\x1f\x41github.com/prometheus/prometheus/model/histogram.CounterResetHint\x12\x0e\n\x06schema\x18\x04 \x01(\x11\x12\x16\n\x0ezero_threshold\x18\x05 \x01(\x01\x12\x12\n\nzero_count\x18\x07 \x01(\x01\x12\r\n\x05\x63ount\x18\x02 \x01(\x01\x12\x0b\n\x03sum\x18\x03 \x01(\x01\x12\x32\n\x0epositive_spans\x18\x0b \x03(\x0b\x32\x14.cortexpb.BucketSpanB\x04\xc8\xde\x1f\x00\x12\x32\n\x0enegative_spans\x18\x08 \x03(\x0b\x32\x14.cortexpb.BucketSpanB\x04\xc8\xde\x1f\x00\x12\x18\n\x10positive_buckets\x18\r \x03(\x01\x12\x18\n\x10negative_buckets\x18\n \x03(\x01J\x04\x08\x01\x10\x02J\x04\x08\x06\x10\x07J\x04\x08\t\x10\nJ\x04\x08\x0c\x10\rJ\x04\x08\x0f\x10\x10R\tcount_intR\x0ezero_count_intR\x0fnegative_deltasR\x0fpositive_deltasR\ttimestamp\",\n\nBucketSpan\x12\x0e\n\x06offset\x18\x01 \x01(\x11\x12\x0e\n\x06length\x18\x02 \x01(\r\"]\n\x12\x46loatHistogramPair\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x31\n\thistogram\x18\x01 \x01(\x0b\x32\x18.cortexpb.FloatHistogramB\x04\xc8\xde\x1f\x01\"Y\n\x0fSampleHistogram\x12\r\n\x05\x63ount\x18\x01 \x01(\x01\x12\x0b\n\x03sum\x18\x02 \x01(\x01\x12*\n\x07\x62uckets\x18\x03 \x03(\x0b\x32\x19.cortexpb.HistogramBucket\"R\n\x0fHistogramBucket\x12\x12\n\nboundaries\x18\x01 \x01(\x05\x12\r\n\x05lower\x18\x02 \x01(\x01\x12\r\n\x05upper\x18\x03 \x01(\x01\x12\r\n\x05\x63ount\x18\x04 \x01(\x01\"V\n\x13SampleHistogramPair\x12\x11\n\ttimestamp\x18\x02 \x01(\x03\x12,\n\thistogram\x18\x01 \x01(\x0b\x32\x19.cortexpb.SampleHistogram\"\xff\x03\n\rQueryResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.cortexpb.QueryResponse.Status\x12\x35\n\nerror_type\x18\x02 \x01(\x0e\x32!.cortexpb.QueryResponse.ErrorType\x12\r\n\x05\x65rror\x18\x03 \x01(\t\x12&\n\x06string\x18\x04 \x01(\x0b\x32\x14.cortexpb.StringDataH\x00\x12&\n\x06vector\x18\x05 \x01(\x0b\x32\x14.cortexpb.VectorDataH\x00\x12&\n\x06scalar\x18\x06 \x01(\x0b\x32\x14.cortexpb.ScalarDataH\x00\x12&\n\x06matrix\x18\x07 \x01(\x0b\x32\x14.cortexpb.MatrixDataH\x00\x12\x10\n\x08warnings\x18\x08 \x03(\t\"&\n\x06Status\x12\t\n\x05\x45RROR\x10\x00\x12\x0b\n\x07SUCCESS\x10\x01\x1a\x04\x88\xa3\x1e\x01\"\x95\x01\n\tErrorType\x12\x08\n\x04NONE\x10\x00\x12\x0b\n\x07TIMEOUT\x10\x01\x12\x0c\n\x08\x43\x41NCELED\x10\x02\x12\r\n\tEXECUTION\x10\x03\x12\x0c\n\x08\x42\x41\x44_DATA\x10\x04\x12\x0c\n\x08INTERNAL\x10\x05\x12\x0f\n\x0bUNAVAILABLE\x10\x06\x12\r\n\tNOT_FOUND\x10\x07\x12\x12\n\x0eNOT_ACCEPTABLE\x10\x08\x1a\x04\x88\xa3\x1e\x01\x42\x06\n\x04\x64\x61ta\"1\n\nStringData\x12\r\n\x05value\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"p\n\nVectorData\x12-\n\x07samples\x18\x01 \x03(\x0b\x32\x16.cortexpb.VectorSampleB\x04\xc8\xde\x1f\x00\x12\x33\n\nhistograms\x18\x02 \x03(\x0b\x32\x19.cortexpb.VectorHistogramB\x04\xc8\xde\x1f\x00\"C\n\x0cVectorSample\x12\x0e\n\x06metric\x18\x01 \x03(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\"j\n\x0fVectorHistogram\x12\x0e\n\x06metric\x18\x01 \x03(\t\x12\x31\n\thistogram\x18\x02 \x01(\x0b\x32\x18.cortexpb.FloatHistogramB\x04\xc8\xde\x1f\x00\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\"1\n\nScalarData\x12\r\n\x05value\x18\x01 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\":\n\nMatrixData\x12,\n\x06series\x18\x01 \x03(\x0b\x32\x16.cortexpb.MatrixSeriesB\x04\xc8\xde\x1f\x00\"\x7f\n\x0cMatrixSeries\x12\x0e\n\x06metric\x18\x01 \x03(\t\x12\'\n\x07samples\x18\x02 \x03(\x0b\x32\x10.cortexpb.SampleB\x04\xc8\xde\x1f\x00\x12\x36\n\nhistograms\x18\x03 \x03(\x0b\x32\x1c.cortexpb.FloatHistogramPairB\x04\xc8\xde\x1f\x00*\xd9\x01\n\nErrorCause\x12\x11\n\rUNKNOWN_CAUSE\x10\x00\x12\x1a\n\x16REPLICAS_DID_NOT_MATCH\x10\x01\x12\x15\n\x11TOO_MANY_CLUSTERS\x10\x02\x12\x0c\n\x08\x42\x41\x44_DATA\x10\x03\x12\x1a\n\x16INGESTION_RATE_LIMITED\x10\x04\x12\x18\n\x14REQUEST_RATE_LIMITED\x10\x05\x12\x12\n\x0eINSTANCE_LIMIT\x10\x06\x12\x17\n\x13SERVICE_UNAVAILABLE\x10\x07\x12\x14\n\x10TSDB_UNAVAILABLE\x10\x08\x42\x11Z\x07mimirpb\xc8\xe2\x1e\x01\xd0\xe2\x1e\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mimir_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\007mimirpb\310\342\036\001\320\342\036\001'
  _WRITEREQUEST.fields_by_name['timeseries']._options = None
  _WRITEREQUEST.fields_by_name['timeseries']._serialized_options = b'\310\336\037\000\332\336\037\022PreallocTimeseries'
  _WRITEREQUEST.fields_by_name['metadata']._options = None
  _WRITEREQUEST.fields_by_name['metadata']._serialized_options = b'\310\336\037\001'
  _TIMESERIES.fields_by_name['labels']._options = None
  _TIMESERIES.fields_by_name['labels']._serialized_options = b'\310\336\037\000\332\336\037\014LabelAdapter'
  _TIMESERIES.fields_by_name['samples']._options = None
  _TIMESERIES.fields_by_name['samples']._serialized_options = b'\310\336\037\000'
  _TIMESERIES.fields_by_name['exemplars']._options = None
  _TIMESERIES.fields_by_name['exemplars']._serialized_options = b'\310\336\037\000'
  _TIMESERIES.fields_by_name['histograms']._options = None
  _TIMESERIES.fields_by_name['histograms']._serialized_options = b'\310\336\037\000'
  _METRIC.fields_by_name['labels']._options = None
  _METRIC.fields_by_name['labels']._serialized_options = b'\310\336\037\000\332\336\037\014LabelAdapter'
  _EXEMPLAR.fields_by_name['labels']._options = None
  _EXEMPLAR.fields_by_name['labels']._serialized_options = b'\310\336\037\000\332\336\037\014LabelAdapter'
  _HISTOGRAM_RESETHINT._options = None
  _HISTOGRAM_RESETHINT._serialized_options = b'\210\243\036\001'
  _HISTOGRAM.fields_by_name['negative_spans']._options = None
  _HISTOGRAM.fields_by_name['negative_spans']._serialized_options = b'\310\336\037\000'
  _HISTOGRAM.fields_by_name['positive_spans']._options = None
  _HISTOGRAM.fields_by_name['positive_spans']._serialized_options = b'\310\336\037\000'
  _FLOATHISTOGRAM.fields_by_name['counter_reset_hint']._options = None
  _FLOATHISTOGRAM.fields_by_name['counter_reset_hint']._serialized_options = b'\372\336\037Agithub.com/prometheus/prometheus/model/histogram.CounterResetHint'
  _FLOATHISTOGRAM.fields_by_name['positive_spans']._options = None
  _FLOATHISTOGRAM.fields_by_name['positive_spans']._serialized_options = b'\310\336\037\000'
  _FLOATHISTOGRAM.fields_by_name['negative_spans']._options = None
  _FLOATHISTOGRAM.fields_by_name['negative_spans']._serialized_options = b'\310\336\037\000'
  _FLOATHISTOGRAMPAIR.fields_by_name['histogram']._options = None
  _FLOATHISTOGRAMPAIR.fields_by_name['histogram']._serialized_options = b'\310\336\037\001'
  _QUERYRESPONSE_STATUS._options = None
  _QUERYRESPONSE_STATUS._serialized_options = b'\210\243\036\001'
  _QUERYRESPONSE_ERRORTYPE._options = None
  _QUERYRESPONSE_ERRORTYPE._serialized_options = b'\210\243\036\001'
  _VECTORDATA.fields_by_name['samples']._options = None
  _VECTORDATA.fields_by_name['samples']._serialized_options = b'\310\336\037\000'
  _VECTORDATA.fields_by_name['histograms']._options = None
  _VECTORDATA.fields_by_name['histograms']._serialized_options = b'\310\336\037\000'
  _VECTORHISTOGRAM.fields_by_name['histogram']._options = None
  _VECTORHISTOGRAM.fields_by_name['histogram']._serialized_options = b'\310\336\037\000'
  _MATRIXDATA.fields_by_name['series']._options = None
  _MATRIXDATA.fields_by_name['series']._serialized_options = b'\310\336\037\000'
  _MATRIXSERIES.fields_by_name['samples']._options = None
  _MATRIXSERIES.fields_by_name['samples']._serialized_options = b'\310\336\037\000'
  _MATRIXSERIES.fields_by_name['histograms']._options = None
  _MATRIXSERIES.fields_by_name['histograms']._serialized_options = b'\310\336\037\000'
  _ERRORCAUSE._serialized_start=3579
  _ERRORCAUSE._serialized_end=3796
  _WRITEREQUEST._serialized_start=38
  _WRITEREQUEST._serialized_end=293
  _WRITEREQUEST_SOURCEENUM._serialized_start=262
  _WRITEREQUEST_SOURCEENUM._serialized_end=293
  _WRITERESPONSE._serialized_start=295
  _WRITERESPONSE._serialized_end=310
  _WRITEERRORDETAILS._serialized_start=312
  _WRITEERRORDETAILS._serialized_end=368
  _TIMESERIES._serialized_start=371
  _TIMESERIES._serialized_end=575
  _LABELPAIR._serialized_start=577
  _LABELPAIR._serialized_end=617
  _SAMPLE._serialized_start=619
  _SAMPLE._serialized_end=664
  _METRICMETADATA._serialized_start=667
  _METRICMETADATA._serialized_end=913
  _METRICMETADATA_METRICTYPE._serialized_start=792
  _METRICMETADATA_METRICTYPE._serialized_end=913
  _METRIC._serialized_start=915
  _METRIC._serialized_end=982
  _EXEMPLAR._serialized_start=984
  _EXEMPLAR._serialized_end=1090
  _HISTOGRAM._serialized_start=1093
  _HISTOGRAM._serialized_end=1612
  _HISTOGRAM_RESETHINT._serialized_start=1531
  _HISTOGRAM_RESETHINT._serialized_end=1589
  _FLOATHISTOGRAM._serialized_start=1615
  _FLOATHISTOGRAM._serialized_end=2076
  _BUCKETSPAN._serialized_start=2078
  _BUCKETSPAN._serialized_end=2122
  _FLOATHISTOGRAMPAIR._serialized_start=2124
  _FLOATHISTOGRAMPAIR._serialized_end=2217
  _SAMPLEHISTOGRAM._serialized_start=2219
  _SAMPLEHISTOGRAM._serialized_end=2308
  _HISTOGRAMBUCKET._serialized_start=2310
  _HISTOGRAMBUCKET._serialized_end=2392
  _SAMPLEHISTOGRAMPAIR._serialized_start=2394
  _SAMPLEHISTOGRAMPAIR._serialized_end=2480
  _QUERYRESPONSE._serialized_start=2483
  _QUERYRESPONSE._serialized_end=2994
  _QUERYRESPONSE_STATUS._serialized_start=2796
  _QUERYRESPONSE_STATUS._serialized_end=2834
  _QUERYRESPONSE_ERRORTYPE._serialized_start=2837
  _QUERYRESPONSE_ERRORTYPE._serialized_end=2986
  _STRINGDATA._serialized_start=2996
  _STRINGDATA._serialized_end=3045
  _VECTORDATA._serialized_start=3047
  _VECTORDATA._serialized_end=3159
  _VECTORSAMPLE._serialized_start=3161
  _VECTORSAMPLE._serialized_end=3228
  _VECTORHISTOGRAM._serialized_start=3230
  _VECTORHISTOGRAM._serialized_end=3336
  _SCALARDATA._serialized_start=3338
  _SCALARDATA._serialized_end=3387
  _MATRIXDATA._serialized_start=3389
  _MATRIXDATA._serialized_end=3447
  _MATRIXSERIES._serialized_start=3449
  _MATRIXSERIES._serialized_end=3576
# @@protoc_insertion_point(module_scope)
