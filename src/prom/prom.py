from collections.abc import Iterator
from datetime import datetime, timezone
from typing import NamedTuple

import httpx
import pendulum
from pendulum import DateTime

from utils import iso_format


class PrometheusDataPoint(NamedTuple):
    timestamp: datetime
    value: int


class PrometheusClient:
    def __init__(
        self,
        mimir_enabled: bool,
        mimir_proxy_url: str | None = None,
        mimir_frontend_url: str | None = None,
        headers: dict | None = None,
        uri: str | None = None,
    ):
        self.mimir_enabled = mimir_enabled
        self.override_uri = uri
        if not mimir_enabled and mimir_proxy_url:
            self.client = httpx.Client(base_url=f"{mimir_proxy_url}", headers=headers, timeout=60)
        elif mimir_frontend_url:
            self.client = httpx.Client(
                base_url=f"{mimir_frontend_url}/prometheus/api/v1",
                headers=headers,
                timeout=60,
            )
        else:
            raise ValueError("No Prometheus URL provided")

    def query(
        self,
        query: str,
        time: DateTime,
        *,
        headers=None,
    ):
        return self.client.get(
            self.override_uri or "/query",
            params={
                "time": iso_format(time),
                "query": query,
            },
            headers=headers,
        )

    def query_range(
        self,
        start: DateTime | int,
        end: DateTime | int,
        step: str,
        query: str,
        *,
        headers=None,
    ):
        return self.client.get(
            self.override_uri or ("/query_range" if self.mimir_enabled else "/query"),
            params={
                "start": iso_format(start) if isinstance(start, DateTime) else start,
                "end": iso_format(end) if isinstance(end, DateTime) else end,
                "step": step,
                "query": query,
            },
            headers=headers,
        ).json()

    def query_range_matrix(
        self,
        start: datetime,
        end: datetime,
        step: str,
        query: str,
        *,
        headers=None,
    ) -> Iterator[tuple[Iterator[PrometheusDataPoint], dict]]:
        # Convert datetime to DateTime if needed
        start_dt = pendulum.instance(start) if isinstance(start, datetime) else start
        end_dt = pendulum.instance(end) if isinstance(end, datetime) else end
        res = self.query_range(start_dt, end_dt, step, query, headers=headers)
        result = res.get("data", {}).get("result", [])
        for r in result:
            ordered = sorted(r["values"], key=lambda x: x[0])
            yield (
                (
                    PrometheusDataPoint(
                        timestamp=datetime.fromtimestamp(ts, tz=timezone.utc),
                        value=int(float(v)),
                    )
                    for ts, v in ordered
                ),
                r["metric"],
            )
