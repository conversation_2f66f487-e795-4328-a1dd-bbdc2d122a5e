def build_log_rate_query(metric_name: str, flow_id: str, step: str):
    metric = f'{{__name__="{metric_name}_counter",_reserved_flow_id="{flow_id}"}}'
    return f"sum(increase({metric}[{step}]))"


def build_flow_bytes_query(flow_id: str, step: str):
    metric = (
        f'{{__name__="vector_component_sent_event_bytes_total",component_type="influxdb_logs",flow_id="{flow_id}"}}'
    )
    return f"sum(increase({metric}[{step}]))"
