import math
from dataclasses import dataclass

import httpx
from snappy import snappy

from custom_exceptions import ServiceError
from pb import mimir_pb2


@dataclass
class MetricPoint:
    metric_name: str
    timestamp: int  # unix timestamp, seconds
    value: float
    labels: dict[str, str]


class PrometheusRemoteWriter:
    def __init__(
        self,
        base_url: str,
        tenant_id: str,
        *,
        remote_write_endpoint: str | None = None,
        params: dict | None = None,
    ):
        self.tenant_id = tenant_id
        self.base_url = base_url
        self.remote_write_endpoint = remote_write_endpoint or "/v1/prometheus/write"
        self.params = params
        self.client = httpx.Client(
            base_url=self.base_url,
            headers={
                "X-Scope-OrgID": tenant_id,
                "X-Prometheus-Remote-Write-Version": "0.1.0",
                "Content-Encoding": "snappy",
                "Content-Type": "application/x-protobuf",
            },
            params=params,
            timeout=60,
        )

    def write_metric_points(self, metric_points: list[MetricPoint]):
        sorted_points = sorted(metric_points, key=lambda x: x.timestamp)
        write_request = mimir_pb2.WriteRequest()
        for point in filter(lambda x: x.value is not None and not math.isnan(x.value), sorted_points):
            labels = [{"name": str(k).encode(), "value": str(v).encode()} for k, v in point.labels.items()]
            labels.append({"name": b"__name__", "value": point.metric_name.encode()})
            write_request.timeseries.add(
                labels=sorted(labels, key=lambda x: x["name"]),
                samples=[{"timestamp_ms": point.timestamp * 1000, "value": point.value}],
            )
        return self.push(write_request)

    def push(self, write_request):
        binary = write_request.SerializeToString()
        data_compressed = snappy.compress(binary)
        try:
            return self.client.post(self.remote_write_endpoint, data=data_compressed)
        except Exception as e:
            raise ServiceError("Failed to write metrics") from e

    def _generate_insert_sqls(self, points: list[MetricPoint]):
        statements = []
        for point in points:
            if point.value != 0:
                continue
            columns = ", ".join([*list(point.labels.keys()), "greptime_timestamp", "greptime_value"])
            values = ", ".join(
                [f"'{v}'" for v in point.labels.values()] + [str(int(point.timestamp * 1000)), str(point.value)],
            )
            statements.append(f"INSERT INTO '{point.metric_name}' ({columns}) VALUES ({values});")
        return statements
