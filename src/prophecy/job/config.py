from prophecy.job.option import ProphecyServiceConfig

service_config: ProphecyServiceConfig = ProphecyServiceConfig(
    mimir_enabled=True,
    is_sks_env=False,
)


def greptime_config():
    return {
        "db": service_config.greptime_db,
        "url": service_config.greptime_url,
    }


def hosted_s3_config():
    return {
        "bucket": service_config.hosted_s3_bucket,
        "url": service_config.hosted_s3_url,
        "user": service_config.hosted_s3_user,
        "pass": service_config.hosted_s3_pass,
    }


def init_config(conf: ProphecyServiceConfig):
    # FIXME: remove this global variable
    global service_config  # noqa: PLW0603
    service_config = conf
