from functools import cache

import boto3

from .config import hosted_s3_config


@cache
def s3_client():
    return boto3.resource(
        "s3",
        endpoint_url=hosted_s3_config()["url"],
        aws_access_key_id=hosted_s3_config()["user"],
        aws_secret_access_key=hosted_s3_config()["pass"],
        aws_session_token=None,
        config=boto3.session.Config(signature_version="s3v4"),
        verify=False,
    )


def put_string_object(key, body):
    s3_client().Bucket(hosted_s3_config()["bucket"]).put_object(Key=key, Body=body)


def load_string(key):
    response = s3_client().Object(hosted_s3_config()["bucket"], key).get()
    return response["Body"].read()
