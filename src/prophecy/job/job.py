from typing import cast

import pendulum
import structlog
from asyncpg import Pool
from pendulum import DateTime, Duration, duration

from config import Settings
from job.model import BaseProphecyConfig, GreptimeProphecyConfig, JobModel, UTCISODatetime
from job.scheduler import BaseJob
from prophecy.job.option import ProphecyJobOptions, ProphecyServiceConfig
from services import ServicesRegistry
from utils import get_module

RESOLUTION = 60


class ProphecyTrainingJob(BaseJob):
    def __init__(self, job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
        self.job_record = job_record
        self.pool = pool
        self.settings = settings
        self.services_registry = services_registry
        self.logger = structlog.get_logger("ProphecyTrainingJob", id=self.job_record.id)

        self.train_func = get_module("prophecy.job.prophecy", "train")

        if isinstance(self.job_record.config, BaseProphecyConfig):
            self.job_config = self.job_record.config
        else:
            raise TypeError(
                f"Invalid job config type: {type(self.job_config)}. Expected BaseProphecyConfig or its subclass.",
            )

    def _retrain_datetime(self, predicted: DateTime, next_run: DateTime):
        merged_dt = pendulum.datetime(
            year=next_run.year,
            month=next_run.month,
            day=next_run.day,
            hour=predicted.hour,
            minute=predicted.minute,
            second=predicted.second,
            tz="UTC",
        )
        if merged_dt > next_run:
            return merged_dt.add(days=-1)
        return merged_dt

    def _training_datetime(self):
        if self.job_config.predicted and self.job_record.next_run:  # retrain
            return self._retrain_datetime(self.job_config.predicted, self.job_record.next_run)
        # initial training
        created = cast(UTCISODatetime, self.job_record.created)
        return created.add(seconds=-300)

    def maximum_duration(self) -> Duration:
        return duration(hours=1)

    def invalidate_cache(self):
        self.services_registry.service("s3").invalidate_cache()
        self.services_registry.service("greptime").invalidate_cache()
        self.services_registry.service("mimir_proxy").invalidate_cache()
        self.services_registry.service("mimir_frontend").invalidate_cache()

    async def prepare_args(self):
        # Create service config
        service_config = ProphecyServiceConfig(
            hosted_s3_bucket=self.settings.s3.bucket,
            hosted_s3_user=self.settings.s3.username,
            hosted_s3_pass=self.settings.s3.password,
            hosted_s3_url=self.services_registry.service("s3").url(),
            greptime_sks_db=self.settings.greptime.sks_db,
            greptime_db=self.settings.greptime.db,
            greptime_url=self.services_registry.service("greptime").url(),
            mimir_enabled=self.settings.mimir_enabled,
            is_sks_env=self.settings.is_sks_env,
        )

        if self.settings.mimir_enabled:
            service_config.mimir_frontend = self.services_registry.service("mimir_frontend").url()
        else:
            service_config.mimir_proxy = self.services_registry.service("mimir_proxy").url()

        # Create job options
        start_time = self._training_datetime().add(days=-14)
        end_time = self._training_datetime()

        resolution = 600 if self.settings.is_sks_env else RESOLUTION

        if isinstance(self.job_config, GreptimeProphecyConfig):
            options = ProphecyJobOptions(
                start=start_time,
                end=end_time,
                tenant_id=str(self.job_record.tenant_id),
                prophecy_id=str(self.job_record.job_source_id),
                resolution=resolution,
                multi_series=self.job_config.multi_series,
                sql=self.job_config.sql,
                timestamp_column=self.job_config.timestamp_column,
                value_column=self.job_config.value_column,
                extra_filters=self.job_config.extra_filters,
                country="JP",
                subdivision=None,
            )
        else:
            options = ProphecyJobOptions(
                start=start_time,
                end=end_time,
                tenant_id=str(self.job_record.tenant_id),
                prophecy_id=str(self.job_record.job_source_id),
                resolution=resolution,
                multi_series=self.job_config.multi_series,
                promql_query=self.job_config.query,
                timestamp_column="timestamp",
                value_column="value",
                country="JP",
                subdivision=None,
            )

        return (service_config, options)

    def run(self):
        return self.train_func

    async def on_success(self):
        self.logger.info("Training completed")
        next_run = cast(UTCISODatetime, self.job_record.next_run)
        config: dict = {"trained": True}
        if self.job_config.predicted and self.job_record.next_run:
            # retrain completed, reset next run to the retraining datetime
            next_run = self._retrain_datetime(self.job_config.predicted, self.job_record.next_run)
            config["predicted"] = next_run.to_iso8601_string()
        async with self.pool.acquire() as conn:
            await conn.execute(
                """
UPDATE jobs SET status = 'pending', attempts = 0, errors = '[]', run_started = NULL, config = config || $2, next_run = $3, cost = 10
WHERE id = $1
                """,
                self.job_record.id,
                config,
                next_run.naive(),
            )


class ProphecyPredictionJob(BaseJob):
    def __init__(
        self,
        job_record: JobModel,
        settings: Settings,
        services_registry: ServicesRegistry,
        pool: Pool,
    ):
        self.job_record = job_record
        self.pool = pool
        self.settings = settings
        self.services_registry = services_registry
        self.logger = structlog.get_logger("ProphecyPredictionJob", id=self.job_record.id)
        self.predict_func = get_module("prophecy.job.prophecy", "predict")
        if isinstance(self.job_record.config, BaseProphecyConfig):
            self.job_config = self.job_record.config
        else:
            raise TypeError(
                f"Invalid job config type: {type(self.job_config)}. Expected BaseProphecyConfig or its subclass.",
            )

    def invalidate_cache(self):
        self.services_registry.service("s3").invalidate_cache()
        self.services_registry.service("greptime").invalidate_cache()
        self.services_registry.service("mimir_proxy").invalidate_cache()
        self.services_registry.service("mimir_frontend").invalidate_cache()

    def _predict_start(self):
        if self.job_config.predicted is not None:
            return self.job_config.predicted
        return cast(UTCISODatetime, self.job_record.created)

    def maximum_duration(self) -> Duration:
        return duration(minutes=30)

    async def prepare_args(self):
        # Create service config
        service_config = ProphecyServiceConfig(
            hosted_s3_bucket=self.settings.s3.bucket,
            hosted_s3_user=self.settings.s3.username,
            hosted_s3_pass=self.settings.s3.password,
            hosted_s3_url=self.services_registry.service("s3").url(),
            greptime_db=self.settings.greptime.db,
            greptime_sks_db=self.settings.greptime.sks_db,
            greptime_url=self.services_registry.service("greptime").url(),
            mimir_enabled=self.settings.mimir_enabled,
            is_sks_env=self.settings.is_sks_env,
        )

        # Create job options for prediction
        options = ProphecyJobOptions(
            start=self._predict_start(),
            end=self._predict_start().add(days=7),
            tenant_id=str(self.job_record.tenant_id),
            prophecy_id=str(self.job_record.job_source_id),
            resolution=RESOLUTION,
            multi_series=self.job_config.multi_series,
            country="JP",
            subdivision=None,
        )

        return (service_config, options)

    def run(self, *_args, **_kwargs):
        return self.predict_func

    async def on_success(self):
        self.logger.info("Prediction completed")
        async with self.pool.acquire() as conn:
            await conn.execute(
                """
                    UPDATE jobs
                    SET status = 'pending', attempts = 0, errors = '[]', run_started = NULL, config = config || $2, next_run = $3
                    WHERE id = $1
                """,
                self.job_record.id,
                {"predicted": self._predict_start().add(days=7).to_iso8601_string()},
                self._predict_start().add(days=6).naive(),
            )


def prophecy_job_factory(job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
    if not isinstance(job_record.config, BaseProphecyConfig):
        raise TypeError(
            f"Invalid job config type: {type(job_record.config)}. Expected BaseProphecyConfig or its subclass.",
        )
    if job_record.config.trained:
        return ProphecyPredictionJob(job_record, settings, services_registry, pool)
    return ProphecyTrainingJob(job_record, settings, services_registry, pool)
