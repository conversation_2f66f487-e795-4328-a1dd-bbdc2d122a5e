from dataclasses import dataclass

from pendulum import DateTime


@dataclass
class ProphecyServiceConfig:
    mimir_enabled: bool
    is_sks_env: bool = False
    greptime_sks_db: str | None = None
    mimir_proxy: str | None = None
    mimir_frontend: str | None = None
    greptime_db: str | None = None
    greptime_url: str | None = None
    hosted_s3_url: str | None = None
    hosted_s3_user: str | None = None
    hosted_s3_pass: str | None = None
    hosted_s3_bucket: str | None = None


@dataclass
class ProphecyJobOptions:
    start: DateTime
    end: DateTime
    tenant_id: str
    prophecy_id: str
    multi_series: bool
    resolution: int = 60
    # promql prophecy
    promql_query: str | None = None
    exclude_labels: list[str] | None = None
    # sql prophecy
    sql: str | None = None
    timestamp_column: str = "greptime_timestamp"
    value_column: str = "greptime_value"
    extra_filters: dict[str, str] | None = None
    # holiday options
    country: str = "JP"
    subdivision: str | None = None
