import math

import numpy as np
import pandas as pd
import structlog

from prom.prom import PrometheusClient
from utils import prom_result_to_panda_df

logger = structlog.get_logger(__name__)

MIMIR_CONFIG_MAX_POINTS = 11000


def batch_series_query(
    prometheus_client: PrometheusClient,
    query: str,
    start: int,
    end: int,
    resolution: int,
    multi_series: bool = False,
):
    windows = generate_even_windows(start, end, resolution, MIMIR_CONFIG_MAX_POINTS)
    logger.info(f"querying mimir with {len(windows)} requests")
    result_df = []
    for t in windows:
        df = series_query(prometheus_client, query, t[0], t[1], resolution, multi_series=multi_series)
        if not df.empty:
            result_df.append(df)

    if not result_df:
        raise ValueError("No data points were returned")

    return pd.concat(result_df)


def generate_even_windows(start, end, resolution, max_window):
    """Generate [start,stop][] windows that evenly distribute points up to a maximum point count"""
    duration = end - start
    points = duration / resolution
    count = math.ceil(points / max_window)
    ts = np.linspace(start, end, count + 1)
    segments = [(ts[i], ts[i + 1]) for i in range(len(ts) - 1)]
    return np.floor(segments)


def series_query(
    prometheus_client: PrometheusClient,
    query: str,
    start: int,
    end: int,
    resolution: int,
    multi_series: bool = False,
):
    resp = prometheus_client.query_range(
        query=query,
        start=start,
        end=end,
        step=f"{resolution}s",
    )
    if len(resp.get("data", {}).get("result", [])) > 1 and not multi_series:
        raise ValueError("Error multiple series were returned")
    if len(resp.get("data", {}).get("result", [])) == 0:
        return pd.DataFrame()

    return prom_result_to_panda_df(
        resp,
        column_name="value",
        multi_series=multi_series,
    )
