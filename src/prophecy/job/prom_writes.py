import requests
import structlog
from snappy import snappy

from pb import mimir_pb2
from prophecy.utils import prophecy_table_name

from .config import greptime_config

logger = structlog.get_logger(__name__)


def create_sample(s):
    return mimir_pb2.Sample(timestamp_ms=round(s[0].timestamp() * 1000), value=s[1])


def create_series(s):
    return list(map(create_sample, s))


def create_label_pairs(labels):
    return list(map(create_label_pair, labels.items()))


def create_label_pair(v):
    return mimir_pb2.LabelPair(name=v[0].encode(), value=v[1].encode())


def create_time_series(name, series, labels: dict | None = None):
    label_paris = [mimir_pb2.LabelPair(name=b"__name__", value=name.encode())]
    if labels:
        for key, value in labels.items():
            label_paris.append(mimir_pb2.LabelPair(name=key.encode(), value=value.encode()))
    return mimir_pb2.TimeSeries(labels=label_paris, samples=create_series(series))


def push_series(timeseries):
    metric_data = mimir_pb2.MetricMetadata(type=3, metric_family_name="metric-sample")

    write_result = mimir_pb2.WriteRequest(timeseries=timeseries, metadata=[metric_data])
    write_result_binary = write_result.SerializeToString()
    data_compressed = snappy.compress(write_result_binary)

    return requests.post(
        url=f"{greptime_config()['url']}/v1/prometheus/write?db={greptime_config()['db']}",
        headers={
            "X-Prometheus-Remote-Write-Version": "0.1.0",
            "Content-Encoding": "snappy",
            "Content-Type": "application/x-protobuf",
        },
        data=data_compressed,
        verify=False,
        timeout=60,
    )


def push_prediction(tenant_id, prophecy_id, df, deviation, labels: dict | None = None):
    df["stddev"] = deviation
    table_types = ["yhat", "stddev"]
    series = [
        create_time_series(
            prophecy_table_name(tenant_id, prophecy_id, table),
            df[["ds", table]].values,
            labels,
        )
        for table in table_types
    ]
    response = push_series(series)
    logger.info("Prophecy write complete.")
    return response
