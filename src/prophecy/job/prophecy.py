import datetime
import json
from math import ceil
from statistics import median, stdev
from time import time
from typing import Any, cast

import holidays
import pandas as pd
import pendulum
import prophet
import prophet.serialize as ps
import pytz
import requests
import structlog

from custom_exceptions import RecoverableError, ServiceError
from greptime import SyncGreptimeClient, greptime_timestamp_sql
from prom.prom import PrometheusClient
from prophecy.job.option import ProphecyJobOptions

from . import hosted_s3, prom_reads, prom_writes
from .config import ProphecyServiceConfig, init_config

logger = structlog.get_logger(__name__)


def prophecy_model_key(tenant_id, prophecy_id):
    return f"/pythia/prophecy/t_{tenant_id}_{prophecy_id}.json"


def forecast_model(m: prophet.Prophet, resolution: int, start: int, end: int):
    duration = end - start
    period = ceil(duration / resolution)
    freq = f"{resolution}s"
    dates = pd.date_range(
        start=pd.Timestamp(start, unit="s"),
        periods=period + 1,
        freq=freq,  # An extra in case we include start
    )

    future = pd.DataFrame({"ds": dates})
    return m.predict(future)


def forecast_model_duration(m: prophet.Prophet, resolution: int, start: int, end: int):
    duration = end - start
    period = ceil(duration / resolution)
    freq = f"{resolution}S"
    future = m.make_future_dataframe(periods=period, freq=freq)
    return m.predict(future)


def trim_forecast(df, start, end) -> pd.DataFrame:
    s = pd.Timestamp(start, unit="s")
    e = pd.Timestamp(end, unit="s")
    return df[(df["ds"] >= s) & (df["ds"] <= e)]


class BaseProphecyModel:
    def __init__(self, tenant_id: str, prophecy_id: str):
        self.tenant_id = tenant_id
        self.prophecy_id = prophecy_id

    def save_to_s3(self):
        hosted_s3.put_string_object(prophecy_model_key(self.tenant_id, self.prophecy_id), self.serialize())

    def serialize(self):
        raise NotImplementedError("Subclasses must implement this method")


class ProphecyModel(BaseProphecyModel):
    def __init__(self, tenant_id, prophecy_id: str, model: prophet.Prophet, deviation: float):
        super().__init__(tenant_id, prophecy_id)
        self.prophecy_id = prophecy_id
        self.deviation = deviation
        self.model = model

    @staticmethod
    def deserialize(tenant_id, prophecy_id: str, data: dict[str, Any]):
        deviation = data["deviation"]
        del data["deviation"]
        prophet_model = ps.model_from_dict(data)
        return ProphecyModel(tenant_id, prophecy_id, prophet_model, deviation)

    def serialize(self):
        model_data = ps.model_to_dict(self.model)
        model_data["deviation"] = self.deviation
        return json.dumps(model_data)

    def predict(self, start: int, end: int, resolution: int):
        data = forecast_model(self.model, resolution, start, end)
        return trim_forecast(data, start, end)


class MultiSeriesProphecyModel(BaseProphecyModel):
    def __init__(self, tenant_id: str, prophecy_id: str, models: list[ProphecyModel], labels: list[dict[str, Any]]):
        super().__init__(tenant_id, prophecy_id)
        self.prophecy_id = prophecy_id
        self.labels = labels
        self.models = models

    @staticmethod
    def deserialize(tenant_id: str, prophecy_id: str, data: dict[str, Any]):
        models = [ProphecyModel.deserialize(tenant_id, prophecy_id, model) for model in data["models"]]
        labels = data["labels"]
        return MultiSeriesProphecyModel(tenant_id, prophecy_id, models, labels)

    def serialize(self):
        model_data = [json.loads(model.serialize()) for model in self.models]
        data = {
            "models": model_data,
            "labels": self.labels,
            "multi_series": True,
        }
        return json.dumps(data)


def load_prophecy_model(tenant_id: str, prophecy_id: str) -> ProphecyModel | MultiSeriesProphecyModel:
    prophecy_model_data = json.loads(hosted_s3.load_string(prophecy_model_key(tenant_id, prophecy_id)))
    if prophecy_model_data.get("multi_series", False):
        return MultiSeriesProphecyModel.deserialize(tenant_id, prophecy_id, prophecy_model_data)
    return ProphecyModel.deserialize(tenant_id, prophecy_id, prophecy_model_data)


def create_df(series):
    df = pd.DataFrame(series, columns=["ds", "y"])  # map into DataFrame
    df["ds"] = df["ds"].apply(lambda ts: pd.Timestamp(ts, unit="s"))
    return df


def lookup_training_holidays(start, end, country, subdiv):
    tz = pytz.timezone("UTC")
    start_year = datetime.datetime.fromtimestamp(start, tz).year
    end_year = datetime.datetime.fromtimestamp(end, tz).year
    training_years = [str(x) for x in range(start_year, end_year + 1)]

    country_holidays = holidays.country_holidays(country, subdiv)
    country_holidays.update(training_years)

    pd.DataFrame({"holiday": "country_holiday", "ds": pd.to_datetime(list(country_holidays.keys()))})


def create_model(df, start, end, country, subdivision):
    model = prophet.Prophet(
        growth="flat",  # disable trends
        interval_width=0.8,
        changepoint_prior_scale=0.01,
        n_changepoints=0,
        yearly_seasonality="auto",
        weekly_seasonality="auto",
        daily_seasonality="auto",
        holidays=lookup_training_holidays(start, end, country, subdivision),
    )  # See https://github.com/facebook/prophet/blob/main/python/prophet/forecaster.py#L31 for param explanation

    model.fit(df)
    return model


def prophecy_model_predict(prophecy_model: ProphecyModel, start, end, resolution, labels: dict | None = None):
    values = prophecy_model.predict(start, end, resolution)
    response = prom_writes.push_prediction(
        prophecy_model.tenant_id,
        prophecy_model.prophecy_id,
        values,
        prophecy_model.deviation,
        labels,
    )
    status = response.status_code
    if status >= requests.codes.INTERNAL_SERVER_ERROR:
        msg = f"Failed to write prediction data: {response.text}. Status code: {status}"
        raise RecoverableError(msg)
    if status >= requests.codes.BAD_REQUEST:
        msg = f"Failed to write prediction data: {response.text}. Status code: {status}"
        raise ServiceError(msg)


def predict_multi_series_model(prophecy_model: MultiSeriesProphecyModel, start, end, resolution):
    for label, model in zip(prophecy_model.labels, prophecy_model.models, strict=False):
        prophecy_model_predict(model, start, end, resolution, label)


def predict(service_config: ProphecyServiceConfig, options: ProphecyJobOptions):
    init_config(service_config)
    logger.debug("Starting prophecy prediction")
    prophecy_model = load_prophecy_model(options.tenant_id, options.prophecy_id)
    forecast_start_time = time()
    if isinstance(prophecy_model, MultiSeriesProphecyModel):
        predict_multi_series_model(
            prophecy_model,
            options.start.int_timestamp,
            options.end.int_timestamp,
            options.resolution,
        )
    else:
        prophecy_model_predict(
            prophecy_model,
            options.start.int_timestamp,
            options.end.int_timestamp,
            options.resolution,
        )
    logger.debug("Time taken to forecast: %ss" % (time() - forecast_start_time))


def create_train_df(df: pd.DataFrame, timestamp_column, value_column):
    train_df = df[[timestamp_column, value_column]].copy()
    train_df["ds"] = train_df[timestamp_column].apply(lambda ts: pd.Timestamp(ts, unit="ms"))
    train_df["y"] = train_df[value_column]
    return train_df.fillna(0)


def create_df_from_sql(
    greptime_client: SyncGreptimeClient,
    sql: str,
    timestamp_column: str,
    value_column: str,
    start_ts: int,
    end_ts: int,
    multi_series: bool = False,
    extra_filters: dict | None = None,
) -> pd.DataFrame:
    start = pendulum.from_timestamp(start_ts)
    end = pendulum.from_timestamp(end_ts)
    query_sql = greptime_timestamp_sql(
        start=start,
        end=end,
        sql=sql,
        extra_filters=extra_filters,
    )
    res = greptime_client.execute(query_sql)
    df = pd.DataFrame(res)
    if multi_series:
        return df
    return create_train_df(df, timestamp_column, value_column)


MINIMUM_POINTS = 3


def train_one_series(df: pd.DataFrame, start, end, country, subdivision, resolution):
    if len(df) < MINIMUM_POINTS:
        return None, None
    model = create_model(df, start, end, country, subdivision)
    deviation = get_noise_stdev(model, df, resolution)
    return model, deviation


def create_df_from_promql(
    prometheus_client: PrometheusClient,
    mimir_query: str,
    start: int,
    end: int,
    resolution: int,
    exclude_labels: list[str] | None = None,
    multi_series: bool = False,
):
    exclude_labels = exclude_labels or []
    exclude_labels.append("__name__")  # __name__ is always excluded
    df = prom_reads.series_query(
        prometheus_client,
        mimir_query,
        start,
        end,
        resolution,
        multi_series=True,
    )
    if df.empty:
        raise ValueError("No data points were returned")
    df = df.drop(columns=exclude_labels, errors="ignore")
    df["timestamp"] = df["timestamp"].apply(lambda dt: dt.timestamp() * 1000)
    if multi_series:
        return df
    return create_train_df(df, "timestamp", "value")


PROPHET_MAX_MULTI_SERIES_MODELS = 500


def train(
    service_config: ProphecyServiceConfig,
    options: ProphecyJobOptions,
):
    init_config(service_config)
    logger.debug("Starting prophecy training")
    if options.promql_query:
        if service_config.is_sks_env:
            prometheus_client = PrometheusClient(
                mimir_proxy_url=f"{service_config.greptime_url}/v1/prometheus/api/v1",
                mimir_enabled=False,
                uri="/query_range",
                headers={
                    "x-greptime-db-name": service_config.greptime_sks_db,
                },
            )
        else:
            prometheus_client = PrometheusClient(
                mimir_proxy_url=service_config.mimir_proxy,
                mimir_frontend_url=service_config.mimir_frontend,
                mimir_enabled=service_config.mimir_enabled,
                headers={"X-Scope-OrgID": options.tenant_id},
            )
        df = create_df_from_promql(
            prometheus_client,
            options.promql_query,
            options.start.int_timestamp,
            options.end.int_timestamp,
            options.resolution,
            exclude_labels=options.exclude_labels,
            multi_series=options.multi_series,
        )
    elif options.sql:
        if not service_config.greptime_url:
            raise ServiceError("GreptimeDB URL is not configured")
        greptime_client = SyncGreptimeClient(
            url=service_config.greptime_url,
            db=cast(str, service_config.greptime_sks_db if service_config.is_sks_env else service_config.greptime_db),
        )
        df = create_df_from_sql(
            greptime_client,
            options.sql,
            options.timestamp_column,
            options.value_column,
            options.start.int_timestamp,
            options.end.int_timestamp,
            multi_series=options.multi_series,
            extra_filters=options.extra_filters,
        )
    else:
        raise ValueError("Either mimir_query or sql must be provided")
    if not options.multi_series:
        model, deviation = train_one_series(
            df,
            options.start.int_timestamp,
            options.end.int_timestamp,
            options.country,
            options.subdivision,
            options.resolution,
        )
        if model is None or deviation is None:
            raise ValueError("Not enough data points to train the model")
        prophecy_model = ProphecyModel(options.tenant_id, options.prophecy_id, model, deviation)
    else:
        label_columns = [c for c in df.columns if c not in [options.timestamp_column, options.value_column]]
        prophecy_models = []
        labels = []
        for label_values, series in df.groupby(label_columns):
            model, deviation = train_one_series(
                create_train_df(series.reset_index(), options.timestamp_column, options.value_column),
                options.start.int_timestamp,
                options.end.int_timestamp,
                options.country,
                options.subdivision,
                options.resolution,
            )
            if model is None or deviation is None:
                continue
            prophecy_models.append(ProphecyModel(options.tenant_id, options.prophecy_id, model, deviation))
            labels.append(dict(zip(label_columns, list(label_values), strict=False)))
            if len(prophecy_models) == PROPHET_MAX_MULTI_SERIES_MODELS:
                logger.warning(f"Max number of models reached for multi-series prophecy {options.prophecy_id}")
                break
        prophecy_model = MultiSeriesProphecyModel(options.tenant_id, options.prophecy_id, prophecy_models, labels)
    prophecy_model.save_to_s3()
    logger.info("Prophecy model trained successfully")


def chunk(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i : i + n]


def filter_none(lst):
    return [i for i in lst if i is not None]


def get_noise_stdev(model, df, resolution):
    start = df["ds"].min().timestamp()
    end = df["ds"].max().timestamp()
    forecast = forecast_model(model, resolution, start, end)
    og = df["y"].head(df["y"].size)
    yhat = forecast["yhat"].head(og.size)
    unmodelled = og - yhat
    chunked = chunk(unmodelled, 10)
    stdev_chunked = (None if t.size < MINIMUM_POINTS else stdev(t) for t in chunked)
    return median(filter_none(stdev_chunked))
