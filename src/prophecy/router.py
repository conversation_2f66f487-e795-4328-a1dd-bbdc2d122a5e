import asyncio
from collections import defaultdict
from typing import Annotated

import httpx
from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Response, status
from pydantic import BaseModel, ConfigDict
from pydantic_extra_types.pendulum_dt import DateTime

from config import Settings, get_settings
from dependencies import db_txn, get_async_greptime, get_async_greptime_promql_client
from greptime import AsyncGreptimeClient
from job.service import job_exists
from utils import prom_metric_selector, to_kebab, try_parse_float

from . import prophecy_service
from .utils import prophecy_table_name


class ProphecyCreate(BaseModel):
    model_config = ConfigDict(alias_generator=to_kebab)
    created: DateTime
    query: str
    multi_series: bool = False


class ProphecyRetrain(BaseModel):
    retrain: DateTime


class BulkProphecyCreate(BaseModel):
    prophecies: dict[str, ProphecyCreate] = {}  # prophecy_id -> ProphecyCreate


class BulkProphecyDelete(BaseModel):
    model_config = ConfigDict(alias_generator=to_kebab)
    prophecy_ids: list[str] = []


router = APIRouter(prefix="/tenants/{tenant_id}/prophecies")


@router.post("/{prophecy_id}")
async def define(
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    tenant_id: str,
    prophecy_id: str,
    request: ProphecyCreate,
):
    if not await job_exists(conn, prophecy_id):
        await prophecy_service.create(
            conn,
            greptime_client,
            tenant_id,
            prophecy_id,
            request.query,
            request.created,
            request.multi_series,
        )
    return Response(status_code=status.HTTP_200_OK)


@router.post("/bulk")
async def bulk_define(
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    tenant_id: str,
    request: BulkProphecyCreate,
):
    prophecies_to_create = [
        (
            prophecy_id,
            prophecy_create.query,
            prophecy_create.created,
            prophecy_create.multi_series,
        )
        for prophecy_id, prophecy_create in request.prophecies.items()
    ]

    if prophecies_to_create:
        await prophecy_service.bulk_create(conn, greptime_client, tenant_id, prophecies_to_create)

    return Response(status_code=status.HTTP_200_OK)


@router.delete("/bulk")
async def bulk_delete(
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    tenant_id: str,
    request: BulkProphecyDelete,
):
    if request.prophecy_ids:
        await prophecy_service.bulk_delete(conn, greptime_client, tenant_id, request.prophecy_ids)

    return Response(status_code=status.HTTP_200_OK)


@router.delete("/{prophecy_id}")
async def delete(
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
    tenant_id: str,
    prophecy_id: str,
):
    await prophecy_service.delete(conn, greptime_client, tenant_id, prophecy_id)
    return Response(status_code=status.HTTP_200_OK)


@router.post("/{prophecy_id}/retrain")
async def retrain(
    conn: Annotated[Connection, Depends(db_txn)],
    tenant_id: str,
    prophecy_id: str,
    request: ProphecyRetrain,
):
    await prophecy_service.retrain(conn, tenant_id, prophecy_id, request.retrain)
    return {"status": "ok"}


@router.delete("/{prophecy_id}/retrain")
async def cancel_retrain(conn: Annotated[Connection, Depends(db_txn)], tenant_id: str, prophecy_id: str):
    await prophecy_service.cancel_retrain(conn, tenant_id, prophecy_id)
    return {"status": "ok"}


async def query_prophecy_greptime(
    client: httpx.AsyncClient,
    db: str,
    tenant_id: str,
    prophecy_id: str,
    query_type: str,
    start: DateTime,
    end: DateTime,
    step: str,
    labels: dict[str, str] | None = None,
):
    q = prom_metric_selector(prophecy_table_name(tenant_id, prophecy_id, query_type), labels)
    res = await client.get(
        "/query_range",
        params={
            "start": start.to_iso8601_string(),
            "end": end.to_iso8601_string(),
            "step": step,
            "query": q,
            "db": db,
        },
    )
    res = res.json()
    if res["status"] != "success":
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
    result = res.get("data", {}).get("result", [])
    return result or []


@router.get("/{prophecy_id}/query")
async def query_prophecy(
    client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    prophecy_id: str,
    start: DateTime,
    end: DateTime,
    step: str,
):
    yhat_res, stddev_res = await asyncio.gather(
        query_prophecy_greptime(client, settings.greptime.db, tenant_id, prophecy_id, "yhat", start, end, step),
        query_prophecy_greptime(client, settings.greptime.db, tenant_id, prophecy_id, "stddev", start, end, step),
    )
    if yhat_res:
        yhat_values = yhat_res[0].get("values", [])
        prediction = {"labels": {}, "values": [[v[0], try_parse_float(v[1])] for v in yhat_values]}
    else:
        prediction = {}
    if stddev_res:
        yhat_stddev = stddev_res[0].get("values", [])
        stddev = {"labels": {}, "values": [[v[0], try_parse_float(v[1])] for v in yhat_stddev]}
    else:
        stddev = {}
    return {
        "prediction": prediction,
        "stddev": stddev,
    }


async def query_marker_prophecy_raw(
    client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    prophecy_id: str,
    start: DateTime,
    end: DateTime,
    step: str,
    labels: str | None = None,
):
    label_dict = {}
    if labels:
        label_paris = labels.split(",") if labels else []
        for label in label_paris:
            key, value = label.split("=")
            label_dict[key] = value[1:-1] if value.startswith('"') and value.endswith('"') else value
    yhat_res, stddev_res = await asyncio.gather(
        query_prophecy_greptime(
            client,
            settings.greptime.db,
            tenant_id,
            prophecy_id,
            "yhat",
            start,
            end,
            step,
            label_dict,
        ),
        query_prophecy_greptime(
            client,
            settings.greptime.db,
            tenant_id,
            prophecy_id,
            "stddev",
            start,
            end,
            step,
            label_dict,
        ),
    )
    responses = defaultdict(lambda: {"prediction": {}, "stddev": {}})

    def process_result(result_list, key_name):
        for res in result_list:
            metrics = res.get("metric", {}).copy()
            if metrics:
                metrics.pop("__name__", None)
            values = [[v[0], try_parse_float(v[1])] for v in res.get("values", [])]

            responses[frozenset(metrics.items())][key_name] = {
                "labels": metrics,
                "values": values,
            }

    process_result(yhat_res, "prediction")
    process_result(stddev_res, "stddev")

    return list(responses.values())


@router.get("/{prophecy_id}/query_raw")
async def query_raw_marker_prophecy(
    client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    prophecy_id: str,
    start: DateTime,
    end: DateTime,
    step: str,
    labels: str | None = None,
):
    return await query_marker_prophecy_raw(
        client,
        settings,
        tenant_id,
        prophecy_id,
        start,
        end,
        step,
        labels,
    )


@router.get("/query_raw")
async def query_raw_markers_prophecies(
    client: Annotated[httpx.AsyncClient, Depends(get_async_greptime_promql_client)],
    settings: Annotated[Settings, Depends(get_settings)],
    tenant_id: str,
    start: DateTime,
    end: DateTime,
    step: str,
    prophecy_ids: str,
):
    prophecy_id_list = prophecy_ids.split(",")
    results = await asyncio.gather(
        *[
            query_marker_prophecy_raw(
                client,
                settings,
                tenant_id,
                prophecy_id,
                start,
                end,
                step,
            )
            for prophecy_id in prophecy_id_list
        ],
    )
    return dict(zip(prophecy_id_list, results, strict=False))
