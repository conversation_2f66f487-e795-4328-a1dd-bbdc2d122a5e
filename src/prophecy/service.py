import asyncio
from collections.abc import Sequence

from asyncpg import Connection
from pendulum import DateTime
from pypika import PostgreSQLQuery as Q
from starlette.concurrency import run_in_threadpool

from config import get_settings
from database import is_updated
from greptime import AsyncGreptimeClient
from job.model import JobStatus, JobType, ProphecyConfig, table as jobs
from prophecy.utils import prophecy_table_name, trained_model_path


def _create_physical_table_query():
    return """
    CREATE TABLE IF NOT EXISTS "greptime_physical_table" (
      "greptime_timestamp" TIMESTAMP(3) NOT NULL,
      "greptime_value" DOUBLE NULL,
      TIME INDEX ("greptime_timestamp")
    )
    ENGINE=metric WITH(
      physical_metric_table = 'true'
    )
    """


def _create_prophecy_table_query(table: str):
    return f"""
    CREATE TABLE IF NOT EXISTS "{table}" (
      "greptime_timestamp" TIMESTAMP(3) NOT NULL,
      "greptime_value" DOUBLE NULL,
      TIME INDEX ("greptime_timestamp")
    )
    ENGINE=metric WITH(
      on_physical_table = 'greptime_physical_table'
    )
    """


async def create(
    conn: Connection,
    greptime_client: AsyncGreptimeClient,
    tenant_id: str,
    prophecy_id: str,
    query: str,
    created: DateTime,
    multi_series: bool = False,
):
    await greptime_client.batch_execute(
        [
            _create_physical_table_query(),
            _create_prophecy_table_query(prophecy_table_name(tenant_id, prophecy_id, "stddev")),
            _create_prophecy_table_query(prophecy_table_name(tenant_id, prophecy_id, "yhat")),
        ],
    )
    config = ProphecyConfig(query=query, multi_series=multi_series)
    await conn.execute(
        """
            INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
        tenant_id,
        prophecy_id,
        created.naive(),
        DateTime.now(tz="UTC").naive(),
        JobType.prophecy,
        20,
        config.kebab_dump(),
    )


async def bulk_create(
    conn: Connection,
    greptime_client: AsyncGreptimeClient,
    tenant_id: str,
    prophecies: Sequence[tuple[str, str, DateTime, bool]],  # (prophecy_id, query, created, multi_series)
):
    # Create all required tables
    table_queries = [_create_physical_table_query()]
    for prophecy_id, _, _, _ in prophecies:
        table_queries.extend([
            _create_prophecy_table_query(prophecy_table_name(tenant_id, prophecy_id, "stddev")),
            _create_prophecy_table_query(prophecy_table_name(tenant_id, prophecy_id, "yhat")),
        ])

    await greptime_client.batch_execute(table_queries)

    # Insert all jobs in a batch
    job_data = []
    for prophecy_id, query, created, multi_series in prophecies:
        config = ProphecyConfig(query=query, multi_series=multi_series)
        job_data.append((
            tenant_id,
            prophecy_id,
            created.naive(),
            DateTime.now(tz="UTC").naive(),
            JobType.prophecy,
            20,
            config.kebab_dump(),
        ))

    await conn.executemany(
        """
            INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
        job_data,
    )


async def get_prophecy_job(conn: Connection, tenant_id, prophecy_id: str):
    q = str(
        Q.from_(jobs)
        .select(jobs.id, jobs.config)
        .where((jobs.type == JobType.prophecy) & (jobs.job_source_id == prophecy_id) & (jobs.tenant_id == tenant_id)),
    )
    records = await conn.fetch(q)
    return records[0] if records else None


async def retrain(conn: Connection, tenant_id: str, prophecy_id: str, retrain_date: DateTime) -> bool:
    job = await get_prophecy_job(conn, tenant_id, prophecy_id)
    if not job:
        return False

    res = await conn.execute(
        "UPDATE jobs SET config = config || $1, next_run = $2, cost = $3, status = $4 WHERE id = $5 AND status != 'running'",
        {"trained": False},
        retrain_date.naive(),
        20,
        JobStatus.pending,
        job["id"],
    )
    return is_updated(res)


async def cancel_retrain(conn: Connection, tenant_id: str, prophecy_id: str) -> bool:
    job = await get_prophecy_job(conn, tenant_id, prophecy_id)
    if not job:
        return False
    prophecy_config = ProphecyConfig(**job["config"])
    if not prophecy_config.predicted:
        return True
    res = await conn.execute(
        "UPDATE jobs SET config = config || $1, next_run = $2, cost = $3 WHERE id = $4 AND status != 'running'",
        {"trained": True},
        prophecy_config.predicted.add(days=-1).naive(),
        10,
        job["id"],
    )
    return is_updated(res)


async def delete(conn: Connection, greptime_client: AsyncGreptimeClient, tenant_id: str, prophecy_id: str):
    q = str(
        Q.from_(jobs)
        .delete()
        .where((jobs.type == JobType.prophecy) & (jobs.job_source_id == prophecy_id) & (jobs.tenant_id == tenant_id)),
    )
    await conn.execute(q)
    await greptime_client.batch_execute(
        [
            str(Q.drop_table(prophecy_table_name(tenant_id, prophecy_id, "stddev")).if_exists()),
            str(Q.drop_table(prophecy_table_name(tenant_id, prophecy_id, "yhat")).if_exists()),
        ],
    )
    settings = get_settings()
    model_path = trained_model_path(settings.s3.base_path, tenant_id, prophecy_id)
    await run_in_threadpool(model_path.unlink, missing_ok=True)


async def bulk_delete(conn: Connection, greptime_client: AsyncGreptimeClient, tenant_id: str, prophecy_ids: list[str]):
    if not prophecy_ids:
        return

    # Delete jobs in batch
    q = str(
        Q.from_(jobs)
        .delete()
        .where(
            (jobs.type == JobType.prophecy) & (jobs.job_source_id.isin(prophecy_ids)) & (jobs.tenant_id == tenant_id),
        ),
    )
    await conn.execute(q)

    # Drop tables in batch
    drop_queries = []
    for prophecy_id in prophecy_ids:
        drop_queries.extend([
            str(Q.drop_table(prophecy_table_name(tenant_id, prophecy_id, "stddev")).if_exists()),
            str(Q.drop_table(prophecy_table_name(tenant_id, prophecy_id, "yhat")).if_exists()),
        ])

    await greptime_client.batch_execute(drop_queries)

    # Delete model files in batch
    settings = get_settings()
    model_paths = [trained_model_path(settings.s3.base_path, tenant_id, prophecy_id) for prophecy_id in prophecy_ids]

    async def delete_model(path):
        await run_in_threadpool(path.unlink, missing_ok=True)

    await asyncio.gather(*[delete_model(path) for path in model_paths], return_exceptions=True)
