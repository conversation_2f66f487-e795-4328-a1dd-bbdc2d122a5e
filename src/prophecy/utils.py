import os

import s3path

IS_SKS_ENV = os.getenv("IS_SKS_ENV", "false").lower() == "true"


def prophecy_table_name(tenant_id: str, prophecy_id: str, table_type: str):
    if IS_SKS_ENV:
        return f"t_{tenant_id}_reserved_prophecy:{table_type}:{prophecy_id}".replace("-", "_")
    return f"_reserved_prophecy:{table_type}:{prophecy_id}".replace("-", "_")


def trained_model_path(base_path: s3path.S3Path, tenant_id: str, prophecy_id: str) -> s3path.S3Path:
    return base_path / f"prophecy/t_{tenant_id}_{prophecy_id}.json"
