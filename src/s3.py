import boto3
import structlog
from s3path import register_configuration_parameter

from config import Settings

logger = structlog.get_logger(__name__)


def register_hosted_s3(settings: Settings, s3_url: str):
    logger.debug("registering hosted S3", s3_url=s3_url)
    resource = boto3.resource(
        "s3",
        endpoint_url=s3_url,
        aws_access_key_id=settings.s3.username,
        aws_secret_access_key=settings.s3.password,
        region_name=settings.s3.region,
    )
    register_configuration_parameter(settings.s3.base_path, resource=resource)
    logger.debug("registered hosted S3", s3_url=s3_url)


def register_aws_s3(settings: Settings):
    if not settings.sagemaker.access_key or not settings.sagemaker.secret_key:
        logger.debug("no sagemaker access key and secret key")
        resource = boto3.resource("s3")
    else:
        logger.debug("registering sagemaker s3")
        resource = boto3.resource(
            "s3",
            aws_access_key_id=settings.sagemaker.access_key,
            aws_secret_access_key=settings.sagemaker.secret_key,
            region_name=settings.sagemaker.aws_region,
        )
    register_configuration_parameter(settings.sagemaker.aws_path, resource=resource)
