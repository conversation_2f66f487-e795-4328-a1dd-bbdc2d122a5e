from __future__ import annotations

import os
import random
from typing import TYPE_CHECKING

import httpx
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_random

if TYPE_CHECKING:
    from config import ConsulSettings, ServiceConfig

from custom_exceptions import ServiceError


def lookup_svc(client: httpx.Client, service: str, tag: str | None = None) -> list[str]:
    q = {"filter": f'ServiceTags contains "{tag}"'} if tag else {}
    res = client.get(f"/v1/catalog/service/{service}", params=q).json()
    return [f"http://{instance['Address']}:{instance['ServicePort']}" for instance in res]


class Service:
    def __init__(self, consul_client: httpx.Client, config: ServiceConfig) -> None:
        self.config = config
        self._consul_client = consul_client
        self._urls = []

    @retry(reraise=True, stop=stop_after_attempt(3), wait=wait_random(0.5, 1))
    def url(self) -> str:
        if self.config.static_url:
            return self.config.static_url
        if self.config.env_var:
            v = os.getenv(self.config.env_var)
            if v:
                return v
        if not self._urls:
            try:
                self._urls = lookup_svc(self._consul_client, self.config.service, self.config.tag)
            except httpx.TransportError as e:
                raise ServiceError("Could not connect to consul") from e
        if not self._urls:
            raise ServiceError(
                "Could not resolve domain name for service",
                service=self.config.service,
                tag=self.config.tag,
            )
        return random.choice(self._urls)

    @retry(reraise=True, stop=stop_after_attempt(1), retry=retry_if_exception_type(ServiceError))
    def request(self, *args, **kwargs) -> httpx.Response:
        with httpx.Client(base_url=self.url()) as client:
            try:
                return client.request(*args, **kwargs)
            except httpx.TransportError as e:
                self.invalidate_cache()
                raise ServiceError(
                    "Could not connect to service",
                    service=self.config.service,
                    tag=self.config.tag,
                ) from e

    def invalidate_cache(self):
        self._urls.clear()


class ServicesRegistry:
    def __init__(self, consul_settings: ConsulSettings, configs: dict[str, ServiceConfig]):
        self._consul_settings = consul_settings
        self._consul_client = httpx.Client(base_url=consul_settings.url)
        self._configs = configs
        self._services: dict[str, Service] = {}

    def service(self, service: str) -> Service:
        if service not in self._services:
            self._services[service] = Service(self._consul_client, self._configs[service])
        return self._services[service]
