# UEL User Experience Level

User Experience Level (UEL) provides a comprehensive evaluation of terminal experience status by analyzing MCS encoding traffic, RSSI distribution of all terminals associated with an AP, and the channel availability rate of the AP within a time period (default 10 minutes).

**Important Note: Throughout this document, when referring to "RSSI", the actual values being measured and calculated are SNR (Signal-to-Noise Ratio) values, not dBm power levels. This is a key distinction for understanding the calculations.**

```mermaid
graph LR
  UEL["UEL<br/>User Experience Level"] --> ConnectionQuality["Connection Quality"]
  UEL --> TransmissionEfficiency["Transmission Efficiency"]

  ConnectionQuality --> ConnectionSuccessRate["Connection Success Rate"]
  ConnectionQuality --> ConnectionFrequency["Connection Frequency"]
  ConnectionQuality --> ConnectionDuration["Connection Duration"]

  TransmissionEfficiency --> MCSDistribution["MCS Distribution"]
  TransmissionEfficiency --> RSSIDistribution["RSSI Distribution"]
  TransmissionEfficiency --> TransmissionSuccessRate["Transmission Success Rate"]

  DeviceLevel["Device Level"] --> CoverageQuality["Coverage Quality"]
  DeviceLevel --> LinkQuality["Link Quality"]

  CoverageQuality --> InterferenceAssessment["Interference Assessment"]
  CoverageQuality --> SignalQuality["Signal Quality"]

  LinkQuality --> UplinkScore["Uplink Score"]
  LinkQuality --> AirInterfaceScore["Air Interface Score"]
```

## Connection Quality - Online

### Metrics

Three metrics extracted from syslog:

- <font color="green" size=4>ap_node_connection_state_duration_ns</font>: Records the time taken from state_a -> state_b when a user successfully transitions to the next successful state. For example, how long it took from associated -> authentication. Records ap_mac, auth_mode, bssid, last_state, target_state, sta_mac, duration.
- <font color="green" size=4>ap_node_connection_failure_duration_ns</font>: Records the duration when a user enters a failure state from any state. For example, from associated -> L2 Auth Fail. Records the time from receiving the user's first log to final failure. Records ap_mac, auth_mode, bssid, reason, state, sta_mac, duration.
- <font color="green" size=4>ap_node_connection_success_duration_ns</font>: Based on the VAP's Auth Mode, records the time from receiving the user's first log to final success.
![alt text](image-2.png)

### Calculation Formula

#### Connection Quality Score Calculation Method

$
Connection\ Quality = Connection\ Success\ Rate \times 0.4 + Connection\ Frequency \times 0.3 + Connection\ Duration \times 0.3
$

- <font color="green" size=4>SuccessCount</font>: Based on auth_mode and whether web_auth is used, calculate the number of successes within 10 minutes as SuccessCount, and the P90 of connection success duration within 10 minutes as p90Duration.
- <font color="green" size=4>FailureCount</font>: Based on auth_mode and web_auth, calculate the number of failures within 10 minutes as failure_count.
- <font color="green" size=4>ChangeCount</font>: Based on auth_mode and web_auth, calculate the number of successful state changes triggered by users within 10 minutes.

Connection success rate is the ratio of successful users to total connected users.
Note: Currently observed issue is that when SSID password changes, as long as one terminal was previously successfully associated under the old password and has auto-association enabled for that SSID, the terminal will continuously attempt using the old password, causing the connection success rate to approach 0.

$
Connection\ Success\ Rate = \frac{SuccessCount}{SuccessCount + FailureCount}
$

$
Connection\ Frequency = \frac{ChangeCount}{ChangeCount + FailureCount}
$

$
Connection\ Duration = p90(duration)\ interval\ scoring
$

Connection Duration: Uses p90_duration value. We expect users to connect successfully within 10 seconds. If there are no successes but failures within the interval, the score is 0, equivalent to complete failure. If there are successful connections, calculate according to the formula:

$$
S_n = \begin{cases}
  P90_d \leq 10, S_n = 0.7 + (10 - P90_d) \times 0.03 \\
  P90_d \leq 20, S_n = 0.7 - (P90_d - 10) \times 0.05 \\
  P90_d > 20 , S_n = 0.2 - min((P90_d - 20) \times 0.01, 0.15)
\end{cases}
$$

## Transmission Efficiency

### MCS Distribution - Online

#### Metric Parameters

```
ap_radio_vap_node_rx_mcs_bucket
ap_radio_vap_node_tx_mcs_bucket
ap_radio_obss_chan_util
```

#### Calculation Formula

$MCS_i = MCSIndex + 1$   # MCS rate set index plus 1

$RX_i = RXMCSBucket[MCS_i]$  # Reported packet count statistics for each rate
$TX_i = TXMCSBucket[MCS_i]$  # Reported packet count statistics for each rate
$RXTotal_n = \displaystyle \sum^{max}_{msc_i=1>}RX_i$
$TXTotal_n = \displaystyle \sum^{max}_{msc_i=1>}TX_i$

STA<sub>n</sub><sup>rx</sup> = $\displaystyle \frac{\sum^{max}_{mcs_i=1}{RX_i \times MCS_i}}{RXtotal_n \times max}$

STA<sub>n</sub><sup>tx</sup> = $\displaystyle \frac{\sum^{max}_{mcs_i=1}{TX_i \times MCS_i}}{TXtotal_n \times max}$

STA<sub>n</sub><sup>score</sup> = ( STA<sub>n</sub><sup>rx</sup> \times 0.5 + STA<sub>n</sub><sup>tx</sup> \times 0.5 ) \times (1 - OBSS<sub>[0-1]</sub>)
OBSS needs to be determined based on the Radio where the terminal is located

### RSSI Distribution

#### Metrics

```
ap_radio_vap_node_rx_rssi
```

Calculation method reference: <https://gist.github.com/senseisimple/fe91413d6bb5d5e0994222ac59bc90c4>
Calculated every ten minutes
**Note: The RSSI values here are actually SNR (Signal-to-Noise Ratio) values, not dBm.**

```
band = 2.4g if ath0-15, 5g if ath16-31
perfect_snr = 60
worst_snr = 8
sta_snr = median(ap_radio_vap_node_rx_rssi) group by (ap_mac, vap, sta_mac)
sta_snr_band = 2.4g if ath0-15, 5g if ath16-31
sta_snr_score = (100 * (perfect_snr - worst_snr) *
      (perfect_snr - worst_snr) -
      (perfect_snr - sta_snr) *
      (25 * (perfect_snr - worst_snr) +
       95 * (perfect_snr - sta_snr))) /
     ((perfect_snr - worst_snr) *
      (perfect_snr - worst_snr))

snr_band_score = avg(sta_snr_score) group by band
snr_score = snr_band_score_2.4g * 0.7 + snr_band_score_5g * 0.3
```

P = Perfect_snr = 60
W = Worst_snr = 8
P - W = 52
R<sub>i</sub> = median(ap_radio_vap_node_rx_rssi) group by (ap_mac, vap, sta_mac)

$$Score_i =  \frac{100 \times 52^2  - (P - R_i) (25 \times 52 + 95 \times (P -R_i))} {52^2}$$

$$ScoreWifi_0 = \displaystyle \frac{\sum^{NumWifi_0}_{i=1}{Score_i}}{NumWifi_0}$$
$$ScoreWifi_1 = \displaystyle \frac{\sum^{NumWifi_1}_{i=1}{Score_i}}{NumWifi_1}$$

$$RSSI_s = ScoreWifi_0 \times 0.7 + ScoreWifi_1 \times 0.3$$

### Transmission Success Rate - Online

#### Metrics

```
ap_radio_tx_failures_total
ap_radio_tx_data_packets_total
```

```
tx_failure = increase(ap_radio_tx_failures_total) group by radio
data_packets = increase(ap_radio_tx_data_packets_total) group by radio
radio_success_rate = data_packets / (data_packets + tx_failure)
success_rate = avg(radio_success_rate)
```

$$
\begin{align}
FailureTX_{radio} &= \Delta(\text{ap\_radio\_tx\_failures\_total})_{radio} \\
DataPacketsTX_{radio} &= \Delta(\text{ap\_radio\_tx\_data\_packets\_total})_{radio} \\
SuccessRateTX_{radio} &= \frac{DataPacketsTX_{radio}}{DataPacketsTX_{radio} + FailureTX_{radio}} \\
SuccessRate &= \overline{SuccessRateTX_{radio}}
\end{align}
$$

Where $\Delta(\cdot)_{radio}$ represents the incremental value grouped by radio within a 10-minute time window

## Coverage Quality

$$
Coverage\ Quality = 0.5 \times Interference\ Assessment + 0.5 \times Signal\ Quality
$$

### Interference Assessment

#### Metrics

```
ap_radio_obss_chan_util
```

$$
\begin{align}
ObssChanUtil_{radio,ap\_mac} &= \overline{ap\_radio\_obss\_chan\_util}_{radio,ap\_mac} \\
InterferenceScore &= 1 - \overline{ObssChanUtil_{radio,ap\_mac}}  / 100
\end{align}
$$

### Signal Quality

#### Metrics

```
ap_radio_rx_rssi
```

Normalization method is the same as RSSI distribution above.
**Note: The RSSI values here are actually SNR (Signal-to-Noise Ratio) values, not dBm.**

$$
\begin{align}
BasicSNR_{ap\_mac,radio} &= \overline{ap\_radio\_rx\_rssi}_{ap\_mac,radio} \\
BasicSnrScore_{radio} &= \overline{\operatorname{normalise}(BasicSNR_{ap\_mac,radio})} \\
Score_{radio} &= BasicSnrScore_{radio} \\
Score &= ScoreWifi_0 \times 0.7 + ScoreWifi_1 \times 0.3 \\
\end{align}
$$

## Link Quality

$$
LinkScore = 0.5 \times SymmetryScore + 0.5 \times AirInterfaceScore
$$

### Link Symmetry

<https://www.juniper.net/documentation/us/en/software/mist/mist-aiops/shared-content/topics/concept/sle-wireless-coverage.html>

#### Metrics

```
ap_radio_vap_node_rx_mcs_bucket
ap_radio_vap_node_tx_mcs_bucket
ap_radio_vap_node_rx_nss_bucket
ap_radio_vap_node_tx_nss_bucket
```

$$
Score = \overline{Score_{radio, ap\_mac}} \\
Score_{radio, ap\_mac} = 0.5 \times MCS_{symmetry, radio, ap\_mac} + 0.5 \times NSS_{symmetry, radio, ap\_mac} \\
MCS_{symmetry, radio, ap\_mac} = \frac{min(MCSV_{rx, radio, ap\_mac}, MCSV_{tx, radio, ap\_mac})}{max(MCSV_{rx, radio, ap\_mac}, MCSV_{tx, radio, ap\_mac})} \\
NSS_{symmetry, radio, ap\_mac} = \frac{min(NSSV_{rx, radio, ap\_mac}, NSSV_{tx, radio, ap\_mac})}{max(NSSV_{rx, radio, ap\_mac}, NSSV_{tx, radio, ap\_mac})} \\
$$

$direction \in \{\texttt{tx}, \texttt{rx}\}$ represents transmission direction
$$
McsWeighted_{direction, radio, ap\_mac} = \sum_{sta} (\sum^{max}_{mcs_i=1}{(i+1) \times MCS_{direction, radio, ap\_mac, sta, i}}) \\
McsPackets_{direction, radio, ap\_mac} = \sum_{sta} (\sum^{max}_{mcs_i=1}{MCS_{direction, radio, ap\_mac, sta, i}}) \\

MCSV_{direction, radio, ap\_mac} = \frac{McsWeighted_{direction, radio, ap\_mac}}{McsPackets_{direction, radio, ap\_mac}} \\
NSSV_{direction, radio, ap\_mac} = \frac{NSSWeighted_{direction, radio, ap\_mac}}{NSSPackets_{direction, radio, ap\_mac}} \\
$$

### Air Interface Score

#### Metrics

```
ap_radio_rx_phy_errors_total
ap_radio_rx_crc_errors_total
ap_radio_rx_data_packets_total
ap_radio_rx_mgmt_frames_total
```

$type \in \{\texttt{phy\_err}, \texttt{crc\_err}, \texttt{data\_pkt}, \texttt{mgmt\_pkt}\}$

$$
\begin{align}
AirInterfaceScore &= 1 - \frac{total_{phy\_err} + total_{crc\_err}}{total_{data\_pkt} + total_{mgmt\_pkt} + total_{phy\_err} + total_{crc\_err}} \\
total_{type} &= \sum_{radio, ap\_mac} \Delta(\text{Total}_{type})_{radio, ap\_mac} \\
\end{align}
$$
