from datetime import timed<PERSON><PERSON>

import structlog
from asyncpg import Connection, Pool
from pendulum import DateTime, Duration, duration
from pypika import PostgreSQLQuery as Q

from anomaly_detector.sks.mcs_nss import sync_mcs_nss_qoes
from config import Settings
from job.model import <PERSON>Model, JobType, SKSMcsNssConfig, table as jobs
from job.scheduler.abc import BaseJob
from services import ServicesRegistry


class SksMcsNssJob(BaseJob):
    def __init__(self, job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
        self.job_record = job_record
        self.settings = settings
        self.services_registry = services_registry
        self.pool = pool
        self.logger = structlog.get_logger("SksMcsNssJob", id=self.job_record.id)

    def maximum_duration(self) -> Duration:
        return duration(minutes=30)

    async def prepare_args(self):
        if self.job_record.next_run is None:
            raise ValueError("next_run is None")
        return {
            "greptime_client_url": self.services_registry.service("greptime").url(),
            "start_time": self.job_record.next_run - timedelta(minutes=10),
            "end_time": self.job_record.next_run,
            "sks_db": self.settings.greptime.sks_db,
            "site_id": self.job_record.tenant_id,
        }

    def run(self):
        return sync_mcs_nss_qoes

    async def on_success(self):
        async with (
            self.pool.acquire() as conn,
            conn.transaction(),
        ):
            await conn.execute(
                """
                UPDATE jobs SET status = 'pending', attempts = 0, errors = '[]', run_started = NULL, next_run = next_run + $2
                WHERE id = $1
                """,
                self.job_record.id,
                timedelta(minutes=10),
            )


def sks_mcs_nss_job_factory(job_record: JobModel, settings: Settings, services_registry: ServicesRegistry, pool: Pool):
    return SksMcsNssJob(job_record, settings, services_registry, pool)


async def create_sks_mcs_nss_job(
    conn: Connection,
    tenant_id: str,
    created: DateTime,
):
    await conn.execute(
        """
        INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        """,
        tenant_id,
        "mcs_nss_qoe",
        created.naive(),
        created.naive(),
        JobType.sks_mcs_nss,
        2,
        SKSMcsNssConfig().kebab_dump(),
    )


async def delete_sks_mcs_nss_job(
    conn: Connection,
    tenant_id: str,
):
    q = str(Q.from_(jobs).delete().where((jobs.type == JobType.sks_mcs_nss) & (jobs.tenant_id == tenant_id)))
    await conn.execute(q)
