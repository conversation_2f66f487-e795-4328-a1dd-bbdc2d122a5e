from typing import Annotated, cast

import pendulum
from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Response, status
from pendulum import DateTime
from pydantic import BaseModel
from pypika import PostgreSQLQuery as Q

from anomaly_detector import ad_service
from anomaly_detector.marker import PromMarker
from anomaly_detector.schema import RetrainRequest
from anomaly_detector.sks.marker import CustomMarker, get_prom_marker_sql
from anomaly_detector.sks.qoe import default_omit_labels, sks_markers
from config import TIMEZONE
from dependencies import db_txn, get_async_greptime, get_sks_async_greptime
from greptime import AsyncGreptimeClient
from job.model import GreptimeAnomalyDetectorConfig, GreptimeProphecyConfig, JobType, UTCISODatetime, table as jobs
from prophecy import prophecy_service
from sks.mcs_nss_job import create_sks_mcs_nss_job, delete_sks_mcs_nss_job

router = APIRouter(prefix="/sks")


class CreateSiteRequest(BaseModel):
    site_id: str


async def create_prophecy_sql_job(
    conn: Connection,
    tenant_id: str,
    created: DateTime,
    custom_marker: CustomMarker,
    sub_marker: str | None = None,
):
    column = sub_marker if sub_marker else custom_marker.name
    config = GreptimeProphecyConfig(
        sql=custom_marker.sql,
        timestamp_column=custom_marker.timestamp_field,
        value_column=column,
        extra_filters={"site_id": tenant_id},
    )
    await conn.execute(
        """
        INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        """,
        tenant_id,
        column,
        created.naive(),
        created.add(days=1).naive(),
        JobType.prophecy,
        20,
        config.kebab_dump(),
    )


@router.post("/sites")
async def create_site(
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_sks_async_greptime)],
    request: CreateSiteRequest,
):
    existed_site = await conn.fetch(
        str(Q.from_(jobs).select(jobs.job_source_id).where(jobs.tenant_id == request.site_id).limit(1)),
    )
    if existed_site:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT)
    start_local_time = pendulum.now().in_tz(TIMEZONE).start_of("day").in_tz("UTC")
    config = GreptimeAnomalyDetectorConfig(
        db="sks",
        is_greptime=True,
        flow_id="skslog",
        express_training=True,
        excluded_fields=[
            {"regex": "\\[\\d+\\.\\d+\\]"},
        ],
        scheduled_train_date=cast(UTCISODatetime, start_local_time.add(days=8)),
        skip_public_holidays=False,
        skip_log_volume_profile=True,
        prophecy_id="",
        log_rate_metric="",
    )
    await conn.execute(
        """
            INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
        request.site_id,
        request.site_id,
        start_local_time.naive(),
        start_local_time.add(days=2).naive(),
        JobType.log_anomaly,
        40,
        config.kebab_dump(),
    )

    await create_sks_mcs_nss_job(conn, request.site_id, start_local_time)

    for marker in sks_markers:
        if isinstance(marker, PromMarker):
            labels = []
            for rec in await greptime_client.execute(f"DESCRIBE TABLE {marker.name}"):
                column_name = rec["Column"]
                if column_name in default_omit_labels:
                    continue
                if rec.get("Semantic Type") == "TAG":
                    labels.append(column_name)
            sql = get_prom_marker_sql(marker, 300, labels)
            config = GreptimeProphecyConfig(
                sql=sql,
                timestamp_column="greptime_timestamp",
                value_column="greptime_value",
                multi_series=True,
                extra_filters={"site_id": request.site_id},
            )
            await conn.execute(
                """
                    INSERT INTO jobs (tenant_id, job_source_id, created, next_run, type, cost, config)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    """,
                request.site_id,
                marker.name,
                start_local_time.naive(),
                start_local_time.add(days=1).naive(),
                JobType.prophecy,
                20,
                config.kebab_dump(),
            )
        elif isinstance(marker, CustomMarker):
            if not marker.hide:
                await create_prophecy_sql_job(conn, request.site_id, start_local_time, marker)
            for sub_marker in marker.sub_markers:
                await create_prophecy_sql_job(conn, request.site_id, start_local_time, marker, sub_marker.name)
    return Response(status_code=status.HTTP_200_OK)


@router.delete("/sites/{site_id}")
async def delete_site(
    site_id: str,
    conn: Annotated[Connection, Depends(db_txn)],
    greptime_client: Annotated[AsyncGreptimeClient, Depends(get_async_greptime)],
):
    await ad_service.delete(conn, greptime_client, site_id, site_id)
    q = str(
        Q.from_(jobs).select(jobs.job_source_id).where((jobs.type == JobType.prophecy) & (jobs.tenant_id == site_id)),
    )
    await delete_sks_mcs_nss_job(conn, site_id)
    prophecies = await conn.fetch(q)
    for prophecy_job in prophecies:
        await prophecy_service.delete(conn, greptime_client, site_id, prophecy_job["job_source_id"])
    return Response(status_code=status.HTTP_200_OK)


@router.post("/sites/{site_id}/retrain")
async def retrain_site(
    site_id: str,
    conn: Annotated[Connection, Depends(db_txn)],
    request: RetrainRequest,
):
    await ad_service.retrain(
        conn,
        site_id,
        site_id,
        request.retrain,
        request.skip_public_holidays,
    )
    q = str(
        Q.from_(jobs).select(jobs.job_source_id).where((jobs.type == JobType.prophecy) & (jobs.tenant_id == site_id)),
    )
    prophecies = await conn.fetch(q)
    for prophecy_job in prophecies:
        await prophecy_service.retrain(
            conn,
            site_id,
            prophecy_job["job_source_id"],
            request.retrain,
        )
    return Response(status_code=status.HTTP_200_OK)
