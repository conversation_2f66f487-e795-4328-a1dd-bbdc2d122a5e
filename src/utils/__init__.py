import sys
from datetime import datetime
from importlib import import_module
from re import sub

import pandas as pd
from pendulum import DateTime

from config import TIMEZONE

DEFAULT_TIMEZONE = TIMEZONE


def to_kebab(s: str) -> str:
    return "-".join(
        sub(
            r"(\s|_|-)+",
            " ",
            sub(
                r"[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+",
                lambda mo: " " + mo.group(0).lower(),
                s,
            ),
        ).split(),
    )


def try_parse_float(value: str):
    try:
        return float(value)
    except ValueError:
        return value


def prom_metric_selector(metric: str, labels: dict | None = None) -> str:
    conditions = [
        f'__name__="{metric}"',
    ]
    if labels:
        conditions.extend([f'{k}="{v}"' for k, v in labels.items()])
    return f"{{{','.join(conditions)}}}"


def iso_format(dt: datetime | DateTime):
    return dt.isoformat(timespec="milliseconds").replace("+00:00", "Z")


def get_module(module_name, attr_name=None):
    module = sys.modules[module_name] if module_name in sys.modules else import_module(module_name)

    if attr_name:
        return getattr(module, attr_name)

    return module


def prom_result_to_panda_df(
    data,
    column_name,
    *,
    timezone: str = DEFAULT_TIMEZONE,
    multi_series=False,
    df_timestamp_col: str = "timestamp",
) -> pd.DataFrame:
    dfs = []
    empty_dates_column = pd.to_datetime([], unit="s", utc=True).tz_convert(timezone)
    empty_df = pd.DataFrame({df_timestamp_col: empty_dates_column, column_name: []})
    label_columns = set()
    if "data" not in data or "result" not in data["data"]:
        return empty_df
    for res in data["data"]["result"]:
        if res["values"]:
            timestamps = [int(value[0]) for value in res["values"]]
            values = [float(value[1]) for value in res["values"]]
            if not multi_series:
                dates = pd.to_datetime(timestamps, unit="s", utc=True).tz_convert(timezone)
                return pd.DataFrame({df_timestamp_col: dates, column_name: values})
            dates = pd.to_datetime(timestamps, unit="s", utc=True).tz_convert(timezone)
            df = pd.DataFrame({df_timestamp_col: dates, column_name: values})
            label_columns.update(res["metric"].keys())
            for label, value in res["metric"].items():
                if label != "__name__":
                    df[label] = value
            dfs.append(df)
    if multi_series and dfs:
        merged_df = pd.concat(dfs)
        label_columns.discard("__name__")
        merged_df.attrs["labels"] = list(label_columns)
        return merged_df

    return empty_df
