import base64
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from time import sleep
from zoneinfo import ZoneInfo

import httpx
import orjson
import pendulum
import structlog
from google import genai
from google.genai import types
from google.oauth2 import service_account
from openai import OpenAI
from qdrant_client import QdrantClient, models
from qdrant_client.models import PointStruct
from tenacity import retry, stop_after_attempt, wait_fixed, wait_random

from config import LLMSettings
from custom_exceptions import ServiceError
from greptime import SyncGreptimeClient, retrieve_original_logs_from_greptime
from loki.loki import retrieve_original_logs_from_loki


@dataclass
class EmbeddingModel:
    name: str
    dimension: int
    provider: str


EMBEDDING_MODELS = {
    "gemini": EmbeddingModel(name="text-embedding-004", dimension=768, provider="gemini"),
    "bge-m3": EmbeddingModel(name="bge-m3", dimension=1024, provider="ollama"),
    "aliyun-embedding-v4": EmbeddingModel(
        name="text-embedding-v4",
        dimension=1024,
        provider="openai",  # <PERSON><PERSON> uses OpenAI API compatible embedding model
    ),
}


class BaseEmbeddingProvider:
    def __init__(self, model: str, dimension: int = 768):
        self.model = model
        self.dimension = dimension
        self.logger = structlog.get_logger("EmbeddingProvider", model=model)

    def embed_content(self, content: str | list[str], task_type: str | None = None, title: str | None = None):
        raise NotImplementedError("This method should be overridden by subclasses.")


class GeminiTextEmbeddingProvider(BaseEmbeddingProvider):
    def __init__(self, model: str, dimension: int, gemini_client: genai.Client):
        super().__init__(model, dimension)
        self.gemini_client = gemini_client

    @retry(wait=wait_random(min=30, max=60), stop=stop_after_attempt(3))
    def embed_content(self, content: str | list[str], task_type: str | None = None, title: str | None = None):
        try:
            res = self.gemini_client.models.embed_content(
                model=self.model,
                contents=content,  # type: ignore
                config=types.EmbedContentConfig(
                    task_type=task_type,
                    title=title,
                ),
            )
        except Exception:
            self.logger.warning("Failed to get embeddings from Gemini", exc_info=True)
            raise
        if res.embeddings is None:
            raise ValueError("No embeddings returned from Gemini")
        if isinstance(content, str):
            return res.embeddings[0].values
        return [embedding.values for embedding in res.embeddings]


class OpenAITextEmbeddingProvider(BaseEmbeddingProvider):
    def __init__(self, model: str, dimension: int, openai_client: OpenAI):
        super().__init__(model, dimension)
        self.openai_client = openai_client

    @retry(wait=wait_random(min=10, max=30), stop=stop_after_attempt(3))
    def embed_content(self, content: str | list[str], task_type: str | None = None, title: str | None = None):  # noqa: ARG002
        completion = self.openai_client.embeddings.create(
            model=self.model,
            input=content,
            dimensions=self.dimension,
            encoding_format="float",
        )
        if completion.data is None or not completion.data:
            raise ValueError("No embeddings returned from OpenAI")
        return completion.data[0].embedding


class OllamaTextEmbeddingProvider(BaseEmbeddingProvider):
    def __init__(self, model: str, dimension: int, ollama_client: httpx.Client):
        super().__init__(model, dimension)
        self.ollama_client = ollama_client

    @retry(wait=wait_random(min=10, max=30), stop=stop_after_attempt(3))
    def embed_content(self, content: str | list[str], task_type: str | None = None, title: str | None = None):  # noqa: ARG002
        res = self.ollama_client.post("/api/embed", json={"model": self.model, "input": content})
        if res.status_code == httpx.codes.OK:
            if isinstance(content, str):
                return res.json()["embeddings"][0]
            return res.json()["embeddings"]
        raise ServiceError("Failed to get embeddings from Ollama")


def create_embedding_provider(
    llm_settings: LLMSettings,
    model: EmbeddingModel,
    *,
    ollama_url: str | None = None,
):
    if model.provider == "gemini":
        credential_json = orjson.loads(base64.b64decode(llm_settings.google_vertex_credential))
        credential = service_account.Credentials.from_service_account_info(credential_json)
        credential = credential.with_scopes(["https://www.googleapis.com/auth/cloud-platform"])
        gemini_client = genai.Client(
            vertexai=True,
            project=credential_json["project_id"],
            location="asia-northeast1",
            credentials=credential,
        )
        return GeminiTextEmbeddingProvider(model.name, model.dimension, gemini_client)
    if model.provider == "ollama":
        if ollama_url is None:
            raise ValueError("Ollama URL is required")
        ollama_client = httpx.Client(base_url=ollama_url)
        return OllamaTextEmbeddingProvider(model.name, model.dimension, ollama_client)
    if model.provider == "openai":
        openai_client = OpenAI(
            api_key=llm_settings.openai_embedding_api_key or llm_settings.openai_api_key,
            base_url=llm_settings.openai_embedding_base_url or llm_settings.openai_base_url or None,
        )
        return OpenAITextEmbeddingProvider(model.name, model.dimension, openai_client)
    msg = f"Unsupported provider: {model.provider}"
    raise ValueError(msg)


@dataclass
class SymptomPayload:
    content: str
    panel: str
    date: str
    field: str | None = None

    @property
    def title(self):
        return (
            f"Symptom: {self.panel} - {self.date} - {self.field}"
            if self.field
            else f"Symptom: {self.panel} - {self.date}"
        )


class VectorSearch:
    def __init__(
        self,
        qdrant_client: QdrantClient,
        embedding_provider: BaseEmbeddingProvider,
        tenant_id: str,
        flow_id: str,
        greptime_client: SyncGreptimeClient,
        retention: int = 60,
        greptime_logs_enabled: bool = False,
        loki_url: str | None = None,
        greptime_table: str | None = None,
    ):
        self.embedding_provider = embedding_provider
        self.qdrant_client = qdrant_client
        self.tenant_id = tenant_id
        self.flow_id = flow_id
        self.retention = retention
        self.greptime_logs_enabled = greptime_logs_enabled
        self.greptime_client = greptime_client
        self.greptime_table = greptime_table
        self.loki_url = loki_url

    @property
    def _advisory_collection(self):
        return f"t_{self.tenant_id}_past_advisories"

    @property
    def _logs_collection(self):
        return f"t_{self.tenant_id}_past_symptomatic_logs"

    def _ensure_collection(self, collection_name: str):
        if not self.qdrant_client.collection_exists(collection_name):
            self.qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=models.VectorParams(
                    size=self.embedding_provider.dimension,
                    distance=models.Distance.COSINE,
                ),
            )
        return collection_name

    def save_symptom(self, symptom: SymptomPayload):
        record_id = str(uuid.uuid4())
        embedding = self.embedding_provider.embed_content(
            symptom.content,
            task_type="RETRIEVAL_DOCUMENT",
            title=symptom.title,
        )
        payload = asdict(symptom)
        payload["flow_id"] = self.flow_id
        self.qdrant_client.upsert(
            wait=True,
            collection_name=self._ensure_collection(self._advisory_collection),
            points=[PointStruct(id=record_id, vector=embedding, payload=payload)],
        )
        sleep(1)

    def save_logs(self, logs: str, current_date: str, *, seq_ids: list[str] | None = None):
        record_id = str(uuid.uuid4())
        embedding = self.embedding_provider.embed_content(
            logs,
            task_type="RETRIEVAL_DOCUMENT",
            title="Symptomatic Logs",
        )
        payload: dict = {"date": current_date, "flow_id": self.flow_id, "logs": logs}
        if seq_ids:
            payload["seq_ids"] = seq_ids
        self.qdrant_client.upsert(
            wait=True,
            collection_name=self._ensure_collection(self._logs_collection),
            points=[PointStruct(id=record_id, vector=embedding, payload=payload)],
        )
        sleep(1)

    def search_symptoms(self, symptom: str, date: str):
        embedding = self.embedding_provider.embed_content(symptom, task_type="RETRIEVAL_QUERY")
        results = self.qdrant_client.query_points(
            collection_name=self._ensure_collection(self._advisory_collection),
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="date",
                        range=models.DatetimeRange(
                            gt=None,
                            gte=None,
                            lt=datetime.fromisoformat(date.replace("Z", "+00:00")),
                        ),
                    ),
                    models.FieldCondition(
                        key="flow_id",
                        match=models.MatchValue(value=self.flow_id),
                    ),
                ],
            ),
            query=embedding,
            limit=10,
            with_payload=True,
        ).points
        return [
            SymptomPayload(
                content=result.payload.get("content", "") if result.payload else "",
                panel=result.payload.get("panel", "") if result.payload else "",
                date=result.payload.get("date", "") if result.payload else "",
                field=result.payload.get("field") if result.payload else None,
            )
            for result in results
        ]

    @retry(wait=wait_fixed(5), stop=stop_after_attempt(3))
    def search_logs(self, logs: list[str], date: str):
        embeddings = self.embedding_provider.embed_content(logs, task_type="RETRIEVAL_QUERY")

        docs: dict[str, str] = {}

        for embedding in embeddings:
            results = self.qdrant_client.query_points(
                collection_name=self._ensure_collection(self._logs_collection),
                query=embedding,
                limit=5,
                with_payload=True,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="date",
                            range=models.DatetimeRange(
                                gt=None,
                                gte=None,
                                lt=datetime.fromisoformat(date.replace("Z", "+00:00")),
                            ),
                        ),
                        models.FieldCondition(
                            key="flow_id",
                            match=models.MatchValue(value=self.flow_id),
                        ),
                    ],
                ),
            ).points
            for result in results:
                if result.id not in docs and result.payload:
                    original_logs = result.payload.get("logs", "")
                    if original_logs:
                        docs[str(result.id)] = original_logs
                    else:
                        seq_ids = result.payload.get("seq_ids", [])
                        if not seq_ids:
                            continue
                        date_str = result.payload.get("date", "")
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=ZoneInfo("Asia/Tokyo"))
                        start = date_obj.replace(hour=0, minute=0, second=0, microsecond=0)
                        end = date_obj + timedelta(days=1)
                        if self.greptime_logs_enabled and self.greptime_table:
                            res = retrieve_original_logs_from_greptime(
                                greptime_client=self.greptime_client,
                                greptime_table=self.greptime_table,
                                flow_id=self.flow_id,
                                start=pendulum.instance(start),
                                end=pendulum.instance(end),
                                log_seq_ids=seq_ids,
                            )
                            original_logs = "\n".join([log["line"] for log in res])
                        else:
                            if self.loki_url is None:
                                raise ValueError("loki_url is required for log retrieval")
                            res = retrieve_original_logs_from_loki(
                                loki_url=self.loki_url,
                                tenant_id=self.tenant_id,
                                flow_id=self.flow_id,
                                start=pendulum.instance(start),
                                end=pendulum.instance(end),
                                log_seq_ids=seq_ids,
                            )
                            original_logs = "\n".join([log for t, log in res[0]["values"]])
                        docs[str(result.id)] = original_logs
        return list(docs.values())
