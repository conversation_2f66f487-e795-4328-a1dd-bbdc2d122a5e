#!/bin/bash

export HOSTED_S3_BUCKET="ffwd-prophet-models" # "ffwd-services"
export HOSTED_S3_URL="http://localhost:9000"
export HOSTED_S3_USER=""
export HOSTED_S3_PASS=""
export MIMIR_PROXY_URL="http://localhost:9090"
export MIMIR_DISTRIBUTOR_URL="http://localhost:3101"

tenant_id='4476c7c1-0de5-4c77-becc-c274ab375010'
prophecy_id='5d15c57c-5f5a-4a08-9874-18dd3accd599'
mimir_query='{_reserved_flow_id="6af2289f-e0ea-473e-8a38-d9e500cb6a13"}'

one_day=$((60*60*24))
now=`date +%s`
past=$(( $now - $one_day*30))
future=$(( $now + $one_day*7))

cd ../..
python3 src prophecy train --tenant-id $tenant_id --prophecy-id $prophecy_id --mimir-query $mimir_query --start $past --end $now --tenant-country Australia
python3 src prophecy predict --tenant-id $tenant_id --prophecy-id $prophecy_id --start $now --end $future
